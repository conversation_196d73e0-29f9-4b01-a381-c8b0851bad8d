/**
 * Container Component
 * 
 * A responsive container component that centers content and provides consistent padding.
 */
import React from 'react';
import { cn } from '@/lib/utils';

interface ContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

export const Container: React.FC<ContainerProps> = ({ 
  children, 
  className, 
  ...props 
}) => {
  return (
    <div 
      className={cn(
        "container mx-auto px-4 sm:px-6 lg:px-8 py-8", 
        className
      )} 
      {...props}
    >
      {children}
    </div>
  );
};
