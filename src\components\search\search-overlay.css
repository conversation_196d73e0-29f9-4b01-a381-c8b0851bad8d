/* Custom styles for search overlay */

.search-overlay {
  background-color: rgba(255, 255, 255, 1);
}

.search-overlay-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 60px;
  background-color: white;
  position: relative;
  z-index: 10;
}

.search-overlay-content {
  padding: 16px;
  background-color: #f9f9f9;
  min-height: calc(100vh - 60px);
}

.search-results-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 16px;
}

.search-suggestion-item {
  transition: all 0.2s ease;
  border-radius: 0;
  padding: 12px 16px;
  margin: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  height: auto;
  display: flex;
  align-items: center;
}

.search-suggestion-item:last-child {
  border-bottom: none;
}

.search-suggestion-item:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.search-suggestion-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  margin-right: 16px;
}

.search-suggestion-text {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  font-size: 15px;
  color: #333;
}

.search-suggestion-badge {
  font-size: 12px;
  padding: 4px 10px;
  border-radius: 16px;
  background-color: #f0f0f0;
  color: #666;
  margin-left: 12px;
  font-weight: 400;
  white-space: nowrap;
  min-width: 60px;
  text-align: center;
}

.search-section-title {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  margin: 0;
  padding: 16px 16px 12px 16px;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Improve search input styling */
.search-input {
  border-radius: 24px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  padding: 10px 16px 10px 40px;
  font-size: 16px; /* Prevents iOS zoom */
  background-color: white;
  transition: all 0.2s ease;
  height: 44px; /* Better touch target */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  width: 100%;
  outline: none;
}

.search-input-container {
  position: relative;
  width: 100%;
}

.search-input:focus {
  background-color: white;
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.search-input-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 0, 0, 0.4);
  width: 18px;
  height: 18px;
}

/* Clock and trending icons */
.clock-icon-wrapper,
.trending-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  min-width: 32px;
  border-radius: 50%;
  margin-right: 16px;
}

.clock-icon-wrapper {
  background-color: rgba(100, 100, 255, 0.1);
  border: 1px solid rgba(100, 100, 255, 0.2);
}

.trending-icon-wrapper {
  background-color: rgba(255, 100, 100, 0.1);
  border: 1px solid rgba(255, 100, 100, 0.2);
}

.clock-icon {
  color: rgba(100, 100, 255, 0.8);
  width: 16px;
  height: 16px;
}

.trending-icon {
  color: rgba(255, 100, 100, 0.8);
  width: 16px;
  height: 16px;
}

/* Search header buttons */
.search-header-button {
  width: 44px;
  height: 44px;
  min-width: 44px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  touch-action: manipulation;
}

.search-header-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.search-header-button:active {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(0.95);
}

.search-header-button svg {
  width: 20px;
  height: 20px;
}
