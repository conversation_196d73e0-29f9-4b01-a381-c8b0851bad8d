import { supabase } from '@/lib/supabase';
import { validateImageFile } from '@/utils/validation';

/**
 * Uploads a profile picture to Supabase Storage
 * @param userId The user ID
 * @param file The file to upload
 * @returns The URL of the uploaded file, or null if upload failed
 */
export const uploadProfilePicture = async (
  userId: string,
  file: File
): Promise<string | null> => {
  try {
    console.log('Starting profile picture upload for user:', userId);

    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      console.error('User not authenticated:', authError);
      throw new Error('You must be logged in to upload a profile picture');
    }

    console.log('User authenticated:', user.id);

    // Validate the file
    const validationError = validateImageFile(file);
    if (validationError) {
      throw new Error(validationError);
    }

    // Create a unique file name with timestamp
    const fileExt = file.name.split('.').pop()?.toLowerCase() || 'jpg';
    const timestamp = Date.now();
    const fileName = `avatar-${timestamp}.${fileExt}`;
    const filePath = `${userId}/${fileName}`;

    console.log('Uploading file to path:', filePath);

    // First, try to remove any existing avatar files for this user
    try {
      const { data: existingFiles } = await supabase.storage
        .from('profile-pictures')
        .list(userId);

      if (existingFiles && existingFiles.length > 0) {
        const filesToRemove = existingFiles.map(file => `${userId}/${file.name}`);
        await supabase.storage
          .from('profile-pictures')
          .remove(filesToRemove);
        console.log('Removed existing avatar files');
      }
    } catch (cleanupError) {
      console.warn('Could not clean up existing files:', cleanupError);
      // Continue with upload even if cleanup fails
    }

    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('profile-pictures')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true,
        contentType: file.type
      });

    if (error) {
      console.error('Storage upload error:', error);
      throw new Error(`Upload failed: ${error.message}`);
    }

    console.log('File uploaded successfully:', data);

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('profile-pictures')
      .getPublicUrl(filePath);

    console.log('Generated public URL:', publicUrl);

    if (!publicUrl) {
      throw new Error('Failed to generate public URL for uploaded file');
    }

    return publicUrl;
  } catch (error: any) {
    console.error('Error uploading profile picture:', error);
    throw error; // Re-throw to let the calling component handle it
  }
};

/**
 * Deletes a profile picture from Supabase Storage
 * @param url The URL of the file to delete
 * @returns True if deletion was successful, false otherwise
 */
export const deleteProfilePicture = async (url: string): Promise<boolean> => {
  try {
    // Extract the file path from the URL
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    const bucketName = pathParts[2]; // Assuming URL format is /storage/v1/object/public/bucket-name/file-path
    const filePath = pathParts.slice(3).join('/');

    // Delete the file
    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting profile picture:', error);
    return false;
  }
};
