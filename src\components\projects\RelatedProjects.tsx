import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight, Calendar, IndianRupee } from 'lucide-react';
import { CustomProject } from '@/services/customProjectService';
import ProjectImageCarousel from './ProjectImageCarousel';

interface RelatedProjectsProps {
  currentProjectId: string;
  projects: CustomProject[];
  category?: string;
}

const RelatedProjects: React.FC<RelatedProjectsProps> = ({ 
  currentProjectId, 
  projects, 
  category 
}) => {
  // Filter out the current project and limit to 3 projects
  const relatedProjects = projects
    .filter(project => project.id !== currentProjectId)
    .filter(project => category ? project.category === category : true)
    .slice(0, 3);

  if (relatedProjects.length === 0) {
    return null;
  }

  return (
    <div className="mt-12 pt-8 border-t border-badhees-100">
      <h2 className="text-2xl font-bold text-badhees-800 mb-6">
        {category 
          ? `More ${category.charAt(0).toUpperCase() + category.slice(1)} Projects` 
          : 'Explore More Projects'}
      </h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
        {relatedProjects.map((project) => (
          <Link
            key={project.id}
            to={`/completed-projects/detail/${project.id}`}
            className="block group"
          >
            <div className="bg-white rounded-xl overflow-hidden shadow-md border border-badhees-100 hover:shadow-lg transition-all duration-300 h-full flex flex-col group-hover:border-badhees-accent">
              {/* Fixed height container for consistent image sizing */}
              <div className="relative w-full h-[180px] md:h-[200px] overflow-hidden bg-badhees-100">
                {Array.isArray(project.image_urls) && project.image_urls.length > 1 ? (
                  <ProjectImageCarousel images={project.image_urls} projectName={project.name} />
                ) : (
                  <img
                    src={Array.isArray(project.image_urls) && project.image_urls.length > 0 ?
                      project.image_urls[0] :
                      (typeof project.image_urls === 'string' ? project.image_urls : '/placeholder-image.jpg')}
                    alt={project.name}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                    onError={(e) => {
                      console.log('Image load error, using placeholder');
                      e.currentTarget.src = '/placeholder-image.jpg';
                    }}
                    loading="lazy"
                  />
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
              <div className="p-3 md:p-4 flex-1 flex flex-col">
                <h3 className="text-base md:text-lg font-semibold text-badhees-800 mb-1 md:mb-2 group-hover:text-badhees-accent transition-colors">
                  {project.name}
                </h3>
                <p className="text-xs md:text-sm text-badhees-600 line-clamp-2 mb-2 md:mb-3 flex-1">
                  {project.description}
                </p>
                <div className="flex flex-wrap gap-2 md:gap-4 text-xs md:text-sm text-badhees-600 mb-2 md:mb-3 border-t border-badhees-100 pt-2 md:pt-3">
                  {project.budget && (
                    <div className="flex items-center">
                      <IndianRupee className="h-3 w-3 md:h-4 md:w-4 mr-1 text-badhees-accent" />
                      <span>₹{project.budget.toLocaleString('en-IN')}</span>
                    </div>
                  )}
                  {project.completion_date && (
                    <div className="flex items-center ml-auto">
                      <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-1 text-badhees-accent" />
                      <span>{new Date(project.completion_date).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
                <div className="flex justify-end">
                  <span className="inline-flex items-center text-xs md:text-sm text-badhees-accent font-medium group-hover:translate-x-1 transition-transform">
                    View Details <ArrowRight className="h-3 w-3 md:h-4 md:w-4 ml-1" />
                  </span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default RelatedProjects;
