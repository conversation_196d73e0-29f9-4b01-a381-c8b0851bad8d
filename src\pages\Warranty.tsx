import React from 'react';
import ResponsiveLayout from '@/components/layout/ResponsiveLayout';
import PageContainer from '@/components/layout/PageContainer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, CheckCircle, XCircle, FileText, HelpCircle } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

const Warranty = () => {
  return (
    <ResponsiveLayout>
      <div className="py-12 md:py-16 lg:py-20 bg-badhees-50">
        <PageContainer>
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl md:text-4xl font-bold text-badhees-800 mb-6">Warranty Information</h1>
            <p className="text-badhees-600 mb-8">
              We stand behind the quality of our products. Our warranty covers manufacturing defects in materials and workmanship for the specified period from the date of delivery.
            </p>

            <div className="space-y-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="mr-2 h-5 w-5 text-badhees-accent" />
                    Warranty Coverage
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <h3 className="font-semibold text-badhees-800 mb-3">Standard Furniture Warranty</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-badhees-50 p-4 rounded-lg">
                        <p className="font-medium text-badhees-800 mb-2">Ready-Made Furniture</p>
                        <p className="text-badhees-600">1-year limited warranty against manufacturing defects</p>
                      </div>
                      <div className="bg-badhees-50 p-4 rounded-lg">
                        <p className="font-medium text-badhees-800 mb-2">Custom Furniture</p>
                        <p className="text-badhees-600">2-year limited warranty against manufacturing defects</p>
                      </div>
                      <div className="bg-badhees-50 p-4 rounded-lg">
                        <p className="font-medium text-badhees-800 mb-2">Upholstery</p>
                        <p className="text-badhees-600">1-year warranty on fabric and 2 years on frame</p>
                      </div>
                      <div className="bg-badhees-50 p-4 rounded-lg">
                        <p className="font-medium text-badhees-800 mb-2">Mattresses</p>
                        <p className="text-badhees-600">5-year warranty against manufacturing defects</p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="font-semibold text-badhees-800 mb-3">What's Covered</h3>
                    <ul className="space-y-2">
                      {[
                        "Structural defects in frames, joints, and supports",
                        "Defective hardware, zippers, and mechanical components",
                        "Excessive fading or discoloration under normal use",
                        "Warping or cracking not caused by environmental factors",
                        "Defects in upholstery materials and stitching"
                      ].map((item, index) => (
                        <li key={index} className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-badhees-600">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="font-semibold text-badhees-800 mb-3">What's Not Covered</h3>
                    <ul className="space-y-2">
                      {[
                        "Normal wear and tear from regular use",
                        "Damage resulting from improper use, abuse, or accidents",
                        "Damage from exposure to extreme temperatures or humidity",
                        "Changes in wood color due to aging or exposure to light",
                        "Damage from improper cleaning or maintenance",
                        "Variations in natural materials such as wood grain or leather texture",
                        "Damage during transportation not reported within 48 hours of delivery"
                      ].map((item, index) => (
                        <li key={index} className="flex items-start">
                          <XCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-badhees-600">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <FileText className="mr-2 h-5 w-5 text-badhees-accent" />
                    Warranty Claim Process
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ol className="list-decimal list-inside space-y-4 text-badhees-600">
                    <li className="pl-2">
                      <span className="font-medium text-badhees-800">Document the Issue</span>
                      <p className="mt-1 ml-6">Take clear photos of the defect or damage. Prepare your order number and delivery date information.</p>
                    </li>
                    <li className="pl-2">
                      <span className="font-medium text-badhees-800">Contact Customer Service</span>
                      <p className="mt-1 ml-6">Reach out to our customer service team via email or phone with your documentation and order details.</p>
                    </li>
                    <li className="pl-2">
                      <span className="font-medium text-badhees-800">Evaluation</span>
                      <p className="mt-1 ml-6">Our team will review your claim and may request additional information or schedule an inspection if necessary.</p>
                    </li>
                    <li className="pl-2">
                      <span className="font-medium text-badhees-800">Resolution</span>
                      <p className="mt-1 ml-6">If your claim is approved, we will repair, replace, or provide a store credit for the defective item, at our discretion.</p>
                    </li>
                  </ol>

                  <div className="mt-6 bg-badhees-50 p-4 rounded-lg">
                    <p className="font-medium text-badhees-800">Important Note:</p>
                    <p className="text-badhees-600 mt-1">
                      Warranty claims must be submitted within the warranty period. Please retain your purchase receipt or order confirmation as proof of purchase date.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <HelpCircle className="mr-2 h-5 w-5 text-badhees-accent" />
                    Frequently Asked Warranty Questions
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium text-badhees-800">Is the warranty transferable?</h3>
                    <p className="text-badhees-600 mt-1">
                      No, our warranty is non-transferable and extends only to the original purchaser of the product.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-badhees-800">Do I need to register my product for warranty coverage?</h3>
                    <p className="text-badhees-600 mt-1">
                      No registration is required. Your order information in our system serves as your warranty registration.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-badhees-800">What if my item is no longer available for replacement?</h3>
                    <p className="text-badhees-600 mt-1">
                      If the exact item is no longer available, we will offer a comparable replacement or a store credit of equal value.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-badhees-800">Are there any costs associated with warranty service?</h3>
                    <p className="text-badhees-600 mt-1">
                      Repairs or replacements under warranty are provided at no cost. However, if an inspection is required and no defect is found, a service fee may apply.
                    </p>
                  </div>

                  <div>
                    <h3 className="font-medium text-badhees-800">Does the warranty cover floor models or clearance items?</h3>
                    <p className="text-badhees-600 mt-1">
                      Floor models and clearance items may have modified warranty terms, which will be specified at the time of purchase.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="mt-12 bg-white p-6 rounded-lg shadow-sm border border-badhees-100">
              <h2 className="text-xl font-semibold text-badhees-800 mb-4">Extended Warranty Options</h2>
              <p className="text-badhees-600 mb-4">
                For additional peace of mind, we offer extended warranty plans that provide coverage beyond the standard warranty period. These plans can be purchased at the time of your furniture purchase.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
                <div className="bg-badhees-50 p-4 rounded-lg">
                  <p className="font-medium text-badhees-800 mb-2">3-Year Extended Protection</p>
                  <p className="text-badhees-600">Extends coverage to a total of 3-4 years, depending on the product</p>
                </div>
                <div className="bg-badhees-50 p-4 rounded-lg">
                  <p className="font-medium text-badhees-800 mb-2">5-Year Extended Protection</p>
                  <p className="text-badhees-600">Extends coverage to a total of 5-6 years, depending on the product</p>
                </div>
              </div>
              <p className="text-badhees-600 mt-4">
                Contact our customer service team for more information about extended warranty options and pricing.
              </p>
            </div>

            <div className="mt-8 bg-white p-6 rounded-lg shadow-sm border border-badhees-100">
              <h2 className="text-xl font-semibold text-badhees-800 mb-4">Contact Warranty Support</h2>
              <p className="text-badhees-600 mb-4">
                If you have questions about your warranty or need to file a claim, our warranty support team is here to help.
              </p>
              <p className="text-badhees-600">
                Email: <EMAIL><br />
                Phone: 9108344363, 8197705438<br />
                Hours: Monday to Friday, 9:00 AM to 6:00 PM IST
              </p>
            </div>
          </div>
        </PageContainer>
      </div>
    </ResponsiveLayout>
  );
};

export default Warranty;
