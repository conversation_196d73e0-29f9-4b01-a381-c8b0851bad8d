import React from 'react';
import { NetworkStatus } from './network-status';

interface OfflineIndicatorProps {
  className?: string;
}

/**
 * Component that displays an offline indicator when the user loses internet connection
 * Uses the enhanced NetworkStatus component
 */
const OfflineIndicator: React.FC<OfflineIndicatorProps> = ({ className }) => {
  return <NetworkStatus className={className} showOfflineOnly={true} />;
};

export default OfflineIndicator;
