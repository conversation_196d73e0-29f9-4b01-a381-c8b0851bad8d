/* Custom styles for search input */

.search-input-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  height: 48px;
  padding: 12px 16px 12px 44px;
  font-size: 16px;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 24px;
  background-color: white;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  outline: none;
}

.search-input:focus {
  border-color: rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.search-input::placeholder {
  color: #999;
  font-size: 15px;
}

.search-input-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #999;
  z-index: 10;
}

.search-clear-button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  color: #999;
  cursor: pointer;
  z-index: 10;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.search-clear-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #666;
}

.search-submit-button {
  position: absolute;
  right: 0;
  top: 0;
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #6366f1;
  border: none;
  color: white;
  cursor: pointer;
  border-radius: 0 24px 24px 0;
  transition: all 0.2s ease;
}

.search-submit-button:hover {
  background-color: #4f46e5;
}

.search-submit-button:disabled {
  background-color: #c7d2fe;
  cursor: not-allowed;
}

.search-loading-indicator {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: #999;
  z-index: 10;
}
