
import { useEffect, useState } from 'react';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import SEOHead from "@/components/seo/SEOHead";
import { Button } from '@/components/ui/button';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import UnifiedCategoryCard from '@/components/shop/UnifiedCategoryCard';
import { generateBreadcrumbSchema } from "@/utils/structuredData";

interface CategoryData {
  id: string;
  name: string;
  description: string;
  image: string;
}

// Define categories outside the component to prevent recreation on each render
const categoriesData: CategoryData[] = [
    {
      id: "living-room",
      name: "Living Room Furniture",
      description: "Create the perfect living space with our stylish and comfortable living room furniture.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productliving%20(1).avif"
    },
    {
      id: "bedroom",
      name: "Bedroom Furniture",
      description: "Transform your bedroom into a peaceful retreat with our beautiful furniture pieces.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productbed%20(1).avif"
    },
    {
      id: "dining-room",
      name: "Dining Room Furniture",
      description: "Create memorable dining experiences with our elegant dining furniture collection.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productdining%20(1).avif"
    },
    {
      id: "home-office",
      name: "Home Office Furniture",
      description: "Set up a productive and comfortable workspace with our home office solutions.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productoffice%20(1).avif"
    },
    {
      id: "storage",
      name: "Storage Solutions",
      description: "Keep your space organized with our functional and aesthetically pleasing storage furniture.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productstorage%20(1).avif"
    },
    {
      id: "kids-nursery",
      name: "Kids & Nursery Furniture",
      description: "Create safe and playful spaces for children with our kids and nursery furniture.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productkids%20(1).avif"
    },
    {
      id: "vibe-decor",
      name: "Vibe & Decor",
      description: "Enhance your space with our stylish outdoor furniture, distinctive accent pieces, and beautiful decorative accessories.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productvibe%20(1).avif"
    },
    {
      id: "custom-specialty",
      name: "Custom & Specialty Furniture",
      description: "Find unique and personalized furniture pieces that match your specific needs.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Productcardimages/Productcustom%20(1).avif"
    }
  ];

const Shop = () => {
  const [categories, setCategories] = useState<CategoryData[]>(categoriesData);

  useEffect(() => {
    window.scrollTo(0, 0);
    setCategories(categoriesData);
  }, []);

  // Generate breadcrumb structured data
  const breadcrumbData = generateBreadcrumbSchema([
    { name: 'Home', url: 'https://www.thebadhees.com/' },
    { name: 'Shop', url: 'https://www.thebadhees.com/shop' }
  ]);

  return (
    <div className="min-h-screen">
      <SEOHead
        title="Shop by Category - Premium Furniture Collection | The Badhees"
        description="Browse our extensive collection of handcrafted furniture and home accessories. Shop by category including living room, bedroom, dining room, office furniture and more."
        keywords="furniture shop, furniture categories, living room furniture, bedroom furniture, dining room furniture, home office furniture, storage solutions, kids furniture, home decor"
        url="/shop"
        type="website"
        structuredData={breadcrumbData}
      />
      <Navbar />

      <div className="pt-20 md:pt-28 pb-8 md:pb-16">
        {/* Hero Section */}
        <div className="bg-badhees-50 py-8 md:py-16">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            {/* Breadcrumb */}
            <Breadcrumb className="mb-3 md:mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Shop</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <div className="max-w-3xl mx-auto text-center">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-badhees-800 mb-3 md:mb-6 animate-fade-in">
                Shop by Category
              </h1>
              <p className="text-base md:text-lg text-badhees-600 animate-fade-in animate-delay-100">
                Browse our extensive collection of furniture and home accessories, categorized for easy navigation.
                Find everything you need to make your house a home.
              </p>
            </div>
          </div>
        </div>

        {/* Categories Grid */}
        <div className="py-8 md:py-16">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
            {categories.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
                {categories.map((category, index) => (
                  <UnifiedCategoryCard
                    key={category.id}
                    category={category}
                    index={index}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-8 md:py-12">
                <h2 className="text-lg md:text-xl font-medium text-badhees-700 mb-3 md:mb-4">No categories found</h2>
                <p className="text-sm md:text-base text-badhees-600 mb-4 md:mb-6">There are no product categories available at the moment.</p>
                <Button onClick={() => window.location.reload()}>
                  Refresh Page
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default Shop;
