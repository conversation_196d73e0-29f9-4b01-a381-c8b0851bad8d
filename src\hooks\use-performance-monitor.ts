import { useEffect, useRef } from 'react';
import { useTabVisibility } from './use-optimized-render';

/**
 * Hook for monitoring performance metrics
 * @param componentName - Name of the component for tracking
 */
export function usePerformanceMonitor(componentName: string) {
  const { isVisible, wasHidden, resetHiddenState } = useTabVisibility();
  const mountTime = useRef(Date.now());
  const lastVisibilityChange = useRef(Date.now());

  useEffect(() => {
    if (isVisible && wasHidden) {
      const now = Date.now();
      const timeSinceHidden = now - lastVisibilityChange.current;
      
      // Log performance metrics in development
      if (import.meta.env.DEV) {
        console.log(`[Performance] ${componentName} became visible after ${timeSinceHidden}ms`);
      }
      
      resetHiddenState();
    }
    
    lastVisibilityChange.current = Date.now();
  }, [isVisible, wasHidden, componentName, resetHiddenState]);

  useEffect(() => {
    // Log component mount time in development
    if (import.meta.env.DEV) {
      console.log(`[Performance] ${componentName} mounted in ${Date.now() - mountTime.current}ms`);
    }
  }, [componentName]);

  return {
    isVisible,
    wasHidden,
  };
}

/**
 * Hook for measuring render performance
 * @param componentName - Name of the component
 */
export function useRenderPerformance(componentName: string) {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(Date.now());

  useEffect(() => {
    renderCount.current += 1;
    const now = Date.now();
    const timeSinceLastRender = now - lastRenderTime.current;
    
    if (import.meta.env.DEV && renderCount.current > 1) {
      console.log(`[Render] ${componentName} render #${renderCount.current} (${timeSinceLastRender}ms since last)`);
    }
    
    lastRenderTime.current = now;
  });

  return {
    renderCount: renderCount.current,
  };
}
