
import { useEffect, useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from '@/components/ui/button';
import { useCart } from '@/context/SupabaseCartContext';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Order, createOrder, getUserOrders } from '@/services/orderService';
import { Loader2, Star, Eye } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { PurchasableProduct, getUserPurchasableProducts } from '@/services/productReviewsService';
import ReviewModal from '@/components/orders/ReviewModal';
import PullToRefresh from '@/components/ui/pull-to-refresh';

const Orders = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isPlacingOrder, setIsPlacingOrder] = useState(false);
  const [purchasableProducts, setPurchasableProducts] = useState<PurchasableProduct[]>([]);
  const [isLoadingReviewable, setIsLoadingReviewable] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<{id: string, name: string} | null>(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const { cartItems, subtotal, clearCart } = useCart();
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();

  // Fetch orders from the database - optimized for performance
  const fetchUserOrders = useCallback(async () => {
    if (!user) return;

    setIsLoading(true);
    try {
      const userOrders = await getUserOrders(user.id);
      setOrders(userOrders);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast({
        title: 'Error',
        description: 'Failed to load your orders. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Fetch purchasable products for reviews
  const fetchPurchasableProducts = useCallback(async () => {
    if (!user) return;

    setIsLoadingReviewable(true);
    try {
      const products = await getUserPurchasableProducts(user.id);
      setPurchasableProducts(products);
    } catch (error) {
      console.error('Error fetching purchasable products:', error);
    } finally {
      setIsLoadingReviewable(false);
    }
  }, [user]);

  // Refresh function for pull-to-refresh
  const handleRefresh = useCallback(async () => {
    await Promise.all([
      fetchUserOrders(),
      fetchPurchasableProducts()
    ]);
    toast({
      title: 'Refreshed',
      description: 'Your orders have been updated.',
    });
  }, [fetchUserOrders, fetchPurchasableProducts]);

  // Fetch user orders from the database
  useEffect(() => {
    window.scrollTo(0, 0);

    if (isAuthenticated && user) {
      fetchUserOrders();
      fetchPurchasableProducts();
    }
  }, [isAuthenticated, user, fetchUserOrders, fetchPurchasableProducts]);

  // Place a new order
  const handlePlaceOrder = async () => {
    if (cartItems.length === 0 || !user) return;

    setIsPlacingOrder(true);
    try {
      // Create the order in the database with minimal information
      // In a real app, you would collect shipping address from a form
      const shippingAddress = {
        name: user.displayName || user.name || '',
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: ''
      };

      // Create the order in the database
      const newOrder = await createOrder(
        user.id,
        cartItems,
        subtotal,
        'Cash on Delivery', // Default payment method
        shippingAddress
      );

      if (newOrder) {
        // Refresh orders list
        fetchUserOrders();
        // Clear the cart after placing the order
        clearCart();

        toast({
          title: 'Order Placed',
          description: 'Your order has been placed successfully!',
        });
      }
    } catch (error) {
      console.error('Error placing order:', error);
      toast({
        title: 'Error',
        description: 'Failed to place your order. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsPlacingOrder(false);
    }
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Please Login</h1>
            <p className="text-badhees-600 mb-6 text-center">
              You need to be logged in to view your orders.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <Link to="/login?redirect=/orders">Login</Link>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />

      <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
        <div className="pt-28 pb-16">
          <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
          <h1 className="text-3xl font-bold text-badhees-800 mb-8">Your Orders</h1>

          {/* Cart items ready for checkout */}
          {cartItems.length > 0 && (
            <div className="mb-12 p-6 border rounded-lg">
              <h2 className="text-xl font-bold mb-4">Ready to Checkout</h2>

              <div className="space-y-4 mb-6">
                {cartItems.map((item) => (
                  <div key={item.product.id} className="flex items-center justify-between border-b pb-4">
                    <div className="flex items-center">
                      <div className="w-16 h-16 bg-badhees-100 rounded overflow-hidden mr-4">
                        <img src={item.product.image} alt={item.product.name} className="w-full h-full object-cover" />
                      </div>
                      <div>
                        <h3 className="font-medium">{item.product.name}</h3>
                        <p className="text-sm text-badhees-500">Quantity: {item.quantity}</p>
                      </div>
                    </div>
                    <p className="font-medium">₹{((item.product.salePrice || item.product.price) * item.quantity).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</p>
                  </div>
                ))}
              </div>

              <div className="flex justify-between font-bold text-lg border-t pt-4">
                <span>Total:</span>
                <span>₹{subtotal.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
              </div>

              <Button
                onClick={() => navigate('/checkout')}
                className="mt-6 w-full"
              >
                Proceed to Checkout
              </Button>
            </div>
          )}

          {/* Past orders */}
          <div>
            <h2 className="text-xl font-bold mb-4">Order History</h2>

            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-6 w-6 animate-spin text-badhees-600" />
                <span className="ml-3 text-badhees-600">Loading your orders...</span>
              </div>
            ) : orders.length > 0 ? (
              <div className="space-y-6">
                {orders.map((order) => (
                  <div key={order.id} className="border rounded-lg p-6">
                    <div className="flex justify-between items-center mb-4">
                      <div>
                        <h3 className="font-bold">Order #{order.id.substring(0, 8)}</h3>
                        <p className="text-sm text-badhees-500">
                          {new Date(order.created_at).toLocaleDateString()}
                        </p>
                      </div>
                      <div className="flex items-center gap-3">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => navigate(`/orders/${order.id}`)}
                          className="flex items-center gap-1"
                        >
                          <Eye className="h-4 w-4" />
                          View Details
                        </Button>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                          order.status === 'delivered' ? 'bg-green-100 text-green-800' :
                          order.status === 'shipped' ? 'bg-blue-100 text-blue-800' :
                          order.status === 'canceled' ? 'bg-red-100 text-red-800' :
                          'bg-amber-100 text-amber-800'
                        }`}>
                          {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                        </div>
                      </div>
                    </div>

                    <div className="space-y-3 mb-4">
                      {order.order_items && order.order_items.map((item) => (
                        <div key={item.id} className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                          <div className="flex items-center">
                            <div className="w-12 h-12 bg-badhees-100 rounded overflow-hidden mr-3">
                              {item.product?.image_url ? (
                                <img src={item.product.image_url} alt={item.product.name} className="w-full h-full object-cover" />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-badhees-200">
                                  <span className="text-xs text-badhees-600">No image</span>
                                </div>
                              )}
                            </div>
                            <span>{item.product?.name || 'Product'} (x{item.quantity})</span>
                          </div>
                          <div className="flex items-center justify-between sm:justify-end w-full sm:w-auto gap-4">
                            <span>₹{(item.price * item.quantity).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                            {(order.status === 'delivered' || order.status === 'shipped') && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                                onClick={() => {
                                  setSelectedProduct({
                                    id: item.product_id,
                                    name: item.product?.name || 'Product'
                                  });
                                  setIsReviewModalOpen(true);
                                }}
                                disabled={purchasableProducts.some(p =>
                                  p.productId === item.product_id && p.hasReviewed
                                )}
                              >
                                <Star className="h-4 w-4" />
                                {purchasableProducts.some(p =>
                                  p.productId === item.product_id && p.hasReviewed
                                ) ? 'Reviewed' : 'Review'}
                              </Button>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Order Summary with Shipping */}
                    <div className="border-t pt-3 space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-badhees-600">Subtotal:</span>
                        <span>₹{(order.total_amount - (order.shipping_fee || 0)).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                      </div>

                      {order.shipping_fee && order.shipping_fee > 0 && (
                        <div className="flex justify-between text-sm">
                          <span className="text-badhees-600">Shipping:</span>
                          <span>₹{order.shipping_fee.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                        </div>
                      )}

                      {order.is_bangalore_delivery !== null && (
                        <div className="text-xs text-badhees-500">
                          {order.is_bangalore_delivery ? '📍 Bangalore delivery' : '📍 Outside Bangalore delivery'}
                        </div>
                      )}

                      {order.shipping_notes && (
                        <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
                          {order.shipping_notes}
                        </div>
                      )}

                      <div className="flex justify-between font-bold text-lg border-t pt-2">
                        <span>Total:</span>
                        <span>₹{order.total_amount.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12 bg-badhees-50 rounded-lg">
                <p className="text-badhees-600 mb-4">You haven't placed any orders yet.</p>
                <Button asChild>
                  <Link to="/products">Start Shopping</Link>
                </Button>
              </div>
            )}
          </div>

          {cartItems.length === 0 && orders.length === 0 && (
            <div className="text-center py-16 bg-badhees-50 rounded-lg">
              <h2 className="text-2xl font-bold text-badhees-800 mb-4">No Orders Yet</h2>
              <p className="text-badhees-600 mb-6">Your order history is empty. Start shopping to place your first order!</p>
              <Button asChild>
                <Link to="/products">Browse Products</Link>
              </Button>
            </div>
          )}
          </div>
        </div>
      </PullToRefresh>

      <Footer />

      {/* Review Modal */}
      {selectedProduct && (
        <ReviewModal
          isOpen={isReviewModalOpen}
          onClose={() => setIsReviewModalOpen(false)}
          productId={selectedProduct.id}
          productName={selectedProduct.name}
          onReviewSubmitted={() => {
            // Refresh the purchasable products list
            fetchPurchasableProducts();
          }}
        />
      )}
    </div>
  );
};

export default Orders;
