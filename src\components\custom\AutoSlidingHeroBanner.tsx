import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import PageContainer from "@/components/layout/PageContainer";

interface ProjectCategory {
  id: string;
  title: string;
  description: string;
  image: string;
}

interface AutoSlidingHeroBannerProps {
  categories: ProjectCategory[];
  autoSlideInterval?: number; // in milliseconds
}

const AutoSlidingHeroBanner: React.FC<AutoSlidingHeroBannerProps> = ({
  categories,
  autoSlideInterval = 3000
}) => {
  const navigate = useNavigate();
  const [activeIndex, setActiveIndex] = useState(0);
  const [isPaused, setIsPaused] = useState(false);

  // Navigate to the completed projects page for the selected category
  const navigateToCategory = useCallback((categoryId: string) => {
    navigate(`/completed-projects/${categoryId}`);
  }, [navigate]);

  // Function to go to the next slide
  const nextSlide = useCallback(() => {
    setActiveIndex((prevIndex) =>
      prevIndex === categories.length - 1 ? 0 : prevIndex + 1
    );
  }, [categories.length]);

  // Function to go to the previous slide
  const prevSlide = useCallback(() => {
    setActiveIndex((prevIndex) =>
      prevIndex === 0 ? categories.length - 1 : prevIndex - 1
    );
  }, [categories.length]);

  // Function to go to a specific slide
  const goToSlide = useCallback((index: number) => {
    setActiveIndex(index);
  }, []);

  // Set up auto-sliding
  useEffect(() => {
    if (isPaused || categories.length <= 1) return;

    console.log('Setting up auto-sliding interval for hero banner');

    const interval = setInterval(() => {
      console.log('Auto-sliding to next slide');
      nextSlide();
    }, autoSlideInterval);

    return () => {
      console.log('Cleaning up auto-sliding interval');
      clearInterval(interval);
    };
  }, [nextSlide, isPaused, categories.length, autoSlideInterval]);

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="w-full h-[45vh] md:h-[50vh] relative overflow-hidden">
      {/* Slides */}
      <div className="relative w-full h-full">
        {categories.map((category, index) => (
          <div
            key={category.id}
            className={`absolute inset-0 w-full h-full transition-opacity duration-1000 ${
              index === activeIndex ? 'opacity-100 z-10' : 'opacity-0 z-0'
            }`}
          >
            <div
              className="relative w-full h-full cursor-pointer group"
              onClick={() => navigateToCategory(category.id)}
              onMouseEnter={() => setIsPaused(true)}
              onMouseLeave={() => setIsPaused(false)}
              role="button"
              aria-label={`View ${category.title} projects`}
            >
              <img
                src={category.image}
                alt={category.title}
                className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
              />
              <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent flex items-center">
                <PageContainer>
                  <div className="max-w-lg">
                    <h1 className="text-2xl md:text-4xl font-bold text-white mb-2 md:mb-4 animate-fade-in">
                      {category.title}
                    </h1>
                    <p className="text-sm md:text-lg text-white/90 mb-3 md:mb-6 line-clamp-2 md:line-clamp-none animate-fade-in animate-delay-100">
                      {category.description}
                    </p>
                    <div className="animate-fade-in animate-delay-200 flex items-center text-white/90 text-xs md:text-sm group-hover:text-white transition-colors duration-300">
                      <span>View all {category.title.toLowerCase()} projects</span>
                      <ArrowRight className="ml-1 md:ml-2 h-3 w-3 md:h-4 md:w-4 transition-transform duration-300 group-hover:translate-x-1" />
                    </div>
                  </div>
                </PageContainer>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AutoSlidingHeroBanner;
