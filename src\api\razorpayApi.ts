/**
 * Razorpay API Endpoints
 * 
 * This module provides API endpoints for Razorpay payment integration.
 */
import express from 'express';
import { createRazorpayOrder, verifyPaymentSignature, updatePaymentRecord } from '@/services/payment/razorpayService';
import { supabase } from '@/lib/supabase';
import { updateOrderStatus } from '@/services/orderService';

const router = express.Router();

/**
 * Create a new Razorpay order
 * 
 * POST /api/razorpay/create-order
 * 
 * Request body:
 * {
 *   amount: number,
 *   currency: string,
 *   receipt: string,
 *   notes: object
 * }
 * 
 * Response:
 * {
 *   id: string,
 *   amount: number,
 *   currency: string,
 *   receipt: string,
 *   ...other order details
 * }
 */
router.post('/create-order', async (req, res) => {
  try {
    const { amount, currency = 'INR', receipt, notes = {} } = req.body;

    if (!amount || !receipt) {
      return res.status(400).json({ 
        success: false, 
        error: 'Amount and receipt are required' 
      });
    }

    const order = await createRazorpayOrder(amount, currency, receipt, notes);

    return res.status(200).json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error creating Razorpay order:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to create Razorpay order'
    });
  }
});

/**
 * Verify Razorpay payment
 * 
 * POST /api/razorpay/verify-payment
 * 
 * Request body:
 * {
 *   razorpay_order_id: string,
 *   razorpay_payment_id: string,
 *   razorpay_signature: string,
 *   order_id: string
 * }
 * 
 * Response:
 * {
 *   success: boolean,
 *   message: string
 * }
 */
router.post('/verify-payment', async (req, res) => {
  try {
    const { 
      razorpay_order_id, 
      razorpay_payment_id, 
      razorpay_signature,
      order_id 
    } = req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      return res.status(400).json({ 
        success: false, 
        error: 'Payment verification failed: Missing required parameters' 
      });
    }

    // Verify payment signature
    const isValid = verifyPaymentSignature(
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature
    );

    if (!isValid) {
      await updatePaymentRecord(razorpay_order_id, {
        status: 'failed',
        error_description: 'Invalid payment signature'
      });

      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Invalid signature'
      });
    }

    // Update payment record
    await updatePaymentRecord(razorpay_order_id, {
      razorpay_payment_id,
      status: 'captured',
      method: req.body.method || 'online'
    });

    // Update order status if order_id is provided
    if (order_id) {
      await updateOrderStatus(order_id, 'paid');
    }

    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully'
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to verify payment'
    });
  }
});

/**
 * Get payment status
 * 
 * GET /api/razorpay/payment-status/:orderId
 * 
 * Response:
 * {
 *   success: boolean,
 *   data: {
 *     status: string,
 *     ...other payment details
 *   }
 * }
 */
router.get('/payment-status/:orderId', async (req, res) => {
  try {
    const { orderId } = req.params;

    const { data, error } = await supabase
      .from('razorpay_payments')
      .select('*')
      .eq('razorpay_order_id', orderId)
      .single();

    if (error) {
      return res.status(404).json({
        success: false,
        error: 'Payment record not found'
      });
    }

    return res.status(200).json({
      success: true,
      data
    });
  } catch (error) {
    console.error('Error fetching payment status:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to fetch payment status'
    });
  }
});

export default router;
