
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, Loader2, Plus, X } from "lucide-react";
import { ProjectType } from "@/types/project";
import { CustomProject } from "@/services/customProjectService";
import { uploadProjectImage } from "@/services/projectImageService";
import { toast } from "@/hooks/use-toast";

interface CompletedProjectFormProps {
  initialData?: ProjectType | CustomProject;
  onSave: (data: any) => void;
  onCancel: () => void;
}

type FormValues = {
  name: string;
  description: string;
  category: string;
  budget: number;
  completionDate: Date;
  clientName?: string;
  location?: string;
  materials?: string;
  images: string[];
  video_url?: string;
  status: 'draft' | 'published' | 'archived';
};

const CompletedProjectForm: React.FC<CompletedProjectFormProps> = ({
  initialData,
  onSave,
  onCancel,
}) => {
  // Handle different property names between ProjectType and CustomProject
  const getInitialValues = () => {
    if (!initialData) {
      return {
        name: "",
        description: "",
        category: "",
        budget: 0,
        completionDate: new Date(),
        clientName: "",
        location: "",
        materials: "",
        images: [],
        video_url: "",
        status: "draft",
      };
    }

    // Check if it's a CustomProject (has completion_date) or ProjectType (has completionDate)
    const isCustomProject = 'completion_date' in initialData;

    return {
      name: initialData.name,
      description: initialData.description || "",
      category: initialData.category || "",
      budget: initialData.budget || 0,
      completionDate: isCustomProject
        ? (initialData as CustomProject).completion_date ? new Date((initialData as CustomProject).completion_date!) : new Date()
        : (initialData as ProjectType).completionDate ? new Date((initialData as ProjectType).completionDate) : new Date(),
      clientName: isCustomProject ? (initialData as CustomProject).client_name || "" : (initialData as ProjectType).clientName || "",
      location: initialData.location || "",
      materials: isCustomProject ? (initialData as CustomProject).materials_used || "" : (initialData as ProjectType).materials || "",
      images: isCustomProject
        ? (() => {
            const imageUrls = (initialData as CustomProject).image_urls;
            console.log('Initial image_urls:', imageUrls);

            // Handle string format (single URL)
            if (typeof imageUrls === 'string' && imageUrls && (imageUrls as string).trim() !== '') {
              console.log('Converting string image URL to array');
              return [imageUrls];
            }

            // Handle array format
            if (Array.isArray(imageUrls)) {
              console.log('Using array of image URLs');
              return imageUrls.filter((url: string) => url && url.trim() !== '');
            }

            // Default to empty array
            console.log('No valid images found, using empty array');
            return [];
          })()
        : (initialData as ProjectType).images || [],
      video_url: isCustomProject ? (initialData as CustomProject).video_url || "" : (initialData as ProjectType).videoUrl || "",
      status: isCustomProject
        ? (((initialData as CustomProject).status as 'draft' | 'published' | 'archived') || "draft")
        : (((initialData as ProjectType).status === 'active' ? 'published' : (initialData as ProjectType).status) as 'draft' | 'published' | 'archived'),
    };
  };

  const form = useForm<FormValues>({
    defaultValues: getInitialValues(),
  });

  const [isUploading, setIsUploading] = useState(false);

  // Handle file uploads
  const handleFileUpload = async (file: File): Promise<string | null> => {
    setIsUploading(true);
    try {
      const url = await uploadProjectImage(file);
      if (!url) {
        toast({
          title: "Upload failed",
          description: "Failed to upload image. Please try again.",
          variant: "destructive"
        });
      }
      return url;
    } catch (error) {
      console.error("Error uploading image:", error);
      toast({
        title: "Upload failed",
        description: "An error occurred while uploading the image",
        variant: "destructive"
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleAddImage = () => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = 'image/*';
    fileInput.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const url = await handleFileUpload(file);
        if (url) {
          console.log('Image uploaded successfully:', url);
          const currentImages = form.getValues().images || [];
          console.log('Current images before adding:', currentImages);

          // Ensure currentImages is an array
          const imagesArray = Array.isArray(currentImages) ? currentImages : [];

          // Add the new image URL
          const updatedImages = [...imagesArray, url];
          console.log('Updated images after adding:', updatedImages);

          form.setValue("images", updatedImages);
        }
      }
    };
    fileInput.click();
  };

  const handleRemoveImage = (index: number) => {
    const currentImages = form.getValues().images;
    console.log('Current images before removing:', currentImages);

    // Ensure currentImages is an array
    const imagesArray = Array.isArray(currentImages) ? currentImages : [];

    // Remove the image at the specified index
    const updatedImages = [...imagesArray];
    updatedImages.splice(index, 1);

    console.log('Updated images after removing:', updatedImages);
    form.setValue("images", updatedImages);
  };

  const onSubmit = (data: FormValues) => {
    // Filter out empty image URLs
    const filteredImages = data.images.filter((img: string) => img && img.trim() !== '');

    console.log('Submitting form with images:', filteredImages);

    // Format the data for Supabase
    const projectData = {
      name: data.name,
      description: data.description,
      category: data.category,
      budget: Number(data.budget),
      completion_date: format(data.completionDate, "yyyy-MM-dd"),
      client_name: data.clientName,
      location: data.location,
      materials_used: data.materials,
      image_urls: filteredImages.length > 0 ? filteredImages : null, // Ensure we don't save empty arrays
      video_url: data.video_url,
      status: data.status,
    };

    console.log('Project data being saved:', projectData);

    onSave(projectData);
  };

  // Category options
  const categoryOptions = [
    { value: 'kitchen', label: 'Kitchen Interiors' },
    { value: 'bedroom', label: 'Bedroom Designs' },
    { value: 'living', label: 'Living Room Interiors' },
    { value: 'office', label: 'Office Furniture & Others' },
    { value: 'bathroom', label: 'Bathroom Designs' },
    { value: 'dining', label: 'Dining Room' },
    { value: 'outdoor', label: 'Outdoor Spaces' },
    { value: 'commercial', label: 'Commercial Spaces' },
    { value: 'other', label: 'Other' }
  ];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="name"
            rules={{ required: "Project name is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Project Name</FormLabel>
                <FormControl>
                  <Input placeholder="E.g., Modern Kitchen Renovation" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            rules={{ required: "Category is required" }}
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {categoryOptions.map(option => (
                      <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="description"
          rules={{ required: "Description is required" }}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Project Description</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the project, its goals, and the solutions implemented"
                  rows={5}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="budget"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Budget</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    min="0"
                    placeholder="Project budget"
                    {...field}
                    onChange={(e) => field.onChange(Number(e.target.value))}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="completionDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Completion Date</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={field.value}
                      onSelect={field.onChange}
                      disabled={(date) =>
                        date > new Date() || date < new Date("1900-01-01")
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="clientName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Client Name (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="E.g., Smith Family" {...field} />
                </FormControl>
                <FormDescription>
                  Leave blank to keep anonymous
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="location"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Location (Optional)</FormLabel>
                <FormControl>
                  <Input placeholder="E.g., San Francisco, CA" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="materials"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Materials Used (Optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="E.g., Marble countertops, custom oak cabinets, brass fixtures"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <FormLabel>Project Images</FormLabel>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddImage}
              disabled={isUploading}
            >
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Image
                </>
              )}
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
            {(() => {
              const images = form.getValues().images;
              console.log('Rendering images in form:', images);

              // Ensure images is an array
              const imagesArray = Array.isArray(images) ? images : [];
              console.log('Images array for rendering:', imagesArray);

              if (imagesArray.length === 0) {
                return (
                  <div className="col-span-full text-center p-8 border border-dashed border-gray-300 rounded-md">
                    <p className="text-gray-500">No images added yet. Click "Add Image" to upload.</p>
                  </div>
                );
              }

              return imagesArray.map((url, index) => (
                <div key={index} className="relative group rounded-md overflow-hidden border border-gray-200">
                  <img
                    src={url}
                    alt={`Project image ${index + 1}`}
                    className="w-full h-40 object-cover"
                  />
                  <Button
                    type="button"
                    variant="destructive"
                    size="sm"
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => handleRemoveImage(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ));
            })()}
          </div>

          <FormDescription>
            Upload images for the project. At least one image is recommended.
          </FormDescription>
        </div>

        <FormField
          control={form.control}
          name="video_url"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Video URL (Optional)</FormLabel>
              <FormControl>
                <Input placeholder="E.g., https://www.youtube.com/embed/..." {...field} />
              </FormControl>
              <FormDescription>
                Enter a YouTube embed URL for a project video
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="status"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Project Status</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>
                Draft projects won't be visible to customers. Archived projects are hidden but not deleted.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-3">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit">Save Project</Button>
        </div>
      </form>
    </Form>
  );
};

export default CompletedProjectForm;
