import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Loader2, Star } from 'lucide-react';
import { addProductReview, hasUserReviewedProduct } from '@/services/productReviewsService';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useQueryClient } from '@tanstack/react-query';
import { productDetailKeys } from '@/hooks/useProductDetail';
import { productKeys } from '@/hooks/useProducts';

interface ReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
  onReviewSubmitted: () => void;
}

const ReviewModal: React.FC<ReviewModalProps> = ({
  isOpen,
  onClose,
  productId,
  productName,
  onReviewSubmitted
}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [rating, setRating] = useState(5);
  const [title, setTitle] = useState('');
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasAlreadyReviewed, setHasAlreadyReviewed] = useState(false);

  // Check if user has already reviewed this product when modal opens
  useEffect(() => {
    const checkExistingReview = async () => {
      if (isOpen && user && productId) {
        try {
          const hasReviewed = await hasUserReviewedProduct(productId, user.id);
          setHasAlreadyReviewed(hasReviewed);

          if (hasReviewed) {
            toast({
              title: 'Already reviewed',
              description: 'You have already submitted a review for this product.',
              variant: 'default'
            });
          }
        } catch (error) {
          console.error('Error checking existing review:', error);
        }
      }
    };

    checkExistingReview();
  }, [isOpen, user, productId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate inputs
      if (rating < 1 || rating > 5) {
        throw new Error('Rating must be between 1 and 5 stars');
      }

      if (!comment.trim()) {
        throw new Error('Please provide a review comment');
      }

      // Check if user has already reviewed this product
      if (user && await hasUserReviewedProduct(productId, user.id)) {
        toast({
          title: 'Already reviewed',
          description: 'You have already submitted a review for this product.',
          variant: 'destructive'
        });
        onClose();
        return;
      }

      // Log the parameters being sent
      console.log('Submitting review with params:', { productId, rating, comment, title });

      const result = await addProductReview(productId, rating, comment, title);
      console.log('Review submission result:', result);

      if (result) {
        toast({
          title: 'Review submitted',
          description: 'Thank you for your feedback!'
        });

        // Invalidate all relevant queries to ensure UI is updated
        console.log('Invalidating queries to refresh UI');

        try {
          // Invalidate the reviews query
          queryClient.invalidateQueries({
            queryKey: productDetailKeys.reviews(productId),
          });

          // Invalidate all products query to update ratings in product listings
          queryClient.invalidateQueries({
            queryKey: productKeys.lists(),
          });

          // Invalidate the specific product query
          queryClient.invalidateQueries({
            queryKey: productKeys.detail(productId),
          });

          // Invalidate featured products and other product-related queries
          queryClient.invalidateQueries({
            queryKey: ['featuredProducts'],
          });

          queryClient.invalidateQueries({
            queryKey: ['categoryProducts'],
          });

          // Force refetch the product detail
          queryClient.refetchQueries({
            queryKey: productKeys.detail(productId),
          });
        } catch (error) {
          console.error('Error invalidating queries:', error);
        }

        // Call the callback and close the modal
        onReviewSubmitted();
        onClose();
      } else {
        // If result is null, there was an issue but no exception was thrown
        toast({
          title: 'Review not submitted',
          description: 'There was an issue submitting your review. You may have already reviewed this product.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error submitting review:', error);

      // Show a more specific error message if available
      const errorMessage = error instanceof Error
        ? error.message
        : 'Failed to submit your review. Please try again.';

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Review {productName}</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="rating">Rating</Label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((value) => (
                <Button
                  key={value}
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setRating(value)}
                  className="p-0 h-auto focus:outline-none"
                  aria-label={`Rate ${value} out of 5 stars`}
                >
                  <Star
                    className={`h-6 w-6 ${
                      value <= rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
                    }`}
                  />
                </Button>
              ))}
              <span className="ml-2 text-sm text-gray-500">
                {rating} out of 5 stars
              </span>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Review Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Summarize your experience"
              maxLength={100}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="comment">Your Review</Label>
            <Textarea
              id="comment"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Share your experience with this product"
              rows={4}
              maxLength={1000}
            />
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit Review'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ReviewModal;
