
import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

interface EditorHeaderProps {
  totalProducts: number;
  onAddProductClick: () => void;
}

const EditorHeader: React.FC<EditorHeaderProps> = ({
  totalProducts,
  onAddProductClick
}) => {
  return (
    <>
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Admin Panel</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold text-badhees-800">
            Admin Panel
          </h1>
          <p className="text-muted-foreground">
            Manage your products. {totalProducts} products total
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button 
            variant="outline"
            onClick={() => window.open('/products', '_blank')}
          >
            View Store
          </Button>
        </div>
      </div>
    </>
  );
};

export default EditorHeader;
