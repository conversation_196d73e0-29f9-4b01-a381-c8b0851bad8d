/* 
  Spacing System
  This file defines consistent spacing variables to be used throughout the application
  to maintain visual hierarchy and consistency.
*/

:root {
  /* Base spacing unit (4px) */
  --spacing-unit: 0.25rem;
  
  /* Spacing scale */
  --spacing-0: 0;
  --spacing-1: calc(var(--spacing-unit) * 1);  /* 4px */
  --spacing-2: calc(var(--spacing-unit) * 2);  /* 8px */
  --spacing-3: calc(var(--spacing-unit) * 3);  /* 12px */
  --spacing-4: calc(var(--spacing-unit) * 4);  /* 16px */
  --spacing-5: calc(var(--spacing-unit) * 5);  /* 20px */
  --spacing-6: calc(var(--spacing-unit) * 6);  /* 24px */
  --spacing-8: calc(var(--spacing-unit) * 8);  /* 32px */
  --spacing-10: calc(var(--spacing-unit) * 10); /* 40px */
  --spacing-12: calc(var(--spacing-unit) * 12); /* 48px */
  --spacing-16: calc(var(--spacing-unit) * 16); /* 64px */
  --spacing-20: calc(var(--spacing-unit) * 20); /* 80px */
  --spacing-24: calc(var(--spacing-unit) * 24); /* 96px */
  --spacing-32: calc(var(--spacing-unit) * 32); /* 128px */
  
  /* Section spacing */
  --section-spacing-sm: var(--spacing-8);
  --section-spacing-md: var(--spacing-16);
  --section-spacing-lg: var(--spacing-24);
  --section-spacing-xl: var(--spacing-32);
  
  /* Component spacing */
  --component-spacing-xs: var(--spacing-2);
  --component-spacing-sm: var(--spacing-4);
  --component-spacing-md: var(--spacing-6);
  --component-spacing-lg: var(--spacing-8);
  
  /* Mobile-optimized touch target spacing */
  --touch-target-spacing: var(--spacing-3); /* 12px minimum */
  --touch-target-size: var(--spacing-11); /* 44px minimum size for touch targets */
  
  /* Grid gaps */
  --grid-gap-sm: var(--spacing-4);
  --grid-gap-md: var(--spacing-6);
  --grid-gap-lg: var(--spacing-8);
  
  /* Form element spacing */
  --form-element-spacing: var(--spacing-4);
  --form-group-spacing: var(--spacing-6);
}

/* Mobile-specific adjustments */
@media (max-width: 640px) {
  :root {
    /* Increase touch target spacing on mobile */
    --touch-target-spacing: var(--spacing-4); /* 16px on mobile */
    
    /* Adjust section spacing for mobile */
    --section-spacing-sm: var(--spacing-6);
    --section-spacing-md: var(--spacing-12);
    --section-spacing-lg: var(--spacing-16);
    --section-spacing-xl: var(--spacing-20);
  }
}

/* Utility classes for spacing */
.space-y-xs > * + * { margin-top: var(--component-spacing-xs); }
.space-y-sm > * + * { margin-top: var(--component-spacing-sm); }
.space-y-md > * + * { margin-top: var(--component-spacing-md); }
.space-y-lg > * + * { margin-top: var(--component-spacing-lg); }

.space-x-xs > * + * { margin-left: var(--component-spacing-xs); }
.space-x-sm > * + * { margin-left: var(--component-spacing-sm); }
.space-x-md > * + * { margin-left: var(--component-spacing-md); }
.space-x-lg > * + * { margin-left: var(--component-spacing-lg); }

/* Touch target classes */
.touch-target {
  min-height: var(--touch-target-size);
  min-width: var(--touch-target-size);
  padding: var(--touch-target-spacing);
}

/* Section padding classes */
.section-padding-sm { padding: var(--section-spacing-sm); }
.section-padding-md { padding: var(--section-spacing-md); }
.section-padding-lg { padding: var(--section-spacing-lg); }
.section-padding-xl { padding: var(--section-spacing-xl); }

/* Component padding classes */
.component-padding-xs { padding: var(--component-spacing-xs); }
.component-padding-sm { padding: var(--component-spacing-sm); }
.component-padding-md { padding: var(--component-spacing-md); }
.component-padding-lg { padding: var(--component-spacing-lg); }

/* Grid gap classes */
.grid-gap-sm { gap: var(--grid-gap-sm); }
.grid-gap-md { gap: var(--grid-gap-md); }
.grid-gap-lg { gap: var(--grid-gap-lg); }
