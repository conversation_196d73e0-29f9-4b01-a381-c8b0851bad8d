import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { ArrowLeft, Calculator, DollarSign, Loader2, Save } from 'lucide-react';
import { format, parseISO, startOfMonth, endOfMonth } from 'date-fns';
import { useAuth } from '@/context/SupabaseAuthContext';
import { 
  useEmployees, 
  useCalculatePayroll,
  useCreatePayroll,
  usePayrollByEmployee,
  usePayrollByPeriod,
  useUpdatePayrollStatus
} from '@/hooks/useEmployeeManagement';
import { PayrollCalculationInput, PayrollCalculationResult, Payroll } from '@/services/employee/types';

const AdminPayroll = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated, isAdmin, user } = useAuth();
  
  // Get employee ID from URL if provided
  const employeeIdFromUrl = searchParams.get('employee');
  
  // State for date selection
  const today = new Date();
  const firstDayOfMonth = format(startOfMonth(today), 'yyyy-MM-dd');
  const lastDayOfMonth = format(endOfMonth(today), 'yyyy-MM-dd');
  
  const [activeTab, setActiveTab] = useState('calculate');
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(employeeIdFromUrl || '');
  
  // State for payroll calculation
  const [periodStartDate, setPeriodStartDate] = useState(firstDayOfMonth);
  const [periodEndDate, setPeriodEndDate] = useState(lastDayOfMonth);
  const [includeOvertime, setIncludeOvertime] = useState(true);
  const [additionalBonuses, setAdditionalBonuses] = useState('0');
  const [additionalDeductions, setAdditionalDeductions] = useState('0');
  const [payrollNotes, setPayrollNotes] = useState('');
  
  // State for calculated payroll
  const [calculationResult, setCalculationResult] = useState<PayrollCalculationResult | null>(null);
  
  // State for payment dialog
  const [paymentDialogOpen, setPaymentDialogOpen] = useState(false);
  const [payrollToProcess, setPayrollToProcess] = useState<string | null>(null);
  const [paymentDate, setPaymentDate] = useState(format(today, 'yyyy-MM-dd'));

  // Fetch data
  const { data: employees, isLoading: isLoadingEmployees } = useEmployees();
  const { data: employeePayroll, isLoading: isLoadingEmployeePayroll } = 
    usePayrollByEmployee(selectedEmployeeId);
  const { data: periodPayroll, isLoading: isLoadingPeriodPayroll } = 
    usePayrollByPeriod(periodStartDate, periodEndDate);
  
  const calculatePayrollMutation = useCalculatePayroll();
  const createPayrollMutation = useCreatePayroll();
  const updatePayrollStatusMutation = useUpdatePayrollStatus();

  // Check authentication and admin status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/employees/payroll');
    } else if (!isAdmin()) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Reset calculation result when inputs change
  useEffect(() => {
    setCalculationResult(null);
  }, [selectedEmployeeId, periodStartDate, periodEndDate, includeOvertime, additionalBonuses, additionalDeductions]);

  // Handle payroll calculation
  const handleCalculatePayroll = async () => {
    if (!selectedEmployeeId) return;
    
    try {
      const input: PayrollCalculationInput = {
        employee_id: selectedEmployeeId,
        period_start_date: periodStartDate,
        period_end_date: periodEndDate,
        include_approved_overtime: includeOvertime,
        additional_bonuses: parseFloat(additionalBonuses) || 0,
        additional_deductions: parseFloat(additionalDeductions) || 0
      };
      
      const result = await calculatePayrollMutation.mutateAsync(input);
      setCalculationResult(result);
    } catch (error) {
      console.error('Error calculating payroll:', error);
    }
  };

  // Handle payroll creation
  const handleCreatePayroll = async () => {
    if (!calculationResult || !user?.id) return;
    
    try {
      const payroll: Omit<Payroll, 'id' | 'created_at' | 'updated_at'> = {
        employee_id: calculationResult.employee_id,
        period_start_date: calculationResult.period_start_date,
        period_end_date: calculationResult.period_end_date,
        days_worked: calculationResult.days_worked,
        overtime_hours: calculationResult.overtime_hours,
        base_pay: calculationResult.base_pay,
        overtime_pay: calculationResult.overtime_pay,
        bonuses: calculationResult.bonuses,
        deductions: calculationResult.deductions,
        total_pay: calculationResult.total_pay,
        payment_status: 'pending',
        notes: payrollNotes,
        created_by: user.id
      };
      
      await createPayrollMutation.mutateAsync(payroll);
      setCalculationResult(null);
      setPayrollNotes('');
    } catch (error) {
      console.error('Error creating payroll:', error);
    }
  };

  // Handle payment dialog open
  const handlePaymentClick = (id: string) => {
    setPayrollToProcess(id);
    setPaymentDialogOpen(true);
  };

  // Handle payment confirmation
  const confirmPayment = async () => {
    if (payrollToProcess) {
      try {
        await updatePayrollStatusMutation.mutateAsync({
          id: payrollToProcess,
          status: 'paid',
          paymentDate
        });
        setPaymentDialogOpen(false);
        setPayrollToProcess(null);
      } catch (error) {
        console.error('Error processing payment:', error);
      }
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-6">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin/employees')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Employees
              </Button>
              <h1 className="text-2xl font-bold">Payroll Management</h1>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="calculate">Calculate Payroll</TabsTrigger>
                <TabsTrigger value="employee">Employee History</TabsTrigger>
                <TabsTrigger value="period">Period Payroll</TabsTrigger>
              </TabsList>

              {/* Calculate Payroll Tab */}
              <TabsContent value="calculate">
                <Card>
                  <CardHeader>
                    <CardTitle>Calculate Employee Payroll</CardTitle>
                    <CardDescription>
                      Calculate payroll for an employee for a specific period.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <Label htmlFor="employee-select">Select Employee</Label>
                        <Select
                          value={selectedEmployeeId}
                          onValueChange={setSelectedEmployeeId}
                        >
                          <SelectTrigger id="employee-select">
                            <SelectValue placeholder="Select an employee" />
                          </SelectTrigger>
                          <SelectContent>
                            {employees?.map(employee => (
                              <SelectItem key={employee.id} value={employee.id}>
                                {employee.first_name} {employee.last_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-center space-x-2 pt-6">
                        <Checkbox
                          id="include-overtime"
                          checked={includeOvertime}
                          onCheckedChange={(checked) => setIncludeOvertime(checked as boolean)}
                        />
                        <Label htmlFor="include-overtime">Include approved overtime</Label>
                      </div>
                      <div>
                        <Label htmlFor="period-start">Period Start Date</Label>
                        <Input
                          id="period-start"
                          type="date"
                          value={periodStartDate}
                          onChange={(e) => setPeriodStartDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="period-end">Period End Date</Label>
                        <Input
                          id="period-end"
                          type="date"
                          value={periodEndDate}
                          onChange={(e) => setPeriodEndDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="additional-bonuses">Additional Bonuses</Label>
                        <Input
                          id="additional-bonuses"
                          type="number"
                          min="0"
                          step="0.01"
                          value={additionalBonuses}
                          onChange={(e) => setAdditionalBonuses(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="additional-deductions">Additional Deductions</Label>
                        <Input
                          id="additional-deductions"
                          type="number"
                          min="0"
                          step="0.01"
                          value={additionalDeductions}
                          onChange={(e) => setAdditionalDeductions(e.target.value)}
                        />
                      </div>
                    </div>

                    <div className="flex justify-end mb-6">
                      <Button 
                        onClick={handleCalculatePayroll}
                        disabled={
                          calculatePayrollMutation.isPending || 
                          !selectedEmployeeId ||
                          !periodStartDate ||
                          !periodEndDate
                        }
                      >
                        {calculatePayrollMutation.isPending && (
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        )}
                        <Calculator className="h-4 w-4 mr-2" />
                        Calculate
                      </Button>
                    </div>

                    {calculationResult && (
                      <div className="border rounded-lg p-6 mt-6">
                        <h3 className="text-lg font-semibold mb-4">Payroll Calculation Result</h3>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                          <div>
                            <p className="text-sm text-gray-500">Employee</p>
                            <p className="font-medium">{calculationResult.employee_name}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Period</p>
                            <p className="font-medium">
                              {format(parseISO(calculationResult.period_start_date), 'MMM d, yyyy')} - {format(parseISO(calculationResult.period_end_date), 'MMM d, yyyy')}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Days Worked</p>
                            <p className="font-medium">{calculationResult.days_worked}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Overtime Hours</p>
                            <p className="font-medium">{calculationResult.overtime_hours}</p>
                          </div>
                        </div>

                        <div className="space-y-2 mb-6">
                          <div className="flex justify-between py-1 border-b">
                            <span>Base Pay</span>
                            <span className="font-medium">{formatCurrency(calculationResult.base_pay)}</span>
                          </div>
                          <div className="flex justify-between py-1 border-b">
                            <span>Overtime Pay</span>
                            <span className="font-medium">{formatCurrency(calculationResult.overtime_pay)}</span>
                          </div>
                          <div className="flex justify-between py-1 border-b">
                            <span>Bonuses</span>
                            <span className="font-medium">{formatCurrency(calculationResult.bonuses)}</span>
                          </div>
                          <div className="flex justify-between py-1 border-b">
                            <span>Deductions</span>
                            <span className="font-medium">{formatCurrency(calculationResult.deductions)}</span>
                          </div>
                          <div className="flex justify-between py-2 font-bold">
                            <span>Total Pay</span>
                            <span>{formatCurrency(calculationResult.total_pay)}</span>
                          </div>
                        </div>

                        <div className="mb-6">
                          <Label htmlFor="payroll-notes">Notes</Label>
                          <Textarea
                            id="payroll-notes"
                            value={payrollNotes}
                            onChange={(e) => setPayrollNotes(e.target.value)}
                            placeholder="Add any notes about this payroll"
                            rows={3}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button 
                            onClick={handleCreatePayroll}
                            disabled={createPayrollMutation.isPending}
                          >
                            {createPayrollMutation.isPending && (
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            )}
                            <Save className="h-4 w-4 mr-2" />
                            Save Payroll
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Employee History Tab */}
              <TabsContent value="employee">
                <Card>
                  <CardHeader>
                    <CardTitle>Employee Payroll History</CardTitle>
                    <CardDescription>
                      View payroll history for a specific employee.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="employee-history-select">Select Employee</Label>
                      <Select
                        value={selectedEmployeeId}
                        onValueChange={setSelectedEmployeeId}
                      >
                        <SelectTrigger id="employee-history-select">
                          <SelectValue placeholder="Select an employee" />
                        </SelectTrigger>
                        <SelectContent>
                          {employees?.map(employee => (
                            <SelectItem key={employee.id} value={employee.id}>
                              {employee.first_name} {employee.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {!selectedEmployeeId ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Please select an employee to view payroll history.</p>
                      </div>
                    ) : isLoadingEmployeePayroll ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading payroll data...</span>
                      </div>
                    ) : employeePayroll?.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No payroll records found for this employee.</p>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        {employeePayroll?.map((payroll) => (
                          <Accordion type="single" collapsible key={payroll.id}>
                            <AccordionItem value={payroll.id}>
                              <AccordionTrigger>
                                <div className="flex justify-between w-full pr-4">
                                  <span>
                                    {format(parseISO(payroll.period_start_date), 'MMM d')} - {format(parseISO(payroll.period_end_date), 'MMM d, yyyy')}
                                  </span>
                                  <div className="flex items-center gap-4">
                                    <Badge 
                                      variant="outline"
                                      className={getStatusBadgeColor(payroll.payment_status)}
                                    >
                                      {payroll.payment_status.charAt(0).toUpperCase() + payroll.payment_status.slice(1)}
                                    </Badge>
                                    <span className="font-bold">{formatCurrency(payroll.total_pay)}</span>
                                  </div>
                                </div>
                              </AccordionTrigger>
                              <AccordionContent>
                                <div className="space-y-2 p-4 bg-gray-50 rounded-md">
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                      <p className="text-sm text-gray-500">Days Worked</p>
                                      <p className="font-medium">{payroll.days_worked}</p>
                                    </div>
                                    <div>
                                      <p className="text-sm text-gray-500">Overtime Hours</p>
                                      <p className="font-medium">{payroll.overtime_hours}</p>
                                    </div>
                                    {payroll.payment_date && (
                                      <div>
                                        <p className="text-sm text-gray-500">Payment Date</p>
                                        <p className="font-medium">{format(parseISO(payroll.payment_date), 'MMM d, yyyy')}</p>
                                      </div>
                                    )}
                                  </div>

                                  <div className="space-y-2">
                                    <div className="flex justify-between py-1 border-b">
                                      <span>Base Pay</span>
                                      <span className="font-medium">{formatCurrency(payroll.base_pay)}</span>
                                    </div>
                                    <div className="flex justify-between py-1 border-b">
                                      <span>Overtime Pay</span>
                                      <span className="font-medium">{formatCurrency(payroll.overtime_pay)}</span>
                                    </div>
                                    <div className="flex justify-between py-1 border-b">
                                      <span>Bonuses</span>
                                      <span className="font-medium">{formatCurrency(payroll.bonuses)}</span>
                                    </div>
                                    <div className="flex justify-between py-1 border-b">
                                      <span>Deductions</span>
                                      <span className="font-medium">{formatCurrency(payroll.deductions)}</span>
                                    </div>
                                    <div className="flex justify-between py-2 font-bold">
                                      <span>Total Pay</span>
                                      <span>{formatCurrency(payroll.total_pay)}</span>
                                    </div>
                                  </div>

                                  {payroll.notes && (
                                    <div className="mt-4 pt-4 border-t">
                                      <p className="text-sm text-gray-500">Notes</p>
                                      <p>{payroll.notes}</p>
                                    </div>
                                  )}

                                  {payroll.payment_status === 'pending' && (
                                    <div className="mt-4 pt-4 border-t flex justify-end">
                                      <Button 
                                        onClick={() => handlePaymentClick(payroll.id)}
                                        size="sm"
                                      >
                                        <DollarSign className="h-4 w-4 mr-2" />
                                        Mark as Paid
                                      </Button>
                                    </div>
                                  )}
                                </div>
                              </AccordionContent>
                            </AccordionItem>
                          </Accordion>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Period Payroll Tab */}
              <TabsContent value="period">
                <Card>
                  <CardHeader>
                    <CardTitle>Period Payroll</CardTitle>
                    <CardDescription>
                      View and manage payroll for a specific period.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <Label htmlFor="period-start-date">Period Start Date</Label>
                        <Input
                          id="period-start-date"
                          type="date"
                          value={periodStartDate}
                          onChange={(e) => setPeriodStartDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="period-end-date">Period End Date</Label>
                        <Input
                          id="period-end-date"
                          type="date"
                          value={periodEndDate}
                          onChange={(e) => setPeriodEndDate(e.target.value)}
                        />
                      </div>
                    </div>

                    {isLoadingPeriodPayroll ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading payroll data...</span>
                      </div>
                    ) : !periodPayroll || periodPayroll.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No payroll records found for this period.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Employee</TableHead>
                              <TableHead>Days Worked</TableHead>
                              <TableHead>OT Hours</TableHead>
                              <TableHead>Total Pay</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {periodPayroll.map((payroll) => (
                              <TableRow key={payroll.id}>
                                <TableCell className="font-medium">
                                  {payroll.employee_name}
                                </TableCell>
                                <TableCell>{payroll.days_worked}</TableCell>
                                <TableCell>{payroll.overtime_hours}</TableCell>
                                <TableCell>{formatCurrency(payroll.total_pay)}</TableCell>
                                <TableCell>
                                  <Badge 
                                    variant="outline"
                                    className={getStatusBadgeColor(payroll.payment_status)}
                                  >
                                    {payroll.payment_status.charAt(0).toUpperCase() + payroll.payment_status.slice(1)}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right">
                                  {payroll.payment_status === 'pending' && (
                                    <Button 
                                      size="sm"
                                      onClick={() => handlePaymentClick(payroll.id)}
                                    >
                                      <DollarSign className="h-4 w-4 mr-2" />
                                      Mark as Paid
                                    </Button>
                                  )}
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      <Footer />

      {/* Payment Dialog */}
      <Dialog open={paymentDialogOpen} onOpenChange={setPaymentDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Process Payment</DialogTitle>
            <DialogDescription>
              Mark this payroll as paid and record the payment date.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="payment-date">Payment Date</Label>
            <Input
              id="payment-date"
              type="date"
              value={paymentDate}
              onChange={(e) => setPaymentDate(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPaymentDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={confirmPayment}
              disabled={updatePayrollStatusMutation.isPending}
            >
              {updatePayrollStatusMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              <DollarSign className="h-4 w-4 mr-2" />
              Process Payment
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminPayroll;
