/* Pull to Refresh Component Styles */

.pull-to-refresh-container {
  position: relative;
  overflow: auto;
  transition: transform 0.3s ease-out;
}

.pull-to-refresh-container.is-pulling {
  transition: none;
}

.pull-to-refresh-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 0;
  background-color: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border-bottom: 1px solid rgb(229, 231, 235);
  z-index: 10;
  transform: translateY(-40px);
  transition: transform 0.3s ease-out;
}

.pull-to-refresh-indicator.is-pulling {
  transition: none;
}

.pull-to-refresh-icon {
  height: 1.25rem;
  width: 1.25rem;
}

.pull-to-refresh-icon.is-refreshing {
  animation: spin 1s linear infinite;
}

.pull-to-refresh-content {
  transition: padding-top 0.3s ease-out;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
