
import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent
} from "@/components/ui/chart";
import {
  AreaChart,
  Area,
  BarChart,
  Bar,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { Badge } from "@/components/ui/badge";
import {
  Package,
  ShoppingCart,
  Users,
  ArrowUp,
  BarChart2,
  Pie<PERSON>hart as PieChartIcon,
  IndianRupee,
  Box as BoxIcon
} from 'lucide-react';

import { Loader2 } from "lucide-react";
import { subscribeToRealtimeUpdates, DashboardStats } from '@/services/analyticsService';
import { useDashboardStats } from '@/hooks/useAnalytics';

interface Product {
  id: string;
  name: string;
  price: number;
  salePrice?: number;
  category: string;
  stock: number;
  status?: 'active' | 'draft' | 'deleted';
  image?: string;
}

interface AdminDashboardStatsProps {
  products: Product[];
}

// Custom colors for charts - updated with a more harmonious palette
const COLORS = [
  '#8b5cf6', // Purple (primary)
  '#ec4899', // Pink
  '#06b6d4', // Cyan
  '#84cc16', // Lime
  '#f97316', // Orange
  '#14b8a6', // Teal
  '#6366f1', // Indigo
  '#a855f7', // Violet
  '#d946ef', // Fuchsia
  '#f43f5e', // Rose
];

const AdminDashboardStats: React.FC<AdminDashboardStatsProps> = ({ products }) => {
  const [topProductsSortBy, setTopProductsSortBy] = useState<'revenue' | 'quantity'>('revenue');

  // Use React Query to fetch dashboard stats
  const {
    data: stats,
    isLoading
  } = useDashboardStats(topProductsSortBy);

  // Default stats object for when data is loading
  const dashboardStats: DashboardStats = stats || {
    totalRevenue: 0,
    orderStats: {
      total: 0,
      completed: 0,
      processing: 0,
      pending: 0
    },
    customerStats: {
      total: 0,
      active: 0,
      inactive: 0,
      totalCustomers: 0
    },
    categoryPerformance: [],
    salesTrend: [],
    weeklyPerformance: [],
    topSellingProducts: [],
    totalProducts: 0,
    totalProjects: 0
  };

  useEffect(() => {
    // Subscribe to real-time updates
    const unsubscribe = subscribeToRealtimeUpdates(() => {
      // The query will be invalidated and refetched automatically
    });

    // Cleanup subscription on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <div className="space-y-8">
      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <IndianRupee className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Loader2 className="h-6 w-6 animate-spin text-badhees-accent" />
              ) : (
                `₹${dashboardStats.totalRevenue.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}`
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              +12.5% from last month
            </p>
            <div className="mt-4 h-1 w-full bg-gray-100 rounded-full overflow-hidden">
              <div className="bg-green-500 h-full rounded-full w-3/4"></div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Loader2 className="h-6 w-6 animate-spin text-badhees-accent" />
              ) : (
                dashboardStats.orderStats.total
              )}
            </div>
            <div className="flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              <span>8.2% increase</span>
            </div>
            <div className="flex justify-between mt-4 text-xs text-muted-foreground">
              <div>Completed: {dashboardStats.orderStats.completed}</div>
              <div>Processing: {dashboardStats.orderStats.processing}</div>
              <div>Pending: {dashboardStats.orderStats.pending}</div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Loader2 className="h-6 w-6 animate-spin text-badhees-accent" />
              ) : (
                dashboardStats.customerStats.totalCustomers
              )}
            </div>
            <div className="flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              <span>12.4% new users</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Loader2 className="h-6 w-6 animate-spin text-badhees-accent" />
              ) : (
                dashboardStats.totalProducts
              )}
            </div>
            <div className="mt-4 flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Total Products</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Projects</CardTitle>
            <BoxIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {isLoading ? (
                <Loader2 className="h-6 w-6 animate-spin text-badhees-accent" />
              ) : (
                dashboardStats.totalProjects
              )}
            </div>
            <div className="mt-4 flex items-center justify-between">
              <span className="text-xs text-muted-foreground">Total Projects</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Chart Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Sales Trend</CardTitle>
              <BarChart2 className="h-4 w-4 text-muted-foreground" />
            </div>
            <CardDescription>Monthly sales revenue</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <div className="h-[300px]">
              <ChartContainer
                config={{
                  sales: { color: "#8b5cf6" },
                  forecast: { color: "#a7a7a7" }
                }}
              >
                <AreaChart
                  data={dashboardStats.salesTrend}
                  margin={{ top: 10, right: 20, left: 10, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="name" opacity={0.7} />
                  <YAxis
                    tickFormatter={(value) => `₹${value}`}
                    opacity={0.7}
                  />
                  <ChartTooltip
                    content={
                      <ChartTooltipContent indicator="dot" nameKey="name" />
                    }
                    formatter={(value, name) =>
                      [`₹${Number(value).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}`, name === "sales" ? "Sales" : "Forecast"]
                    }
                  />
                  <Area
                    type="monotone"
                    dataKey="sales"
                    stroke="var(--color-sales)"
                    fill="var(--color-sales)"
                    fillOpacity={0.2}
                    strokeWidth={2}
                    activeDot={{ r: 6 }}
                  />
                  <Area
                    type="monotone"
                    dataKey="forecast"
                    stroke="var(--color-forecast)"
                    fill="var(--color-forecast)"
                    fillOpacity={0.1}
                    strokeWidth={2}
                    strokeDasharray="5 5"
                  />
                </AreaChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Category Performance</CardTitle>
              <PieChartIcon className="h-4 w-4 text-muted-foreground" />
            </div>
            <CardDescription>Sales distribution by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                </div>
              ) : dashboardStats.categoryPerformance.length === 0 ? (
                <div className="flex flex-col justify-center items-center h-full text-muted-foreground">
                  <PieChartIcon className="h-12 w-12 mb-2 opacity-20" />
                  <p>No sales data available</p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={dashboardStats.categoryPerformance}
                      cx="50%"
                      cy="50%"
                      labelLine={true}
                      outerRadius={90}
                      innerRadius={30}
                      fill="#8884d8"
                      dataKey="value"
                      paddingAngle={2}
                      label={false}
                    >
                      {dashboardStats.categoryPerformance.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                          stroke="white"
                          strokeWidth={1}
                        />
                      ))}
                    </Pie>
                    <Legend
                      layout="vertical"
                      verticalAlign="middle"
                      align="right"
                      wrapperStyle={{
                        fontSize: '12px',
                        lineHeight: '20px',
                        padding: '0 8px'
                      }}
                      formatter={(value, entry) => {
                        // Truncate long category names
                        const displayName = value.length > 15 ? `${value.substring(0, 12)}...` : value;
                        return <span className={`text-gray-800 ${entry.payload.value > 0 ? 'font-medium' : 'font-normal'}`}>{displayName}</span>;
                      }}
                    />
                    <Tooltip
                      formatter={(value, name, props) => {
                        // Calculate percentage
                        const total = dashboardStats.categoryPerformance.reduce((sum, item) => sum + item.value, 0);
                        const percent = total > 0 ? ((value / total) * 100).toFixed(1) : '0.0';

                        return [
                          `₹${Number(value).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })} (${percent}%)`,
                          'Revenue'
                        ];
                      }}
                      contentStyle={{
                        backgroundColor: 'white',
                        border: '1px solid #f0f0f0',
                        borderRadius: '4px',
                        padding: '8px',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Weekly Sales & Top Products */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Performance</CardTitle>
            <CardDescription>Daily orders and revenue</CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <div className="h-[300px]">
              <ChartContainer
                config={{
                  orders: { color: "#ec4899" },
                  revenue: { color: "#06b6d4" }
                }}
              >
                <BarChart
                  data={dashboardStats.weeklyPerformance}
                  margin={{ top: 10, right: 20, left: 10, bottom: 10 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                  <XAxis dataKey="name" opacity={0.7} />
                  <YAxis
                    yAxisId="left"
                    orientation="left"
                    opacity={0.7}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    tickFormatter={(value) => `₹${value}`}
                    opacity={0.7}
                  />
                  <ChartTooltip
                    content={
                      <ChartTooltipContent indicator="dot" nameKey="name" />
                    }
                    formatter={(value, name) =>
                      name === "orders"
                        ? [`${value} orders`, "Orders"]
                        : [`₹${Number(value).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}`, "Revenue"]
                    }
                  />
                  <Bar
                    dataKey="orders"
                    fill="var(--color-orders)"
                    yAxisId="left"
                    barSize={20}
                  />
                  <Line
                    type="monotone"
                    dataKey="revenue"
                    stroke="var(--color-revenue)"
                    yAxisId="right"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </BarChart>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle>Top Selling Products</CardTitle>
              <div className="flex items-center space-x-2">
                <button
                  type="button"
                  onClick={() => setTopProductsSortBy('revenue')}
                  className={`px-2 py-1 text-xs rounded-md ${topProductsSortBy === 'revenue'
                    ? 'bg-badhees-100 text-badhees-800 font-medium'
                    : 'text-gray-500 hover:bg-gray-100'}`}
                >
                  By Revenue
                </button>
                <button
                  type="button"
                  onClick={() => setTopProductsSortBy('quantity')}
                  className={`px-2 py-1 text-xs rounded-md ${topProductsSortBy === 'quantity'
                    ? 'bg-badhees-100 text-badhees-800 font-medium'
                    : 'text-gray-500 hover:bg-gray-100'}`}
                >
                  By Quantity
                </button>
              </div>
            </div>
            <CardDescription>
              {topProductsSortBy === 'revenue'
                ? 'Best performing products by revenue'
                : 'Best performing products by quantity sold'}
            </CardDescription>
          </CardHeader>
          <CardContent className="px-2">
            <div className="space-y-4">
              {isLoading ? (
                <div className="flex justify-center py-10">
                  <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                </div>
              ) : dashboardStats.topSellingProducts.length > 0 ? (
                dashboardStats.topSellingProducts.map((product) => (
                <div key={product.id} className="flex items-center">
                  <div className="w-12 h-12 rounded overflow-hidden bg-muted flex-shrink-0">
                    <img
                      src={product.image_url || 'https://placehold.co/100x100?text=Image'}
                      alt={product.name}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = 'https://placehold.co/100x100?text=No+Image';
                      }}
                    />
                  </div>
                  <div className="ml-4 flex-1 min-w-0">
                    <h4 className="text-sm font-medium truncate">{product.name}</h4>
                    <p className="text-xs text-muted-foreground">{product.category}</p>
                  </div>
                  <div className="flex flex-col items-end">
                    {topProductsSortBy === 'revenue' ? (
                      <span className="text-sm font-medium">₹{product.total_sales.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                    ) : (
                      <span className="text-sm font-medium">{product.quantity_sold} units</span>
                    )}
                    <span className="text-xs text-muted-foreground">Top seller</span>
                  </div>
                </div>
              ))
              ) : (
                <div className="text-center py-10 text-muted-foreground">
                  No sales data available
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>


    </div>
  );
};

export default AdminDashboardStats;
