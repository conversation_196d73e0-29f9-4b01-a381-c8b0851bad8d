import React, { useMemo } from 'react';
import { Clock, TrendingUp, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export interface SearchSuggestionsProps {
  /**
   * Popular searches to display
   */
  popularSearches?: { query: string; count: number }[];

  /**
   * Recent searches to display
   */
  recentSearches?: { query: string; timestamp: Date }[];

  /**
   * Auto-complete suggestions based on current input
   */
  autoCompleteSuggestions?: string[];

  /**
   * Callback when a suggestion is selected
   */
  onSuggestionSelect: (query: string) => void;

  /**
   * Current search query
   */
  currentQuery: string;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * A component to display search suggestions, including popular and recent searches
 */
export const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  popularSearches = [],
  recentSearches = [],
  autoCompleteSuggestions = [],
  onSuggestionSelect,
  currentQuery,
  className = "",
}) => {
  // If there are no suggestions and no current query, don't show anything
  if (
    popularSearches.length === 0 &&
    recentSearches.length === 0 &&
    autoCompleteSuggestions.length === 0 &&
    !currentQuery.trim()
  ) {
    return null;
  }

  // Format the date to a relative time (e.g., "2 days ago")
  const formatRelativeTime = (date: Date): string => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

    return date.toLocaleDateString();
  };

  // Remove duplicates between recent and popular searches
  const uniquePopularSearches = useMemo(() => {
    const recentQueries = new Set(recentSearches.map(item => item.query.toLowerCase()));
    return popularSearches.filter(item => !recentQueries.has(item.query.toLowerCase()));
  }, [popularSearches, recentSearches]);

  return (
    <div className={cn("", className)}>
      {/* Auto-complete suggestions based on current input */}
      {currentQuery.trim() && autoCompleteSuggestions.length > 0 && (
        <div className="mb-4">
          <h3 className="search-section-title">Suggestions</h3>
          <div>
            {autoCompleteSuggestions.map((suggestion, index) => (
              <button
                key={`autocomplete-${index}`}
                type="button"
                className="search-suggestion-item"
                onClick={() => onSuggestionSelect(suggestion)}
              >
                <div className="search-suggestion-icon">
                  <Search className="h-3.5 w-3.5 text-badhees-500" />
                </div>
                <span className="search-suggestion-text">{suggestion}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Recent searches */}
      {recentSearches.length > 0 && (
        <div className="mb-4">
          <h3 className="search-section-title">Recent Searches</h3>
          <div>
            {recentSearches.map((item, index) => (
              <button
                key={`recent-${index}`}
                type="button"
                className="search-suggestion-item"
                onClick={() => onSuggestionSelect(item.query)}
              >
                <div className="clock-icon-wrapper">
                  <Clock className="clock-icon" />
                </div>
                <span className="search-suggestion-text">{item.query}</span>
                <span className="search-suggestion-badge">
                  {formatRelativeTime(item.timestamp)}
                </span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Popular searches */}
      {uniquePopularSearches.length > 0 && (
        <div>
          <h3 className="search-section-title">Popular Searches</h3>
          <div>
            {uniquePopularSearches.map((item, index) => (
              <button
                key={`popular-${index}`}
                type="button"
                className="search-suggestion-item"
                onClick={() => onSuggestionSelect(item.query)}
              >
                <div className="trending-icon-wrapper">
                  <TrendingUp className="trending-icon" />
                </div>
                <span className="search-suggestion-text">{item.query}</span>
                <span className="search-suggestion-badge">
                  {item.count} {item.count === 1 ? 'search' : 'searches'}
                </span>
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
