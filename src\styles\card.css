/* Unified Card Styles */
.unified-category-card {
  @apply bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 transition-all duration-300 hover:shadow-md h-full flex flex-col;
}

.unified-image-container {
  @apply relative overflow-hidden bg-badhees-100 h-[220px];
}

.unified-image {
  @apply w-full h-full object-cover transition-transform duration-500 hover:scale-105;
  object-position: center; /* Ensure consistent image positioning */
}

.unified-overlay {
  @apply absolute inset-0 bg-gradient-to-t from-black/60 to-transparent;
}

.unified-title-container {
  @apply absolute bottom-0 left-0 p-4;
}

.unified-title {
  @apply text-lg sm:text-xl md:text-2xl font-bold text-white;
}

.unified-content {
  @apply p-4 sm:p-5 md:p-6 flex flex-col flex-grow;
}

.unified-description {
  @apply text-sm sm:text-base text-badhees-600 mb-4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.unified-button-container {
  @apply mt-auto;
}

/* Project Category Card Styles */
.project-category-card {
  @apply bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 hover:shadow-md transition-all duration-300 flex flex-col h-full;
}

.project-category-image {
  @apply relative overflow-hidden bg-badhees-100 h-[220px];
}

.project-category-image img {
  @apply w-full h-full object-cover transition-transform duration-500 hover:scale-105;
  object-position: center; /* Ensure consistent image positioning */
}

.project-category-content {
  @apply p-4 sm:p-5 md:p-6 flex flex-col flex-grow;
}

.project-category-title {
  @apply text-lg sm:text-xl md:text-2xl font-bold text-badhees-800 mb-2 md:mb-3;
}

.project-category-description {
  @apply text-sm sm:text-base text-badhees-600 mb-4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.project-category-features {
  @apply mb-5 flex-grow;
}

.project-category-features-title {
  @apply text-xs sm:text-sm font-medium text-badhees-800 mb-2 md:mb-3;
}

.project-category-features-list {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3;
}

.project-category-feature-item {
  @apply flex items-center text-badhees-600 text-xs sm:text-sm;
}

.project-category-feature-icon {
  @apply h-3 w-3 sm:h-4 sm:w-4 mr-1.5 sm:mr-2 text-badhees-accent flex-shrink-0;
}

.project-category-button {
  @apply mt-auto;
}

/* Animations - Optimized for performance */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 10px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
  will-change: opacity, transform;
}

/* Animation delays */
.animation-delay-0 {
  animation-delay: 0ms;
}

.animation-delay-50 {
  animation-delay: 50ms;
}

.animation-delay-100 {
  animation-delay: 100ms;
}

.animation-delay-150 {
  animation-delay: 150ms;
}

.animation-delay-200 {
  animation-delay: 200ms;
}

.animation-delay-250 {
  animation-delay: 250ms;
}

.animation-delay-300 {
  animation-delay: 300ms;
}

.animation-delay-350 {
  animation-delay: 350ms;
}

.animation-delay-400 {
  animation-delay: 400ms;
}

.animation-delay-450 {
  animation-delay: 450ms;
}

.animation-delay-500 {
  animation-delay: 500ms;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .category-card-title h2 {
    @apply text-lg;
  }

  .category-card-content {
    @apply p-3;
  }

  .category-card-description {
    @apply text-sm;
  }
}

/* Mobile optimizations for Shop page */
@media (max-width: 640px) {
  /* Reduce image height on mobile */
  .unified-image-container {
    @apply h-[180px];
  }

  /* Reduce padding in content area */
  .unified-content {
    @apply p-3;
  }

  /* Optimize title size */
  .unified-title {
    @apply text-lg;
  }

  /* Reduce description margin */
  .unified-description {
    @apply text-sm mb-3;
  }

  /* Ensure button maintains proper touch target */
  .unified-button-container button {
    @apply min-h-[44px] text-sm;
  }
}
