import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON><PERSON>, Trash2, Eye } from 'lucide-react';
import { Product } from '@/services/editorProductsService';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface AdminProductsTableProps {
  products: Product[];
  onUpdate: (product: Product) => void;
  onDelete: (id: string) => void;
  onBulkAction: (ids: string[], action: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const AdminProductsTable: React.FC<AdminProductsTableProps> = ({
  products,
  onUpdate,
  onDelete,
  onBulkAction,
  currentPage,
  totalPages,
  onPageChange
}) => {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [deleteProductId, setDeleteProductId] = useState<string | null>(null);
  const [bulkDeleteConfirmOpen, setBulkDeleteConfirmOpen] = useState(false);

  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allProductIds = products.map(product => product.id);
      setSelectedProducts(allProductIds);
    } else {
      setSelectedProducts([]);
    }
  };

  const handleStockUpdate = (productId: string, stockChange: number) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      const newStock = Math.max(0, product.stock + stockChange);
      onUpdate({
        ...product,
        stock: newStock
      });
    }
  };

  const handleStatusToggle = (productId: string, checked: boolean) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      onUpdate({
        ...product,
        status: checked ? 'active' : 'draft'
      });
    }
  };

  const handleDeleteConfirm = () => {
    if (deleteProductId) {
      onDelete(deleteProductId);
      setDeleteProductId(null);
    }
    setDeleteConfirmOpen(false);
  };

  const handleBulkDeleteConfirm = () => {
    onBulkAction(selectedProducts, 'delete');
    setSelectedProducts([]);
    setBulkDeleteConfirmOpen(false);
  };

  const handleBulkAction = (action: string) => {
    if (action === 'delete') {
      setBulkDeleteConfirmOpen(true);
    } else {
      onBulkAction(selectedProducts, action);
      setSelectedProducts([]);
    }
  };

  const renderPaginationItems = () => {
    const pages = [];

    if (currentPage > 2) {
      pages.push(
        <PaginationItem key="1">
          <PaginationLink onClick={() => onPageChange(1)}>1</PaginationLink>
        </PaginationItem>
      );

      if (currentPage > 3) {
        pages.push(
          <PaginationItem key="ellipsis1">
            <span className="px-2">...</span>
          </PaginationItem>
        );
      }
    }

    for (let i = Math.max(1, currentPage - 1); i <= Math.min(totalPages, currentPage + 1); i++) {
      pages.push(
        <PaginationItem key={i}>
          <PaginationLink
            isActive={i === currentPage}
            onClick={() => onPageChange(i)}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    if (currentPage < totalPages - 1) {
      if (currentPage < totalPages - 2) {
        pages.push(
          <PaginationItem key="ellipsis2">
            <span className="px-2">...</span>
          </PaginationItem>
        );
      }

      pages.push(
        <PaginationItem key={totalPages}>
          <PaginationLink onClick={() => onPageChange(totalPages)}>
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }

    return pages;
  };

  return (
    <div className="bg-white rounded-md shadow-sm">
      {selectedProducts.length > 0 && (
        <div className="p-4 bg-gray-50 border-b flex items-center">
          <span className="text-sm font-medium mr-4">
            {selectedProducts.length} products selected
          </span>

          <Button
            variant="outline"
            size="sm"
            className="mr-2"
            onClick={() => handleBulkAction('active')}
          >
            Set Active
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="mr-2"
            onClick={() => handleBulkAction('draft')}
          >
            Set Draft
          </Button>

          <Button
            variant="destructive"
            size="sm"
            onClick={() => handleBulkAction('delete')}
          >
            Delete
          </Button>

          <Button
            variant="ghost"
            size="sm"
            className="ml-auto"
            onClick={() => setSelectedProducts([])}
          >
            Cancel
          </Button>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th scope="col" className="py-3 pl-4 pr-3 w-12">
                <Checkbox
                  checked={selectedProducts.length === products.length && products.length > 0}
                  onCheckedChange={(checked) => handleSelectAll(!!checked)}
                />
              </th>
              <th scope="col" className="py-3 pl-3 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-16">
                Image
              </th>
              <th scope="col" className="py-3 pl-3 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Product
              </th>
              <th scope="col" className="py-3 pl-3 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Category
              </th>
              <th scope="col" className="py-3 pl-3 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th scope="col" className="py-3 pl-3 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Stock
              </th>
              <th scope="col" className="py-3 pl-3 pr-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="py-3 pl-3 pr-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {products.length > 0 ? (
              products.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="py-4 pl-4 pr-3 whitespace-nowrap">
                    <Checkbox
                      checked={selectedProducts.includes(product.id)}
                      onCheckedChange={(checked) => handleSelectProduct(product.id, !!checked)}
                    />
                  </td>
                  <td className="py-4 pl-3 pr-3 whitespace-nowrap">
                    <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                      <img
                        src={product.image}
                        alt={product.name}
                        className="h-full w-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "https://placehold.co/100x100?text=No+Image";
                        }}
                      />
                    </div>
                  </td>
                  <td className="py-4 pl-3 pr-3 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{product.name}</div>
                      <div className="text-sm text-gray-500 truncate max-w-[200px]">
                        {product.description && product.description.substring(0, 50)}
                        {product.description && product.description.length > 50 ? '...' : ''}
                      </div>
                    </div>
                  </td>
                  <td className="py-4 pl-3 pr-3 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{product.category}</div>
                  </td>
                  <td className="py-4 pl-3 pr-3 whitespace-nowrap">
                    {product.isSale && product.salePrice ? (
                      <div>
                        <div className="text-sm font-medium text-green-600">₹{product.salePrice.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</div>
                        <div className="text-xs text-gray-500 line-through">₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</div>
                      </div>
                    ) : (
                      <div className="text-sm text-gray-900">₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</div>
                    )}
                  </td>
                  <td className="py-4 pl-3 pr-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 w-7 p-0"
                        onClick={() => handleStockUpdate(product.id, -1)}
                        disabled={product.stock <= 0}
                      >
                        -
                      </Button>
                      <span className="mx-2 text-sm font-medium">{product.stock}</span>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-7 w-7 p-0"
                        onClick={() => handleStockUpdate(product.id, 1)}
                      >
                        +
                      </Button>
                    </div>
                  </td>
                  <td className="py-4 pl-3 pr-3 whitespace-nowrap">
                    <Switch
                      checked={product.status === 'active'}
                      onCheckedChange={(checked) => handleStatusToggle(product.id, checked)}
                    />
                  </td>
                  <td className="py-4 pl-3 pr-3 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Link to={`/products/${product.id}`} target="_blank">
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Eye className="h-4 w-4" />
                          <span className="sr-only">View</span>
                        </Button>
                      </Link>
                      <Link to={`/admin/products/${product.id}/edit`}>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                        onClick={() => {
                          setDeleteProductId(product.id);
                          setDeleteConfirmOpen(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete</span>
                      </Button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="py-8 text-center text-gray-500">
                  No products found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {totalPages > 1 && (
        <div className="py-4 px-6 bg-white border-t">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                {currentPage === 1 ? (
                  <Button variant="ghost" size="sm" className="cursor-not-allowed opacity-50">
                    Previous
                  </Button>
                ) : (
                  <PaginationPrevious onClick={() => onPageChange(Math.max(1, currentPage - 1))} />
                )}
              </PaginationItem>

              {renderPaginationItems()}

              <PaginationItem>
                {currentPage === totalPages ? (
                  <Button variant="ghost" size="sm" className="cursor-not-allowed opacity-50">
                    Next
                  </Button>
                ) : (
                  <PaginationNext onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))} />
                )}
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete this product.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={bulkDeleteConfirmOpen} onOpenChange={setBulkDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the selected {selectedProducts.length} products.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleBulkDeleteConfirm}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminProductsTable;
