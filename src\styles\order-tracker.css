/**
 * Order Tracker Styles
 * 
 * This file contains styles for the OrderTracker component.
 */

/* Progress bar styles */
.order-tracker-progress {
  position: absolute;
  height: 100%;
  background-color: var(--badhees-600);
  transition: width 0.5s ease-in-out;
}

/* Progress bar width based on order status */
.order-tracker-progress-pending {
  width: 0%;
}

.order-tracker-progress-processing {
  width: 33%;
}

.order-tracker-progress-shipped {
  width: 67%;
}

.order-tracker-progress-delivered {
  width: 100%;
}

/* Canceled order styles */
.order-tracker-canceled {
  width: 100%;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: rgba(254, 226, 226, 1);
}

/* Step styles */
.order-tracker-step {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Step icon container */
.order-tracker-step-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border: 2px solid;
}

/* Active step styles */
.order-tracker-step-active {
  background-color: var(--badhees-600);
  border-color: var(--badhees-600);
  color: white;
}

/* Inactive step styles */
.order-tracker-step-inactive {
  background-color: white;
  border-color: rgb(209, 213, 219);
  color: rgb(156, 163, 175);
}

/* Step label */
.order-tracker-step-label {
  margin-top: 0.5rem;
  text-align: center;
}

/* Step label text */
.order-tracker-step-text {
  font-size: 0.75rem;
}

/* Step description */
.order-tracker-step-description {
  font-size: 0.75rem;
  color: rgb(107, 114, 128);
  margin-top: 0.25rem;
  max-width: 7.5rem;
}
