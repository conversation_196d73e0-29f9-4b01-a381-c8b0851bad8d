
import React, { useEffect, useCallback } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import PageContainer from "@/components/layout/PageContainer";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Calendar, IndianRupee } from 'lucide-react';
import { getCustomProjects, CustomProject } from '@/services/customProjectService';
import ProjectImageCarousel from '@/components/projects/ProjectImageCarousel';
import { PullToRefresh } from "@/components/ui/pull-to-refresh";
import { useTabVisibility } from "@/hooks/use-optimized-render";

const CompletedProjects = () => {
  const { categoryId } = useParams<{ categoryId: string }>();
  const queryClient = useQueryClient();

  // Handle tab visibility changes - refetch data when tab becomes visible
  const handleTabVisible = useCallback(() => {
    console.log('Tab became visible - refreshing completed projects data');
    queryClient.invalidateQueries({ queryKey: ['completedProjects'] });
  }, [queryClient]);

  useTabVisibility(handleTabVisible);

  // Fetch projects data from Supabase
  const { data: projects = [], isLoading, refetch } = useQuery({
    queryKey: ['completedProjects', categoryId],
    queryFn: async () => {
      // Only fetch published projects
      const allProjects = await getCustomProjects('published');

      console.log('All projects:', JSON.stringify(allProjects, null, 2));
      console.log('All project categories:', [...new Set(allProjects.map(p => p.category))]);
      console.log('All project image URLs:', allProjects.map(p => ({
        name: p.name,
        category: p.category,
        images: p.image_urls,
        image_urls_type: typeof p.image_urls,
        is_array: Array.isArray(p.image_urls),
        image_urls_length: p.image_urls ? p.image_urls.length : 0
      })));

      // Filter by category if categoryId is provided
      if (categoryId) {
        console.log('Filtering by category:', categoryId);

        console.log('KITCHEN DEBUG - CategoryId for filtering:', categoryId);
        console.log('KITCHEN DEBUG - All project categories before filtering:', allProjects.map(p => ({ name: p.name, category: p.category })));

        // Special case for kitchen category
        if (categoryId === 'kitchen') {
          console.log('KITCHEN DEBUG - Using special case for kitchen category');
          const filteredProjects = allProjects.filter(project => {
            // Check if category contains 'kitchen' in any form
            const projectCategory = String(project.category || '').toLowerCase();
            const isKitchenProject =
              projectCategory === 'kitchen' ||
              projectCategory.includes('kitchen') ||
              projectCategory === 'kitchens' ||
              projectCategory.includes('kitchens');

            if (isKitchenProject) {
              console.log(`KITCHEN DEBUG - Kitchen match for project ${project.name}, category: ${project.category}`);
              return true;
            }

            console.log(`KITCHEN DEBUG - Not a kitchen project: ${project.name}, category: ${project.category}`);
            return false;
          });
          return filteredProjects;
        }

        // Special case for office category - make it a catch-all for projects that don't fit other categories
        if (categoryId === 'office') {
          console.log('OFFICE DEBUG - Using special case for office & others category');
          const mainCategories = ['kitchen', 'bedroom', 'living'];

          const filteredProjects = allProjects.filter(project => {
            const projectCategory = String(project.category || '').toLowerCase();

            // Check if it's an office project
            const isOfficeProject =
              projectCategory === 'office' ||
              projectCategory.includes('office') ||
              projectCategory === 'furniture' ||
              projectCategory.includes('furniture');

            // Check if it belongs to any of the main categories
            const isMainCategory = mainCategories.some(cat =>
              projectCategory === cat || projectCategory.includes(cat)
            );

            // Include if it's an office project OR if it doesn't belong to any main category
            if (isOfficeProject || !isMainCategory) {
              console.log(`OFFICE DEBUG - Office/Other match for project ${project.name}, category: ${project.category}`);
              return true;
            }

            console.log(`OFFICE DEBUG - Not an office/other project: ${project.name}, category: ${project.category}`);
            return false;
          });
          return filteredProjects;
        }

        // Regular category matching for other categories
        const filteredProjects = allProjects.filter(project => {
          // Direct match
          if (project.category === categoryId) {
            console.log(`KITCHEN DEBUG - Direct match for project ${project.name}, category: ${project.category}`);
            return true;
          }

          // Case insensitive match
          if (project.category?.toLowerCase() === categoryId.toLowerCase()) {
            console.log(`KITCHEN DEBUG - Case insensitive match for project ${project.name}, category: ${project.category}`);
            return true;
          }

          // Partial match (e.g., 'bedroom' matches 'bedroom-modern')
          if (project.category?.toLowerCase().includes(categoryId.toLowerCase()) ||
              categoryId.toLowerCase().includes(project.category?.toLowerCase())) {
            console.log(`KITCHEN DEBUG - Partial match for project ${project.name}, category: ${project.category}`);
            return true;
          }

          console.log(`KITCHEN DEBUG - No match for project ${project.name}, category: ${project.category}`);
          return false;
        });

        console.log('Filtered projects:', JSON.stringify(filteredProjects, null, 2));
        console.log('Filtered project image URLs:', filteredProjects.map(p => ({
          name: p.name,
          images: p.image_urls,
          image_urls_type: typeof p.image_urls,
          is_array: Array.isArray(p.image_urls),
          image_urls_length: p.image_urls ? p.image_urls.length : 0
        })));

        return filteredProjects;
      }

      return allProjects;
    },
    // Use global settings for better tab switching behavior
    // staleTime and refetchOnWindowFocus will be inherited from global config
  });

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    try {
      // Invalidate and refetch projects data
      await queryClient.invalidateQueries({ queryKey: ['completedProjects'] });
      await refetch();
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  }, [queryClient, refetch]);

  const getCategoryName = (id: string): string => {
    const categoryMap: Record<string, string> = {
      'kitchen': 'Kitchen Interiors',
      'bedroom': 'Bedroom Designs',
      'living': 'Living Room Interiors',
      'office': 'Office Furniture & Others'
    };
    return categoryMap[id] || 'Custom Projects';
  };

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [categoryId]);

  return (
    <div className="min-h-screen">
      <Navbar />

      <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
        <div className="pt-20 pb-12 md:pt-28 md:pb-16">
          <PageContainer>
          <div className="mb-6 md:mb-10 text-center">
            <h1 className="text-2xl md:text-4xl font-bold text-badhees-800 mb-2 md:mb-3">
              {getCategoryName(categoryId || '')}
            </h1>
            <p className="text-base md:text-lg text-badhees-600 max-w-2xl mx-auto">
              Browse our completed projects and see what we can do for your space
            </p>
            <div className="w-16 md:w-20 h-1 bg-badhees-accent mx-auto mt-4 md:mt-6"></div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <p className="text-badhees-600">Loading projects...</p>
            </div>
          ) : projects.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
              {projects.map((project) => (
                <Link
                  key={project.id}
                  to={`/completed-projects/detail/${project.id}`}
                  className="block group"
                >
                  <div className="bg-white rounded-xl overflow-hidden shadow-md border border-badhees-100 hover:shadow-lg transition-all duration-300 h-full flex flex-col group-hover:border-badhees-accent">
                    {/* Fixed height container for consistent image sizing */}
                    <div className="relative w-full h-[180px] md:h-[200px] overflow-hidden bg-badhees-100">
                      {Array.isArray(project.image_urls) && project.image_urls.length > 1 ? (
                        <ProjectImageCarousel images={project.image_urls} projectName={project.name} />
                      ) : (
                        <img
                          src={Array.isArray(project.image_urls) && project.image_urls.length > 0 ?
                            project.image_urls[0] :
                            (typeof project.image_urls === 'string' ? project.image_urls : '/placeholder-image.jpg')}
                          alt={project.name}
                          className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                          onError={(e) => {
                            console.log('Image load error, using placeholder');
                            e.currentTarget.src = '/placeholder-image.jpg';
                          }}
                          loading="lazy"
                        />
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <div className="p-3 md:p-6 flex-1 flex flex-col">
                      <h3 className="text-base md:text-xl font-semibold text-badhees-800 mb-1 md:mb-2 group-hover:text-badhees-accent transition-colors">
                        {project.name}
                      </h3>
                      <p className="text-xs md:text-sm text-badhees-600 line-clamp-2 mb-2 md:mb-4 flex-1">
                        {project.description}
                      </p>
                      <div className="flex flex-wrap gap-2 md:gap-4 text-xs md:text-sm text-badhees-600 mb-2 md:mb-4 border-t border-badhees-100 pt-2 md:pt-4">
                        <div className="flex items-center">
                          <IndianRupee className="h-3 w-3 md:h-4 md:w-4 mr-1 text-badhees-accent" />
                          <span>₹{project.budget ? project.budget.toLocaleString('en-IN') : 'N/A'}</span>
                        </div>
                        <div className="flex items-center ml-auto">
                          <Calendar className="h-3 w-3 md:h-4 md:w-4 mr-1 text-badhees-accent" />
                          <span>{project.completion_date ? new Date(project.completion_date).toLocaleDateString() : 'N/A'}</span>
                        </div>
                      </div>
                      <div className="flex justify-end">
                        <span className="inline-flex items-center text-xs md:text-sm text-badhees-accent font-medium group-hover:translate-x-1 transition-transform">
                          View Details <ArrowRight className="h-3 w-3 md:h-4 md:w-4 ml-1" />
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 md:py-16 bg-badhees-50 rounded-xl border border-badhees-100">
              <div className="max-w-md mx-auto px-4">
                <svg className="w-12 h-12 md:w-16 md:h-16 mx-auto mb-3 md:mb-4 text-badhees-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                <h3 className="text-xl md:text-2xl font-semibold text-badhees-800 mb-2 md:mb-3">No Projects Found</h3>
                <p className="text-sm md:text-base text-badhees-600 mb-6 md:mb-8">
                  We couldn't find any completed projects in this category. Please check back later as we're constantly updating our portfolio.
                </p>
                <div className="flex flex-col sm:flex-row gap-3 md:gap-4 justify-center">
                  <Button asChild variant="outline" size="sm" className="md:text-base">
                    <Link to="/custom-interiors">Browse All Services</Link>
                  </Button>
                  <Button asChild size="sm" className="md:text-base">
                    <Link to="/custom-interiors#contact-section">Request a Consultation</Link>
                  </Button>
                </div>
              </div>
            </div>
          )}

          <div className="mt-8 md:mt-16 bg-badhees-50 rounded-xl border border-badhees-100 p-4 md:p-8">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4 md:gap-8">
              <div className="text-center md:text-left">
                <h3 className="text-xl md:text-2xl font-semibold text-badhees-800 mb-2 md:mb-3">Need a Custom Project?</h3>
                <p className="text-sm md:text-base text-badhees-600 max-w-xl">
                  Let us know what you have in mind, and we'll create something amazing for you. Our team of experts will work closely with you to bring your vision to life.
                </p>
              </div>
              <div className="flex-shrink-0 mt-4 md:mt-0 w-full md:w-auto">
                <Button
                  asChild
                  size="default"
                  className="w-full md:w-auto bg-badhees-accent hover:bg-badhees-accent/90 shadow-md hover:shadow-lg transition-all"
                >
                  <Link to="/custom-interiors#contact-section" className="flex items-center justify-center gap-2">
                    <svg className="w-4 h-4 md:w-5 md:h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                    </svg>
                    Request a Consultation
                  </Link>
                </Button>
              </div>
            </div>
          </div>
          </PageContainer>
        </div>

        <Footer />
      </PullToRefresh>
    </div>
  );
};

export default CompletedProjects;
