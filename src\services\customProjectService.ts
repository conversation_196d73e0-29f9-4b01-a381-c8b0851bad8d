import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface CustomProject {
  id?: string;
  name: string;
  description?: string;
  category?: string;
  budget?: number;
  completion_date?: string;
  client_name?: string;
  location?: string;
  materials_used?: string;
  image_urls?: string[];
  video_url?: string;
  status?: 'draft' | 'published' | 'archived';
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

/**
 * Fetches all custom projects from Supabase
 * @param status Optional status filter
 * @returns Array of custom projects
 */
export const getCustomProjects = async (status?: string): Promise<CustomProject[]> => {
  try {
    let query = supabase
      .from('custom_projects')
      .select('*');

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching custom projects:', error);
      return [];
    }

    // Ensure image_urls is always an array
    const processedData = data.map(project => {
      let normalizedImageUrls = [];

      // Special case for kitchen projects
      if (project.category === 'kitchen' || String(project.category || '').toLowerCase().includes('kitchen')) {
        console.log('Special handling for kitchen project:', project.name);

        // Handle string format (single URL)
        if (typeof project.image_urls === 'string' && project.image_urls.trim() !== '') {
          normalizedImageUrls = [project.image_urls];
        }
        // Handle array format
        else if (Array.isArray(project.image_urls) && project.image_urls.some((url: string) => url && url.trim() !== '')) {
          normalizedImageUrls = project.image_urls.filter((url: string) => url && url.trim() !== '');
        }
        // If no valid images, use a default kitchen placeholder
        else {
          console.log('Using default kitchen placeholder for:', project.name);
          normalizedImageUrls = ['/placeholder-image.jpg'];
        }
      }
      // Handle other categories
      else {
        // Handle string format (single URL)
        if (typeof project.image_urls === 'string' && project.image_urls.trim() !== '') {
          normalizedImageUrls = [project.image_urls];
        }
        // Handle array format
        else if (Array.isArray(project.image_urls)) {
          normalizedImageUrls = project.image_urls.filter((url: string) => url && url.trim() !== '');
        }
      }

      return {
        ...project,
        image_urls: normalizedImageUrls
      };
    });

    console.log('Processed projects data:', JSON.stringify(processedData, null, 2));
    return processedData as CustomProject[];
  } catch (error) {
    console.error('Error in getCustomProjects:', error);
    return [];
  }
};

/**
 * Fetches a single custom project by ID
 * @param id The project ID
 * @returns The custom project or null if not found
 */
export const getCustomProjectById = async (id: string): Promise<CustomProject | null> => {
  try {
    const { data, error } = await supabase
      .from('custom_projects')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching custom project:', error);
      return null;
    }

    // Ensure image_urls is always an array
    let normalizedImageUrls = [];

    // Special case for kitchen projects
    if (data.category === 'kitchen' || String(data.category || '').toLowerCase().includes('kitchen')) {
      console.log('Special handling for kitchen project detail:', data.name);

      // Handle string format (single URL)
      if (typeof data.image_urls === 'string' && data.image_urls.trim() !== '') {
        normalizedImageUrls = [data.image_urls];
      }
      // Handle array format
      else if (Array.isArray(data.image_urls) && data.image_urls.some((url: string) => url && url.trim() !== '')) {
        normalizedImageUrls = data.image_urls.filter((url: string) => url && url.trim() !== '');
      }
      // If no valid images, use a default kitchen placeholder
      else {
        console.log('Using default kitchen placeholder for detail:', data.name);
        normalizedImageUrls = ['/placeholder-image.jpg'];
      }
    }
    // Handle other categories
    else {
      // Handle string format (single URL)
      if (typeof data.image_urls === 'string' && data.image_urls.trim() !== '') {
        normalizedImageUrls = [data.image_urls];
      }
      // Handle array format
      else if (Array.isArray(data.image_urls)) {
        normalizedImageUrls = data.image_urls.filter((url: string) => url && url.trim() !== '');
      }
    }

    const processedData = {
      ...data,
      image_urls: normalizedImageUrls
    };

    console.log('Processed project data:', JSON.stringify(processedData, null, 2));
    return processedData as CustomProject;
  } catch (error) {
    console.error('Error in getCustomProjectById:', error);
    return null;
  }
};

/**
 * Creates a new custom project in Supabase
 * @param project The project data to create
 * @returns The created project or null if creation failed
 */
export const createCustomProject = async (project: CustomProject): Promise<CustomProject | null> => {
  try {
    // Get current user ID
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('Authentication error:', authError);
      toast({
        title: 'Authentication Error',
        description: 'You must be logged in to create a project',
        variant: 'destructive',
      });
      return null;
    }

    // Add the user ID to the project data
    const projectWithUser = {
      ...project,
      created_by: user.id
    };

    console.log('Creating project with data:', projectWithUser);

    const { data, error } = await supabase
      .from('custom_projects')
      .insert([projectWithUser])
      .select()
      .single();

    if (error) {
      console.error('Error creating custom project:', error);
      toast({
        title: 'Error creating project',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    toast({
      title: 'Project created',
      description: 'Your project has been successfully created',
    });

    return data as CustomProject;
  } catch (error: any) {
    console.error('Error in createCustomProject:', error);
    toast({
      title: 'Error creating project',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Updates an existing custom project in Supabase
 * @param id The project ID to update
 * @param project The project data to update
 * @returns True if successful, false otherwise
 */
export const updateCustomProject = async (
  id: string,
  project: Partial<CustomProject>
): Promise<boolean> => {
  try {
    // Get current user ID
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('Authentication error:', authError);
      toast({
        title: 'Authentication Error',
        description: 'You must be logged in to update a project',
        variant: 'destructive',
      });
      return false;
    }



    // Remove any fields that shouldn't be updated
    const { created_by, created_at, ...updateData } = project;

    console.log('Updating project with data:', updateData);

    const { error } = await supabase
      .from('custom_projects')
      .update(updateData)
      .eq('id', id);

    if (error) {
      console.error('Error updating custom project:', error);
      toast({
        title: 'Error updating project',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Project updated',
      description: 'Your project has been successfully updated',
    });

    return true;
  } catch (error: any) {
    console.error('Error in updateCustomProject:', error);
    toast({
      title: 'Error updating project',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Archives or permanently deletes a custom project from Supabase
 * @param id The project ID to delete
 * @param permanent Whether to permanently delete the project (default: false)
 * @returns True if successful, false otherwise
 */
export const deleteCustomProject = async (id: string, permanent: boolean = false): Promise<boolean> => {
  try {
    // Get current user ID
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('Authentication error:', authError);
      toast({
        title: 'Authentication Error',
        description: 'You must be logged in to delete a project',
        variant: 'destructive',
      });
      return false;
    }



    // First, check the current status of the project
    const { data: project, error: fetchError } = await supabase
      .from('custom_projects')
      .select('status')
      .eq('id', id)
      .single();

    if (fetchError) {
      console.error('Error fetching project status:', fetchError);
      toast({
        title: 'Error deleting project',
        description: fetchError.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    console.log('Project status:', project?.status, 'Permanent delete:', permanent);

    // If the project is already archived or permanent deletion is requested, delete it permanently
    if (permanent || project?.status === 'archived') {
      const { error } = await supabase
        .from('custom_projects')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error permanently deleting custom project:', error);
        toast({
          title: 'Error deleting project',
          description: error.message || 'An unexpected error occurred',
          variant: 'destructive',
        });
        return false;
      }

      toast({
        title: 'Project deleted',
        description: 'Your project has been permanently deleted',
      });
    } else {
      // Otherwise, just update the status to 'archived'
      const { error } = await supabase
        .from('custom_projects')
        .update({ status: 'archived' })
        .eq('id', id);

      if (error) {
        console.error('Error archiving custom project:', error);
        toast({
          title: 'Error archiving project',
          description: error.message || 'An unexpected error occurred',
          variant: 'destructive',
        });
        return false;
      }

      toast({
        title: 'Project archived',
        description: 'Your project has been archived',
      });
    }

    return true;
  } catch (error: any) {
    console.error('Error in deleteCustomProject:', error);
    toast({
      title: 'Error deleting project',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Uploads an image for a custom project to Supabase Storage
 * @param file The file to upload
 * @returns The URL of the uploaded file, or null if upload failed
 */
export const uploadProjectImage = async (file: File): Promise<string | null> => {
  try {
    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `${fileName}`;

    // Upload the file to Supabase Storage (using 'others' bucket like existing images)
    const { error } = await supabase.storage
      .from('others')
      .upload(`project-images/${filePath}`, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      throw error;
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('others')
      .getPublicUrl(`project-images/${filePath}`);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading project image:', error);
    return null;
  }
};


