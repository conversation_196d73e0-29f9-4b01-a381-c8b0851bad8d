/**
 * Notification Service
 *
 * Handles admin notifications for new orders, messages, and requests.
 * Provides real-time updates and notification counts.
 */
import { supabase } from '@/lib/supabase';

export interface NotificationCounts {
  orders: number;
  customizationRequests: number;
  contactMessages: number;
  consultationRequests: number;
}

/**
 * Get count of new/unread orders (pending status)
 */
export const getNewOrdersCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching new orders count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewOrdersCount:', error);
    return 0;
  }
};

/**
 * Get count of new/unread customization requests
 */
export const getNewCustomizationRequestsCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('customization_requests')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching customization requests count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewCustomizationRequestsCount:', error);
    return 0;
  }
};

/**
 * Get count of new/unread contact messages
 */
export const getNewContactMessagesCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('contact_submissions')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'new');

    if (error) {
      console.error('Error fetching contact messages count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewContactMessagesCount:', error);
    return 0;
  }
};

/**
 * Get count of new/unread consultation requests
 */
export const getNewConsultationRequestsCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('consultation_requests')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'pending');

    if (error) {
      console.error('Error fetching consultation requests count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getNewConsultationRequestsCount:', error);
    return 0;
  }
};

/**
 * Get all notification counts in a single call
 */
export const getAllNotificationCounts = async (): Promise<NotificationCounts> => {
  try {
    const [orders, customizationRequests, contactMessages, consultationRequests] = await Promise.all([
      getNewOrdersCount(),
      getNewCustomizationRequestsCount(),
      getNewContactMessagesCount(),
      getNewConsultationRequestsCount()
    ]);

    return {
      orders,
      customizationRequests,
      contactMessages,
      consultationRequests
    };
  } catch (error) {
    console.error('Error in getAllNotificationCounts:', error);
    return {
      orders: 0,
      customizationRequests: 0,
      contactMessages: 0,
      consultationRequests: 0
    };
  }
};

/**
 * Mark orders as viewed (when admin opens orders page)
 */
export const markOrdersAsViewed = async (): Promise<void> => {
  try {
    // Update pending orders to processing status when admin views them
    const { error } = await supabase
      .from('orders')
      .update({ status: 'processing' })
      .eq('status', 'pending');

    if (error) {
      console.error('Error marking orders as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markOrdersAsViewed:', error);
  }
};

/**
 * Mark customization requests as viewed
 */
export const markCustomizationRequestsAsViewed = async (): Promise<void> => {
  try {
    const { error } = await supabase
      .from('customization_requests')
      .update({ status: 'in_progress' })
      .eq('status', 'pending');

    if (error) {
      console.error('Error marking customization requests as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markCustomizationRequestsAsViewed:', error);
  }
};

/**
 * Mark contact messages as viewed
 */
export const markContactMessagesAsViewed = async (): Promise<void> => {
  try {
    const { error } = await supabase
      .from('contact_submissions')
      .update({ status: 'read' })
      .eq('status', 'new');

    if (error) {
      console.error('Error marking contact messages as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markContactMessagesAsViewed:', error);
  }
};

/**
 * Mark consultation requests as viewed
 */
export const markConsultationRequestsAsViewed = async (): Promise<void> => {
  try {
    const { error } = await supabase
      .from('consultation_requests')
      .update({ status: 'contacted' })
      .eq('status', 'pending');

    if (error) {
      console.error('Error marking consultation requests as viewed:', error);
    }
  } catch (error) {
    console.error('Error in markConsultationRequestsAsViewed:', error);
  }
};

/**
 * Subscribe to real-time notification updates
 */
export const subscribeToNotifications = (
  callback: (counts: NotificationCounts) => void
): (() => void) => {
  const channels: any[] = [];

  // Subscribe to orders changes
  const ordersChannel = supabase
    .channel('orders-notifications')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'orders' },
      async () => {
        const counts = await getAllNotificationCounts();
        callback(counts);
      }
    )
    .subscribe();
  channels.push(ordersChannel);

  // Subscribe to customization requests changes
  const customizationChannel = supabase
    .channel('customization-notifications')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'customization_requests' },
      async () => {
        const counts = await getAllNotificationCounts();
        callback(counts);
      }
    )
    .subscribe();
  channels.push(customizationChannel);

  // Subscribe to contact messages changes
  const contactChannel = supabase
    .channel('contact-notifications')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'contact_submissions' },
      async () => {
        const counts = await getAllNotificationCounts();
        callback(counts);
      }
    )
    .subscribe();
  channels.push(contactChannel);

  // Subscribe to consultation requests changes
  const consultationChannel = supabase
    .channel('consultation-notifications')
    .on('postgres_changes',
      { event: '*', schema: 'public', table: 'consultation_requests' },
      async () => {
        const counts = await getAllNotificationCounts();
        callback(counts);
      }
    )
    .subscribe();
  channels.push(consultationChannel);

  // Return unsubscribe function
  return () => {
    channels.forEach(channel => {
      supabase.removeChannel(channel);
    });
  };
};
