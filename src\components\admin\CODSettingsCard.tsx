/**
 * COD Settings Card Component
 * 
 * Admin component to toggle Cash on Delivery availability.
 */
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Loader2, Truck, CreditCard, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useCODEnabled, useToggleCOD } from '@/hooks/useSettings';
import { toast } from '@/hooks/use-toast';

const CODSettingsCard: React.FC = () => {
  const { data: isCODEnabled, isLoading: isLoadingCOD, error } = useCODEnabled();
  const toggleCODMutation = useToggleCOD();

  const handleCODToggle = async (enabled: boolean) => {
    try {
      await toggleCODMutation.mutateAsync(enabled);
    } catch (error) {
      console.error('Error toggling COD:', error);
      toast({
        title: 'Error',
        description: 'Failed to update COD settings. Please try again.',
        variant: 'destructive'
      });
    }
  };

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
            Payment Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load payment settings. Please refresh the page.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <CreditCard className="h-5 w-5 mr-2 text-badhees-600" />
          Payment Settings
        </CardTitle>
        <CardDescription>
          Manage payment method availability for customers
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* COD Toggle */}
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="cod-toggle" className="text-base font-medium">
              Cash on Delivery (COD)
            </Label>
            <p className="text-sm text-muted-foreground">
              Allow customers to pay when they receive their order
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {isLoadingCOD && (
              <Loader2 className="h-4 w-4 animate-spin text-badhees-600" />
            )}
            <Switch
              id="cod-toggle"
              checked={isCODEnabled || false}
              onCheckedChange={handleCODToggle}
              disabled={isLoadingCOD || toggleCODMutation.isPending}
            />
          </div>
        </div>

        {/* Status Indicator */}
        <div className="flex items-center space-x-2 p-3 rounded-lg bg-gray-50">
          <Truck className={`h-4 w-4 ${isCODEnabled ? 'text-green-600' : 'text-gray-400'}`} />
          <span className="text-sm font-medium">
            COD Status: 
            <span className={`ml-1 ${isCODEnabled ? 'text-green-600' : 'text-red-600'}`}>
              {isLoadingCOD ? 'Loading...' : (isCODEnabled ? 'Enabled' : 'Disabled')}
            </span>
          </span>
        </div>

        {/* Information Alert */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {isCODEnabled ? (
              <>
                <strong>COD is currently enabled.</strong> Customers can select "Cash on Delivery" 
                during checkout and pay when they receive their order.
              </>
            ) : (
              <>
                <strong>COD is currently disabled.</strong> Customers will see the COD option 
                but won't be able to select it. They'll be prompted to use online payment methods.
              </>
            )}
          </AlertDescription>
        </Alert>

        {/* Loading State */}
        {toggleCODMutation.isPending && (
          <Alert>
            <Loader2 className="h-4 w-4 animate-spin" />
            <AlertDescription>
              Updating COD settings...
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

export default CODSettingsCard;
