
/* Import spacing system */
@import './styles/spacing-system.css';
/* Import mobile optimizations */
@import './styles/mobile-optimizations.css';
/* Import product indicators styling */
@import './styles/product-indicators.css';
/* Import auth forms styling */
@import './styles/auth-forms.css';
/* Import completed projects mobile optimizations */
@import './styles/completed-projects-mobile.css';
/* Import product consistency styles */
@import './styles/product-consistency.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  /* Base HTML elements */
  html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-tap-highlight-color: transparent;
  }

  @media (max-width: 640px) {
    html {
      font-size: 15px;
    }
  }

  body {
    @apply bg-background text-foreground antialiased;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
  }
}

@layer base {
  * {
    @apply border-border;
  }

  /* Make images responsive by default */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Increase tap target sizes on mobile */
  button,
  [role="button"],
  a {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-badhees-accent;
  }

  /* Touch-friendly form elements */
  input,
  select,
  textarea {
    @apply text-base;
    min-height: 44px;
  }
}

@layer components {
  .btn-primary {
    @apply inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 bg-badhees-accent text-white font-medium rounded-md transition-colors hover:bg-badhees-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-badhees-accent;
  }

  .btn-secondary {
    @apply inline-flex items-center px-4 sm:px-6 py-2 sm:py-3 border border-badhees-300 text-badhees-800 font-medium rounded-md transition-colors hover:bg-badhees-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-badhees-300;
  }

  .heading-1 {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight;
  }

  .heading-2 {
    @apply text-2xl sm:text-3xl md:text-4xl font-bold;
  }

  .subtitle {
    @apply text-base sm:text-lg md:text-xl text-badhees-600;
  }

  .section-container {
    @apply max-w-[1400px] mx-auto px-4 sm:px-6 md:px-8 py-8 sm:py-12 md:py-16 lg:py-24;
  }

  .glass-morphism {
    @apply bg-white/80 backdrop-blur-sm border border-white/80 shadow-lg;
  }

  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-badhees-accent;
  }

  /* Mobile-friendly card */
  .mobile-card {
    @apply rounded-xl border border-badhees-100 bg-white shadow-sm transition-all duration-300 hover:shadow-md overflow-hidden;
  }

  /* Responsive flex container */
  .responsive-flex {
    @apply flex flex-col sm:flex-row;
  }

  /* Responsive grid container */
  .responsive-grid {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6;
  }
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.6s ease-in forwards;
  }

  .animate-slideUp {
    animation: slideUp 0.6s ease-out forwards;
  }

  .animate-slideDown {
    animation: slideDown 0.6s ease-out forwards;
  }

  .animate-delay-100 {
    animation-delay: 100ms;
  }

  .animate-delay-200 {
    animation-delay: 200ms;
  }

  .animate-delay-300 {
    animation-delay: 300ms;
  }

  .animate-delay-400 {
    animation-delay: 400ms;
  }

  .animate-delay-500 {
    animation-delay: 500ms;
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Safe area insets for notched devices */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Hide scrollbar but allow scrolling */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
  }

  /* Firefox */
  @supports (scrollbar-width: none) {
    .no-scrollbar {
      scrollbar-width: none;
    }
  }

  /* Chrome, Safari, Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
    background: transparent;
  }

  /* Aspect ratio containers */
  .aspect-square {
    aspect-ratio: 1 / 1;
  }

  .aspect-video {
    aspect-ratio: 16 / 9;
  }

  .aspect-portrait {
    aspect-ratio: 3 / 4;
  }
}

/* Optimized animations for better performance */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Performance optimized animations with will-change */
.animate-fade-in {
  will-change: opacity;
}

.animate-slide-up {
  will-change: opacity, transform;
}

.animate-slide-down {
  will-change: opacity, transform;
}

.animate-slide-in-right {
  will-change: opacity, transform;
}

.animate-fadeIn {
  animation-name: fadeIn;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
  will-change: opacity;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideUp {
  animation-name: slideUp;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
  will-change: transform, opacity;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideDown {
  animation-name: slideDown;
  animation-duration: 0.5s;
  animation-fill-mode: forwards;
  will-change: transform, opacity;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInRight {
  animation-name: slideInRight;
  animation-duration: 0.3s;
  animation-fill-mode: forwards;
  will-change: transform, opacity;
}
