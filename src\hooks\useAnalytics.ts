/**
 * Analytics data fetching hooks using React Query
 * 
 * These hooks provide optimized data fetching for analytics data with caching,
 * background updates, and error handling.
 */
import { useQuery } from '@tanstack/react-query';
import { getDashboardStats } from '@/services/analytics/dashboardService';
import { 
  getTotalRevenue, 
  getOrderStats, 
  getSalesTrend, 
  getWeeklyPerformance 
} from '@/services/analytics/revenueService';
import { 
  getCustomerStats, 
  getCategoryPerformance, 
  getTopSellingProducts 
} from '@/services/analytics/customerProductService';
import { TopProductsSortBy } from '@/services/analytics/types';

// Query keys for React Query
export const analyticsKeys = {
  all: ['analytics'] as const,
  dashboard: () => [...analyticsKeys.all, 'dashboard'] as const,
  revenue: () => [...analyticsKeys.all, 'revenue'] as const,
  orders: () => [...analyticsKeys.all, 'orders'] as const,
  customers: () => [...analyticsKeys.all, 'customers'] as const,
  categories: () => [...analyticsKeys.all, 'categories'] as const,
  salesTrend: () => [...analyticsKeys.all, 'salesTrend'] as const,
  weeklyPerformance: () => [...analyticsKeys.all, 'weeklyPerformance'] as const,
  topProducts: (sortBy: TopProductsSortBy) => [...analyticsKeys.all, 'topProducts', sortBy] as const,
};

/**
 * Hook to fetch all dashboard statistics
 * @param sortBy Sort method for top products
 * @returns Query result with dashboard data, loading state, and error
 */
export function useDashboardStats(sortBy: TopProductsSortBy = 'revenue') {
  return useQuery({
    queryKey: [...analyticsKeys.dashboard(), sortBy],
    queryFn: () => getDashboardStats(sortBy),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to fetch total revenue
 * @returns Query result with total revenue, loading state, and error
 */
export function useTotalRevenue() {
  return useQuery({
    queryKey: analyticsKeys.revenue(),
    queryFn: getTotalRevenue,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch order statistics
 * @returns Query result with order stats, loading state, and error
 */
export function useOrderStats() {
  return useQuery({
    queryKey: analyticsKeys.orders(),
    queryFn: getOrderStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch customer statistics
 * @returns Query result with customer stats, loading state, and error
 */
export function useCustomerStats() {
  return useQuery({
    queryKey: analyticsKeys.customers(),
    queryFn: getCustomerStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch category performance
 * @returns Query result with category performance data, loading state, and error
 */
export function useCategoryPerformance() {
  return useQuery({
    queryKey: analyticsKeys.categories(),
    queryFn: getCategoryPerformance,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch sales trend
 * @returns Query result with sales trend data, loading state, and error
 */
export function useSalesTrend() {
  return useQuery({
    queryKey: analyticsKeys.salesTrend(),
    queryFn: getSalesTrend,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch weekly performance
 * @returns Query result with weekly performance data, loading state, and error
 */
export function useWeeklyPerformance() {
  return useQuery({
    queryKey: analyticsKeys.weeklyPerformance(),
    queryFn: getWeeklyPerformance,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch top selling products
 * @param limit Number of products to fetch
 * @param sortBy Sort method (revenue or quantity)
 * @returns Query result with top products data, loading state, and error
 */
export function useTopSellingProducts(limit: number = 5, sortBy: TopProductsSortBy = 'revenue') {
  return useQuery({
    queryKey: [...analyticsKeys.topProducts(sortBy), limit],
    queryFn: () => getTopSellingProducts(limit, sortBy),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
