<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <!-- Theme colors for different browsers -->
    <meta name="theme-color" content="#8B4513" />
    <meta name="msapplication-navbutton-color" content="#8B4513" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="color-scheme" content="light" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="robots" content="index,follow" />
    <meta name="googlebot" content="index,follow" />
    <title>The Badhees - Premium Handcrafted Furniture & Home Decor</title>
    <meta name="description" content="The Badhees - Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta name="keywords" content="furniture, home decor, handcrafted, premium, interior design, custom furniture, The Badhees" />
    <meta name="author" content="The Badhees" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.thebadhees.com/" />
    <meta property="og:title" content="The Badhees - Premium Handcrafted Furniture & Home Decor" />
    <meta property="og:description" content="Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:site_name" content="The Badhees" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://www.thebadhees.com/" />
    <meta property="twitter:title" content="The Badhees - Premium Handcrafted Furniture & Home Decor" />
    <meta property="twitter:description" content="Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta property="twitter:image" content="/og-image.png" />

    <!-- Critical Image Preloads for LCP Optimization -->
    <link rel="preload" as="image" href="/hero-banners/hero-banner-1.avif" type="image/avif" />
    <link rel="preload" as="image" href="/hero-banners/hero-banner-2.avif" type="image/avif" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

    <!-- Theme Color -->
    <meta name="theme-color" content="#8B4513" />
    <meta name="msapplication-TileColor" content="#8B4513" />

    <!-- Favicons and Icons -->
    <link rel="manifest" href="/manifest.webmanifest" />

    <!-- Apple Touch Icon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.svg" />

    <!-- Standard Favicons -->
    <link rel="icon" type="image/svg+xml" sizes="32x32" href="/icons/favicon-32x32.svg" />
    <link rel="icon" type="image/svg+xml" sizes="16x16" href="/icons/favicon-16x16.svg" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="icon" href="/favicon.ico" />

    <!-- Fallback for older browsers -->
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />

    <!-- Schema.org markup for Google+ -->
    <meta itemprop="name" content="The Badhees - Premium Handcrafted Furniture & Home Decor" />
    <meta itemprop="description" content="Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality." />
    <meta itemprop="image" content="/og-image.png" />

    <!-- Additional SEO Meta Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="googlebot" content="index, follow" />
    <link rel="canonical" href="https://thebadhees.com/" />
    <script>
      // Check if we have a redirect path from the 404 page
      (function() {
        const redirectPath = sessionStorage.getItem('redirectPath');
        if (redirectPath) {
          sessionStorage.removeItem('redirectPath');
          // We'll let the app handle the routing once it loads
          window.history.replaceState(null, null, redirectPath);
        }
      })();
    </script>
  </head>

  <body>
    <div id="root"></div>
    <!-- External script removed temporarily for debugging -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
