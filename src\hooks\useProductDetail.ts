/**
 * Product detail data fetching hook using React Query
 *
 * This hook provides optimized data fetching for product details with caching,
 * background updates, and error handling.
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { getProductReviews, addProductReview, hasUserPurchasedProduct, ProductReview } from '@/services/productReviewsService';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useState } from 'react';
import { toast } from '@/hooks/use-toast';

// Query keys for React Query
export const productDetailKeys = {
  all: ['productDetail'] as const,
  reviews: (productId: string) => [...productDetailKeys.all, 'reviews', productId] as const,
  hasPurchased: (productId: string, userId: string) => [...productDetailKeys.all, 'hasPurchased', productId, userId] as const,
  products: ['products'] as const,
  product: (productId: string) => ['product', productId] as const,
};

/**
 * Hook to fetch product reviews
 * @param productId Product ID
 * @returns Query result with reviews data, loading state, and error
 */
export function useProductReviews(productId: string) {
  return useQuery({
    queryKey: productDetailKeys.reviews(productId),
    queryFn: () => getProductReviews(productId),
    enabled: !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to check if a user has purchased a product
 * @param productId Product ID
 * @returns Query result with purchase status, loading state, and error
 */
export function useHasPurchasedProduct(productId: string) {
  const { isAuthenticated } = useAuth();
  const [userId, setUserId] = useState<string | null>(null);

  // Get the current user ID
  useQuery({
    queryKey: ['currentUser'],
    queryFn: async () => {
      const { data } = await supabase.auth.getUser();
      if (data.user) {
        setUserId(data.user.id);
      }
      return data.user;
    },
    enabled: isAuthenticated,
  });

  return useQuery({
    queryKey: userId ? productDetailKeys.hasPurchased(productId, userId) : ['noPurchaseCheck'],
    queryFn: () => hasUserPurchasedProduct(productId, userId!),
    enabled: !!productId && !!userId && isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to add a product review
 * @returns Mutation function and state
 */
export function useAddProductReview() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (reviewData: {
      productId: string;
      rating: number;
      title: string;
      comment: string;
    }) => {
      console.log('Submitting review with data:', reviewData);
      return addProductReview(
        reviewData.productId,
        reviewData.rating,
        reviewData.comment,
        reviewData.title
      );
    },

    onSuccess: (newReview, variables) => {
      console.log('Review submission successful:', newReview);

      if (newReview) {
        try {
          // Update the reviews cache with the new review
          queryClient.setQueryData<ProductReview[]>(
            productDetailKeys.reviews(variables.productId),
            (oldReviews = []) => {
              console.log('Updating reviews cache with new review');
              return [newReview, ...oldReviews];
            }
          );
        } catch (error) {
          console.error('Error updating reviews cache:', error);
        }

        // Show success message
        toast({
          title: 'Review submitted',
          description: 'Thank you for your feedback!'
        });
      }

      // Invalidate all relevant queries to ensure UI is updated
      console.log('Invalidating queries to refresh UI');

      try {
        // Invalidate the reviews query
        queryClient.invalidateQueries({
          queryKey: productDetailKeys.reviews(variables.productId),
        });

        // Invalidate all products query to update ratings in product listings
        queryClient.invalidateQueries({
          queryKey: productDetailKeys.products,
        });

        // Invalidate the specific product query
        queryClient.invalidateQueries({
          queryKey: productDetailKeys.product(variables.productId),
        });

        // Invalidate featured products and other product-related queries
        queryClient.invalidateQueries({
          queryKey: ['featuredProducts'],
        });

        queryClient.invalidateQueries({
          queryKey: ['categoryProducts'],
        });

        // Force refetch the product detail
        queryClient.refetchQueries({
          queryKey: productDetailKeys.product(variables.productId),
        });
      } catch (error) {
        console.error('Error invalidating queries:', error);
      }
    },

    onError: (error) => {
      console.error('Error submitting review:', error);
      toast({
        title: 'Error submitting review',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive'
      });
    }
  });
}
