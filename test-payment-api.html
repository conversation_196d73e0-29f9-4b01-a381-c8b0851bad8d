<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Payment API Test</h1>
    
    <div class="test-section">
        <h2>1. CORS Test</h2>
        <button onclick="testCORS()">Test CORS</button>
        <div id="cors-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Create Order Test</h2>
        <button onclick="testCreateOrder()">Test Create Order</button>
        <div id="create-order-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. Verify Payment Test</h2>
        <button onclick="testVerifyPayment()">Test Verify Payment</button>
        <div id="verify-payment-result" class="result"></div>
    </div>

    <script>
        const baseUrl = window.location.origin;
        
        async function testCORS() {
            const resultDiv = document.getElementById('cors-result');
            resultDiv.textContent = 'Testing CORS...';
            
            try {
                const response = await fetch(`${baseUrl}/api/cors-test`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                const data = await response.json();
                resultDiv.textContent = `✅ CORS Test Success:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ CORS Test Failed:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testCreateOrder() {
            const resultDiv = document.getElementById('create-order-result');
            resultDiv.textContent = 'Testing Create Order...';
            
            try {
                const response = await fetch(`${baseUrl}/api/razorpay/create-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        amount: 100,
                        currency: 'INR',
                        receipt: `test_${Date.now()}`,
                        notes: {
                            test: true
                        }
                    })
                });
                
                const data = await response.json();
                resultDiv.textContent = `✅ Create Order Success:\n${JSON.stringify(data, null, 2)}`;
                resultDiv.className = 'result success';
            } catch (error) {
                resultDiv.textContent = `❌ Create Order Failed:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        async function testVerifyPayment() {
            const resultDiv = document.getElementById('verify-payment-result');
            resultDiv.textContent = 'Testing Verify Payment...';
            
            try {
                const response = await fetch(`${baseUrl}/api/razorpay/verify-payment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        razorpay_order_id: 'order_test_123',
                        razorpay_payment_id: 'pay_test_123',
                        razorpay_signature: 'test_signature',
                        order_id: 'test_order_123'
                    })
                });
                
                const data = await response.json();
                if (data.success) {
                    resultDiv.textContent = `✅ Verify Payment Success:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `⚠️ Verify Payment Expected Failure (test data):\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result';
                }
            } catch (error) {
                resultDiv.textContent = `❌ Verify Payment Failed:\n${error.message}`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
