/* Consultation Form Styles */

.consultation-form-wrapper {
  /* Ensure proper z-index stacking for dropdowns */
  position: relative;
  z-index: 1;
}

/* Dark theme form styles */
.consultation-form-wrapper .bg-white\/20 {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.consultation-form-wrapper .text-white {
  color: white;
}

.consultation-form-wrapper .text-white\/80 {
  color: rgba(255, 255, 255, 0.8);
}

.consultation-form-wrapper .text-white\/60 {
  color: rgba(255, 255, 255, 0.6);
}

/* Select dropdown specific styles */
.consultation-form-wrapper [data-radix-select-content] {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  z-index: 9999 !important;
  max-height: 240px !important;
  overflow-y: auto !important;
}

.consultation-form-wrapper [data-radix-select-item] {
  cursor: pointer !important;
  padding: 8px 12px !important;
  color: #374151 !important;
  transition: background-color 0.2s ease !important;
}

.consultation-form-wrapper [data-radix-select-item]:hover {
  background-color: #f3f4f6 !important;
}

.consultation-form-wrapper [data-radix-select-item][data-highlighted] {
  background-color: #f3f4f6 !important;
  outline: none !important;
}

/* Ensure dropdown appears above other elements */
.consultation-form-wrapper [data-radix-select-viewport] {
  padding: 4px !important;
}

/* Fix for mobile devices */
@media (max-width: 640px) {
  .consultation-form-wrapper [data-radix-select-content] {
    max-height: 200px !important;
    width: 100% !important;
  }
  
  .consultation-form-wrapper [data-radix-select-item] {
    padding: 12px 16px !important;
    font-size: 16px !important; /* Prevents zoom on iOS */
  }
}

/* Input focus styles for dark theme */
.consultation-form-wrapper input:focus,
.consultation-form-wrapper textarea:focus,
.consultation-form-wrapper [data-radix-select-trigger]:focus {
  outline: none !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1) !important;
}

/* Placeholder text styling */
.consultation-form-wrapper input::placeholder,
.consultation-form-wrapper textarea::placeholder {
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Button styling */
.consultation-form-wrapper button[type="submit"] {
  transition: all 0.2s ease !important;
}

.consultation-form-wrapper button[type="submit"]:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* Loading state */
.consultation-form-wrapper button[disabled] {
  opacity: 0.7 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Form validation styles */
.consultation-form-wrapper input:invalid,
.consultation-form-wrapper textarea:invalid {
  border-color: rgba(239, 68, 68, 0.5) !important;
}

.consultation-form-wrapper input:valid,
.consultation-form-wrapper textarea:valid {
  border-color: rgba(34, 197, 94, 0.5) !important;
}

/* Responsive grid adjustments */
@media (max-width: 640px) {
  .consultation-form-wrapper .grid-cols-1.sm\\:grid-cols-2 {
    grid-template-columns: 1fr !important;
  }
  
  .consultation-form-wrapper .gap-4 {
    gap: 1rem !important;
  }
}

/* Accessibility improvements */
.consultation-form-wrapper label {
  font-weight: 500 !important;
  margin-bottom: 0.5rem !important;
  display: block !important;
}

.consultation-form-wrapper input,
.consultation-form-wrapper textarea,
.consultation-form-wrapper [data-radix-select-trigger] {
  min-height: 44px !important; /* Touch-friendly size */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .consultation-form-wrapper .bg-white\/20 {
    background-color: rgba(255, 255, 255, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
  }
  
  .consultation-form-wrapper .text-white\/80 {
    color: white !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .consultation-form-wrapper * {
    transition: none !important;
    animation: none !important;
  }
}
