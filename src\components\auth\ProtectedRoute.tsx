
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import Loading from '@/components/ui/loading';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'user' | 'admin';
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole = 'user'
}) => {
  const { isAuthenticated, user, isLoading } = useAuth();

  // Show loading state while checking authentication
  if (isLoading) {
    return <Loading fullScreen size="lg" />;
  }

  const hasRequiredRole = () => {
    if (!user || !user.role) return false;

    switch (requiredRole) {
      case 'admin':
        return user.role === 'admin';
      case 'user':
        return ['user', 'admin'].includes(user.role);
      default:
        return false;
    }
  };

  if (!isAuthenticated) {
    toast({
      title: "Access Denied",
      description: "Please log in to access this page",
      variant: "destructive"
    });
    return <Navigate to="/login" replace />;
  }

  if (!hasRequiredRole()) {
    toast({
      title: "Permission Denied",
      description: `You need ${requiredRole} permissions to access this page`,
      variant: "destructive"
    });

    // Redirect to appropriate dashboard based on role
    if (user?.role === 'admin') {
      return <Navigate to="/admin" replace />;
    } else {
      return <Navigate to="/" replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;
