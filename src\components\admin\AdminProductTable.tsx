
import React from 'react';
import { Link } from 'react-router-dom';
import { Pencil, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  salePrice?: number;
  isSale?: boolean;
  isNew?: boolean;
  image: string;
  category: string;
}

interface AdminProductTableProps {
  products: Product[];
  onDelete: (id: string) => void;
}

const AdminProductTable = ({ products, onDelete }: AdminProductTableProps) => {
  return (
    <div className="overflow-x-auto animate-fade-in">
      <div className="min-w-full">
        <table className="min-w-full divide-y divide-badhees-200">
          <thead className="bg-badhees-50">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-badhees-500 uppercase tracking-wider">
                Product
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-badhees-500 uppercase tracking-wider">
                Category
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-badhees-500 uppercase tracking-wider">
                Price
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-badhees-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-badhees-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-badhees-200">
            {products.length > 0 ? (
              products.map((product) => (
                <tr key={product.id} className="hover:bg-badhees-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-10 w-10 flex-shrink-0 rounded overflow-hidden bg-badhees-100">
                        <img
                          src={product.image}
                          alt={product.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-badhees-800">{product.name}</div>
                        <div className="text-sm text-badhees-500 truncate max-w-xs">
                          {product.description?.substring(0, 60)}
                          {product.description && product.description.length > 60 ? '...' : ''}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-badhees-600">{product.category}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {product.isSale && product.salePrice ? (
                      <div>
                        <div className="text-sm font-medium text-badhees-accent">₹{product.salePrice.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</div>
                        <div className="text-xs text-badhees-500 line-through">₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</div>
                      </div>
                    ) : (
                      <div className="text-sm text-badhees-600">₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-2">
                      {product.isNew && (
                        <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">
                          New
                        </span>
                      )}
                      {product.isSale && (
                        <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                          Sale
                        </span>
                      )}
                      {!product.isNew && !product.isSale && (
                        <span className="px-2 py-1 text-xs font-medium bg-badhees-100 text-badhees-600 rounded">
                          Regular
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Button
                      size="sm"
                      variant="outline"
                      className="mr-2"
                      asChild
                    >
                      <Link to={`/admin/products/${product.id}/edit`}>
                        <Pencil className="h-4 w-4" />
                      </Link>
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => onDelete(product.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-badhees-500">
                  No products found. Add your first product to get started.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AdminProductTable;
