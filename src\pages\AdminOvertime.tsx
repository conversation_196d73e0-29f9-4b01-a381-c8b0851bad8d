import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ArrowLeft, Check, Clock, Loader2, Save, X } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { useAuth } from '@/context/SupabaseAuthContext';
import {
  useEmployees,
  useOvertimeByEmployee,
  useOvertimeByMonth,
  useOvertimeSummary,
  useRecordOvertime,
  useUpdateOvertimeStatus
} from '@/hooks/useEmployeeManagement';
import { Overtime } from '@/services/employee/types';

const AdminOvertime = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated, isAdmin, user } = useAuth();

  // Get employee ID from URL if provided
  const employeeIdFromUrl = searchParams.get('employee');

  // State for date selection
  const today = new Date();
  const [selectedDate, setSelectedDate] = useState(format(today, 'yyyy-MM-dd'));
  const [selectedMonth, setSelectedMonth] = useState(format(today, 'yyyy-MM'));
  const [selectedYear, setSelectedYear] = useState(today.getFullYear().toString());
  const [selectedMonthNumber, setSelectedMonthNumber] = useState((today.getMonth() + 1).toString());
  const [activeTab, setActiveTab] = useState('record');
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(employeeIdFromUrl || '');

  // State for overtime record
  const [overtimeHours, setOvertimeHours] = useState('');
  const [overtimeRate, setOvertimeRate] = useState('1.5');
  const [overtimeCostPerHour, setOvertimeCostPerHour] = useState('');
  const [overtimeNotes, setOvertimeNotes] = useState('');

  // State for approval dialog
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [overtimeToApprove, setOvertimeToApprove] = useState<string | null>(null);
  const [approvalAction, setApprovalAction] = useState<'approved' | 'rejected'>('approved');

  // Fetch data
  const { data: employees, isLoading: isLoadingEmployees } = useEmployees();
  const { data: employeeOvertime, isLoading: isLoadingEmployeeOvertime } =
    useOvertimeByEmployee(selectedEmployeeId);
  const { data: monthlyOvertime, isLoading: isLoadingMonthlyOvertime } =
    useOvertimeByMonth(
      parseInt(selectedYear),
      parseInt(selectedMonthNumber)
    );
  const { data: overtimeSummary, isLoading: isLoadingOvertimeSummary } =
    useOvertimeSummary(
      parseInt(selectedYear),
      parseInt(selectedMonthNumber)
    );

  const recordOvertimeMutation = useRecordOvertime();
  const updateOvertimeStatusMutation = useUpdateOvertimeStatus();

  // Check authentication and admin status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/employees/overtime');
    } else if (!isAdmin()) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Handle month change
  useEffect(() => {
    const [year, month] = selectedMonth.split('-');
    setSelectedYear(year);
    setSelectedMonthNumber(month);
  }, [selectedMonth]);

  // Reset form after successful submission
  useEffect(() => {
    if (recordOvertimeMutation.isSuccess) {
      setOvertimeHours('');
      setOvertimeRate('1.5');
      setOvertimeCostPerHour('');
      setOvertimeNotes('');
    }
  }, [recordOvertimeMutation.isSuccess]);

  // Handle overtime record submission
  const handleRecordOvertime = async () => {
    if (!selectedEmployeeId || !overtimeHours) return;

    try {
      const overtime: Omit<Overtime, 'id' | 'created_at' | 'updated_at'> = {
        employee_id: selectedEmployeeId,
        date: selectedDate,
        hours: parseFloat(overtimeHours),
        rate_multiplier: parseFloat(overtimeRate),
        cost_per_hour: overtimeCostPerHour ? parseFloat(overtimeCostPerHour) : undefined,
        status: 'pending',
        notes: overtimeNotes,
        created_by: user?.id
      };

      await recordOvertimeMutation.mutateAsync(overtime);
    } catch (error) {
      console.error('Error recording overtime:', error);
    }
  };

  // Handle approval dialog open
  const handleApprovalClick = (id: string, action: 'approved' | 'rejected') => {
    setOvertimeToApprove(id);
    setApprovalAction(action);
    setApprovalDialogOpen(true);
  };

  // Handle approval confirmation
  const confirmApproval = async () => {
    if (overtimeToApprove && user?.id) {
      try {
        await updateOvertimeStatusMutation.mutateAsync({
          id: overtimeToApprove,
          status: approvalAction,
          approvedBy: user.id
        });
        setApprovalDialogOpen(false);
        setOvertimeToApprove(null);
      } catch (error) {
        console.error('Error updating overtime status:', error);
      }
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-6">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin/employees')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Employees
              </Button>
              <h1 className="text-2xl font-bold">Overtime Management</h1>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="record">Record Overtime</TabsTrigger>
                <TabsTrigger value="employee">Employee Overtime</TabsTrigger>
                <TabsTrigger value="approval">Approval Queue</TabsTrigger>
                <TabsTrigger value="summary">Monthly Summary</TabsTrigger>
              </TabsList>

              {/* Record Overtime Tab */}
              <TabsContent value="record">
                <Card>
                  <CardHeader>
                    <CardTitle>Record Overtime Hours</CardTitle>
                    <CardDescription>
                      Record overtime hours for an employee.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="employee-select">Select Employee</Label>
                        <Select
                          value={selectedEmployeeId}
                          onValueChange={setSelectedEmployeeId}
                        >
                          <SelectTrigger id="employee-select">
                            <SelectValue placeholder="Select an employee" />
                          </SelectTrigger>
                          <SelectContent>
                            {employees?.map(employee => (
                              <SelectItem key={employee.id} value={employee.id}>
                                {employee.first_name} {employee.last_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="overtime-date">Date</Label>
                        <Input
                          id="overtime-date"
                          type="date"
                          value={selectedDate}
                          onChange={(e) => setSelectedDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="overtime-hours">Hours</Label>
                        <Input
                          id="overtime-hours"
                          type="number"
                          min="0.5"
                          step="0.5"
                          value={overtimeHours}
                          onChange={(e) => setOvertimeHours(e.target.value)}
                          placeholder="Enter overtime hours"
                        />
                      </div>
                      <div>
                        <Label htmlFor="overtime-rate">Rate Multiplier</Label>
                        <Select
                          value={overtimeRate}
                          onValueChange={setOvertimeRate}
                        >
                          <SelectTrigger id="overtime-rate">
                            <SelectValue placeholder="Select rate" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1.5">1.5x (Standard)</SelectItem>
                            <SelectItem value="2">2x (Double)</SelectItem>
                            <SelectItem value="2.5">2.5x (Premium)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="overtime-cost">Cost Per Hour</Label>
                        <Input
                          id="overtime-cost"
                          type="number"
                          min="0"
                          step="0.01"
                          value={overtimeCostPerHour}
                          onChange={(e) => setOvertimeCostPerHour(e.target.value)}
                          placeholder="Enter cost per hour"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <Label htmlFor="overtime-notes">Notes</Label>
                        <Textarea
                          id="overtime-notes"
                          value={overtimeNotes}
                          onChange={(e) => setOvertimeNotes(e.target.value)}
                          placeholder="Enter reason for overtime"
                          rows={3}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button
                      onClick={handleRecordOvertime}
                      disabled={
                        recordOvertimeMutation.isPending ||
                        !selectedEmployeeId ||
                        !overtimeHours
                      }
                    >
                      {recordOvertimeMutation.isPending && (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      )}
                      <Save className="h-4 w-4 mr-2" />
                      Record Overtime
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              {/* Employee Overtime Tab */}
              <TabsContent value="employee">
                <Card>
                  <CardHeader>
                    <CardTitle>Employee Overtime History</CardTitle>
                    <CardDescription>
                      View overtime records for a specific employee.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="employee-history-select">Select Employee</Label>
                      <Select
                        value={selectedEmployeeId}
                        onValueChange={setSelectedEmployeeId}
                      >
                        <SelectTrigger id="employee-history-select">
                          <SelectValue placeholder="Select an employee" />
                        </SelectTrigger>
                        <SelectContent>
                          {employees?.map(employee => (
                            <SelectItem key={employee.id} value={employee.id}>
                              {employee.first_name} {employee.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {!selectedEmployeeId ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Please select an employee to view overtime records.</p>
                      </div>
                    ) : isLoadingEmployeeOvertime ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading overtime data...</span>
                      </div>
                    ) : employeeOvertime?.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No overtime records found for this employee.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Date</TableHead>
                              <TableHead>Hours</TableHead>
                              <TableHead>Rate</TableHead>
                              <TableHead>Cost/Hour</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Notes</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {employeeOvertime?.map((record) => (
                              <TableRow key={record.id}>
                                <TableCell>
                                  {format(parseISO(record.date), 'MMM d, yyyy')}
                                </TableCell>
                                <TableCell>{record.hours}</TableCell>
                                <TableCell>{record.rate_multiplier}x</TableCell>
                                <TableCell>{record.cost_per_hour ? `$${record.cost_per_hour.toFixed(2)}` : '-'}</TableCell>
                                <TableCell>
                                  <Badge
                                    variant="outline"
                                    className={getStatusBadgeColor(record.status)}
                                  >
                                    {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                                  </Badge>
                                </TableCell>
                                <TableCell>{record.notes || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Approval Queue Tab */}
              <TabsContent value="approval">
                <Card>
                  <CardHeader>
                    <CardTitle>Overtime Approval Queue</CardTitle>
                    <CardDescription>
                      Approve or reject pending overtime requests.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="approval-month">Filter by Month</Label>
                      <Input
                        id="approval-month"
                        type="month"
                        value={selectedMonth}
                        onChange={(e) => setSelectedMonth(e.target.value)}
                        className="w-48"
                      />
                    </div>

                    {isLoadingMonthlyOvertime ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading overtime data...</span>
                      </div>
                    ) : !monthlyOvertime || monthlyOvertime.filter(r => r.status === 'pending').length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No pending overtime requests found.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Employee</TableHead>
                              <TableHead>Date</TableHead>
                              <TableHead>Hours</TableHead>
                              <TableHead>Rate</TableHead>
                              <TableHead>Cost/Hour</TableHead>
                              <TableHead>Notes</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {monthlyOvertime
                              .filter(record => record.status === 'pending')
                              .map((record) => (
                                <TableRow key={record.id}>
                                  <TableCell className="font-medium">
                                    {record.employee_name}
                                  </TableCell>
                                  <TableCell>
                                    {format(parseISO(record.date), 'MMM d, yyyy')}
                                  </TableCell>
                                  <TableCell>{record.hours}</TableCell>
                                  <TableCell>{record.rate_multiplier}x</TableCell>
                                  <TableCell>{record.cost_per_hour ? `$${record.cost_per_hour.toFixed(2)}` : '-'}</TableCell>
                                  <TableCell>{record.notes || '-'}</TableCell>
                                  <TableCell className="text-right">
                                    <div className="flex justify-end gap-2">
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                        onClick={() => handleApprovalClick(record.id, 'approved')}
                                      >
                                        <Check className="h-4 w-4 mr-1" />
                                        Approve
                                      </Button>
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                        onClick={() => handleApprovalClick(record.id, 'rejected')}
                                      >
                                        <X className="h-4 w-4 mr-1" />
                                        Reject
                                      </Button>
                                    </div>
                                  </TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Monthly Summary Tab */}
              <TabsContent value="summary">
                <Card>
                  <CardHeader>
                    <CardTitle>Monthly Overtime Summary</CardTitle>
                    <CardDescription>
                      Overview of overtime for all employees in a month.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="summary-month">Select Month</Label>
                      <Input
                        id="summary-month"
                        type="month"
                        value={selectedMonth}
                        onChange={(e) => setSelectedMonth(e.target.value)}
                        className="w-48"
                      />
                    </div>

                    {isLoadingOvertimeSummary ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading summary data...</span>
                      </div>
                    ) : overtimeSummary?.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No overtime data found for this month.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Employee</TableHead>
                              <TableHead>Total Hours</TableHead>
                              <TableHead>Approved Hours</TableHead>
                              <TableHead>Pending Hours</TableHead>
                              <TableHead>Rejected Hours</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {overtimeSummary
                              ?.filter(summary => summary.total_hours > 0)
                              .map((summary) => (
                                <TableRow key={summary.employee_id}>
                                  <TableCell className="font-medium">
                                    {summary.employee_name}
                                  </TableCell>
                                  <TableCell>{summary.total_hours}</TableCell>
                                  <TableCell>{summary.approved_hours}</TableCell>
                                  <TableCell>{summary.pending_hours}</TableCell>
                                  <TableCell>{summary.rejected_hours}</TableCell>
                                </TableRow>
                              ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      <Footer />

      {/* Approval Confirmation Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {approvalAction === 'approved' ? 'Approve Overtime' : 'Reject Overtime'}
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to {approvalAction === 'approved' ? 'approve' : 'reject'} this overtime request?
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant={approvalAction === 'approved' ? 'default' : 'destructive'}
              onClick={confirmApproval}
              disabled={updateOvertimeStatusMutation.isPending}
            >
              {updateOvertimeStatusMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              {approvalAction === 'approved' ? 'Approve' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminOvertime;
