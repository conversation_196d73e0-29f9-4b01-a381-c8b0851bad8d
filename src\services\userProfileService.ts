import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface UserProfile {
  id: string;
  display_name: string;
  email: string;
  phone?: string;
  dob?: string;
  street?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
  avatar_url?: string;
  role: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Fetches the user profile from Supabase using the safe function
 * @param userId The user ID to fetch the profile for
 * @returns The user profile or null if not found
 */
export const getUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    console.log('Fetching profile for user:', userId);

    // Use the new safe function that bypasses R<PERSON> issues
    const { data, error } = await supabase.rpc('get_user_profile_safe', {
      user_id: userId
    });

    if (error) {
      console.error('Error fetching user profile via RPC:', error);

      // Fallback to direct query if RPC fails
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (fallbackError) {
        console.error('Fallback query also failed:', fallbackError);
        return null;
      }

      return fallbackData;
    }

    // RPC returns an array, get the first item
    if (data && data.length > 0) {
      console.log('Profile data retrieved successfully:', data[0]);
      return data[0];
    }

    console.log('No profile found for user:', userId);
    return null;
  } catch (error) {
    console.error('Exception in getUserProfile:', error);
    return null;
  }
};

/**
 * Creates a user profile if it doesn't exist
 * @param userId The user ID to create the profile for
 * @returns The created user profile or null if creation failed
 */
export const createUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    // Get user data from auth
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError) {
      console.error('Error getting user data:', userError);

      // Fallback to just creating a basic profile
      const { data: authUser } = await supabase.auth.getUser();

      if (!authUser || !authUser.user) {
        console.error('Could not get current user');
        return null;
      }

      const newProfile: Partial<UserProfile> = {
        id: userId,
        display_name: authUser.user.email?.split('@')[0] || 'User',
        email: authUser.user.email || '',
        role: 'user',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('user_profiles')
        .insert(newProfile)
        .select()
        .single();

      if (error) {
        console.error('Error creating user profile:', error);
        return null;
      }

      return data as UserProfile;
    }

    // Create profile with user data
    const user = userData.user;
    const newProfile: Partial<UserProfile> = {
      id: userId,
      display_name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
      email: user.email || '',
      role: 'user',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('user_profiles')
      .insert(newProfile)
      .select()
      .single();

    if (error) {
      console.error('Error creating user profile:', error);
      return null;
    }

    return data as UserProfile;
  } catch (error) {
    console.error('Error in createUserProfile:', error);
    return null;
  }
};

/**
 * Updates the user profile in Supabase
 * @param userId The user ID to update the profile for
 * @param profileData The profile data to update
 * @returns True if successful, false otherwise
 */
export const updateUserProfile = async (
  userId: string,
  profileData: Partial<UserProfile>
): Promise<boolean> => {
  try {
    console.log('Updating profile for user:', userId, 'with data:', profileData);

    // First try using the RPC function which has SECURITY DEFINER
    const { data: rpcResult, error: rpcError } = await supabase.rpc('update_user_profile', {
      user_id: userId,
      p_display_name: profileData.display_name,
      p_phone: profileData.phone,
      p_dob: profileData.dob ? new Date(profileData.dob) : null,
      p_street: profileData.street,
      p_city: profileData.city,
      p_state: profileData.state,
      p_postal_code: profileData.postal_code,
      p_country: profileData.country,
      p_avatar_url: profileData.avatar_url
    });

    // If RPC function exists and worked
    if (!rpcError && rpcResult === true) {
      console.log('Profile updated successfully via RPC function');
      toast({
        title: 'Profile updated',
        description: 'Your profile has been successfully updated',
      });
      return true;
    }

    // If RPC function failed or doesn't exist, fall back to direct update
    if (rpcError) {
      console.warn('RPC function failed, falling back to direct update:', rpcError);
    }

    // Check if profile exists first
    const { data: existingProfile, error: checkError } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', userId)
      .maybeSingle();

    // If profile doesn't exist, create it first
    if (checkError || !existingProfile) {
      console.log('Profile not found during update, creating it first...');
      const newProfile = await createUserProfile(userId);

      if (!newProfile) {
        console.error('Failed to create profile before update');
        toast({
          title: 'Error updating profile',
          description: 'Could not create your profile. Please try again.',
          variant: 'destructive',
        });
        return false;
      }
    }

    // Prepare the data for update
    const updateData = {
      ...profileData,
      updated_at: new Date().toISOString()
    };

    // Update the profile
    const { error } = await supabase
      .from('user_profiles')
      .update(updateData)
      .eq('id', userId);

    if (error) {
      console.error('Error updating user profile:', error);

      // Try upsert approach instead
      console.log('Update failed, trying upsert instead...');

      const upsertData = {
        id: userId,
        ...profileData,
        updated_at: new Date().toISOString()
      };

      const { error: upsertError } = await supabase
        .from('user_profiles')
        .upsert(upsertData)
        .select();

      if (upsertError) {
        console.error('Error upserting user profile:', upsertError);

        // Last resort: try direct insert
        if (upsertError.code === 'PGRST116' || upsertError.message.includes('0 rows')) {
          console.log('Upsert failed, trying direct insert...');

          const insertData = {
            id: userId,
            ...profileData,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          };

          const { error: insertError } = await supabase
            .from('user_profiles')
            .insert(insertData)
            .select();

          if (insertError) {
            console.error('Error inserting user profile:', insertError);
            toast({
              title: 'Error updating profile',
              description: insertError.message || 'An unexpected error occurred',
              variant: 'destructive',
            });
            return false;
          }

          toast({
            title: 'Profile created',
            description: 'Your profile has been successfully created',
          });

          return true;
        }

        toast({
          title: 'Error updating profile',
          description: upsertError.message || 'An unexpected error occurred',
          variant: 'destructive',
        });
        return false;
      }

      toast({
        title: 'Profile updated',
        description: 'Your profile has been successfully updated',
      });

      return true;
    }

    toast({
      title: 'Profile updated',
      description: 'Your profile has been successfully updated',
    });

    return true;
  } catch (error: any) {
    console.error('Error in updateUserProfile:', error);
    toast({
      title: 'Error updating profile',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return false;
  }
};
