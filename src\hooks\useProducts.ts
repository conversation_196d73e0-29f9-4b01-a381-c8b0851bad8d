/**
 * Products data fetching hook using React Query
 *
 * This hook provides optimized data fetching for products with caching,
 * background updates, and error handling.
 */
import { useQuery } from '@tanstack/react-query';
import { getInitialProducts } from '@/services/editorProductsService';
import { searchProducts } from '@/services/search';
import { Product } from '@/types/product';

// Query keys for React Query
export const productKeys = {
  all: ['products'] as const,
  lists: () => [...productKeys.all, 'list'] as const,
  list: (filters: any) => [...productKeys.lists(), filters] as const,
  details: () => [...productKeys.all, 'detail'] as const,
  detail: (id: string) => [...productKeys.details(), id] as const,
  search: (query: string) => [...productKeys.all, 'search', query] as const,
};

/**
 * Hook to fetch all products
 * @returns Query result with products data, loading state, and error
 */
export function useProducts() {
  return useQuery({
    queryKey: productKeys.lists(),
    queryFn: getInitialProducts,
    // Use global settings for better tab switching behavior
    // staleTime and refetchOnWindowFocus will be inherited from global config
  });
}

/**
 * Hook to fetch a single product by ID
 * @param id Product ID
 * @returns Query result with product data, loading state, and error
 */
export function useProduct(id: string) {
  const { data: products } = useProducts();

  return useQuery({
    queryKey: productKeys.detail(id),
    queryFn: () => {
      // If we already have the products, find the product by ID
      if (products) {
        const product = products.find(p => p.id === id);
        if (product) return Promise.resolve(product);
      }

      // Otherwise, fetch all products and find the one we need
      return getInitialProducts().then(allProducts => {
        const product = allProducts.find(p => p.id === id);
        if (!product) throw new Error(`Product with ID ${id} not found`);
        return product;
      });
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to search products
 * @param query Search query
 * @returns Query result with search results, loading state, and error
 */
export function useProductSearch(query: string) {
  return useQuery({
    queryKey: productKeys.search(query),
    queryFn: () => searchProducts(query),
    enabled: !!query && query.length > 0,
    // Use global settings for better tab switching behavior
    // staleTime and refetchOnWindowFocus will be inherited from global config
  });
}

/**
 * Hook to filter products by category
 * @param category Category to filter by
 * @returns Filtered products, loading state, and error
 */
export function useFilteredProducts(category: string | null) {
  const { data: products, isLoading, error } = useProducts();

  // Helper function to check if a product matches the category
  const matchesCategory = (productCategory: string, selectedCat: string) => {
    if (!selectedCat) return true;

    // Normalize category names for comparison
    const normalizedProductCategory = productCategory?.toLowerCase() || '';
    const normalizedSelectedCat = selectedCat.toLowerCase();
    const selectedCatNoFurniture = normalizedSelectedCat.replace(' furniture', '');

    // Exact match
    if (normalizedProductCategory === normalizedSelectedCat) return true;

    // Check if product category is part of selected category
    if (normalizedSelectedCat.includes(normalizedProductCategory)) return true;

    // Check if selected category without 'furniture' matches product category
    if (normalizedProductCategory.includes(selectedCatNoFurniture)) return true;

    // Special case for 'Living Room' vs 'Living Room Furniture'
    if (normalizedSelectedCat === 'living room furniture' && normalizedProductCategory === 'living room') return true;
    if (normalizedSelectedCat === 'bedroom furniture' && normalizedProductCategory === 'bedroom') return true;
    if (normalizedSelectedCat === 'dining room furniture' && normalizedProductCategory === 'dining room') return true;
    if (normalizedSelectedCat === 'home office furniture' && normalizedProductCategory === 'home office') return true;
    if (normalizedSelectedCat === 'storage solutions' && normalizedProductCategory === 'storage') return true;
    if (normalizedSelectedCat === 'kids & nursery furniture' &&
        (normalizedProductCategory === 'kids' || normalizedProductCategory === 'nursery' || normalizedProductCategory === 'kids & nursery')) return true;

    // Special case for the "Vibe & Decor" category
    if (normalizedSelectedCat === 'vibe & decor' &&
        (normalizedProductCategory === 'outdoor' ||
         normalizedProductCategory === 'outdoor furniture' ||
         normalizedProductCategory === 'accent' ||
         normalizedProductCategory === 'accent furniture' ||
         normalizedProductCategory === 'decorative' ||
         normalizedProductCategory === 'decorative accessories')) return true;

    return false;
  };

  // Filter products by category
  const filteredProducts = !category || !products
    ? products
    : products.filter(product =>
        matchesCategory(product.category, category) && product.status === 'active'
      );

  return {
    data: filteredProducts,
    isLoading,
    error
  };
}
