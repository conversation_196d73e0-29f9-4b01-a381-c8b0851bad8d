/*
  Product Indicators Styling
  This file contains styles for product indicators, color selectors, and image navigation dots
*/

/* Base styles for all product indicators */
.product-indicator {
  width: 0.5rem;  /* 8px - much smaller than before */
  height: 0.5rem; /* 8px - much smaller than before */
  border-radius: 50%;
  transition: all 0.2s ease;
  opacity: 0.7;
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Active indicator */
.product-indicator.active {
  transform: scale(1.2);
  opacity: 1;
}

/* Container for indicators */
.product-indicators-container {
  display: flex;
  gap: 0.375rem; /* 6px - smaller spacing for mobile */
  padding: 0.25rem 0.375rem; /* 4px 6px - more compact padding */
  border-radius: 0.75rem; /* 12px - slightly less rounded */
  background-color: rgba(0, 0, 0, 0.2); /* subtle background */
  -webkit-backdrop-filter: blur(4px); /* Safari support */
  backdrop-filter: blur(4px); /* slight blur effect */
}

/* Color indicators for product variants */
.color-indicator {
  width: 0.5rem;  /* 8px */
  height: 0.5rem; /* 8px */
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.5);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.color-indicator:hover,
.color-indicator.selected {
  transform: scale(1.2);
  border: 1px solid white;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  /* Smaller on mobile for better appearance */
  .product-indicator {
    width: 0.25rem;  /* 4px */
    height: 0.25rem; /* 4px */
    min-width: 0.25rem;
    min-height: 0.25rem;
  }

  .product-indicators-container {
    gap: 0.25rem; /* 4px */
    padding: 0.125rem 0.25rem; /* 2px 4px */
  }

  /* Color indicators for mobile */
  .color-indicator {
    width: 0.375rem;  /* 6px */
    height: 0.375rem; /* 6px */
  }
}

@media (min-width: 768px) {
  /* Slightly larger on desktop for better visibility */
  .product-indicator {
    width: 0.375rem;  /* 6px */
    height: 0.375rem; /* 6px */
  }

  .product-indicators-container {
    gap: 0.375rem; /* 6px */
    padding: 0.125rem 0.375rem; /* 2px 6px */
  }
}
