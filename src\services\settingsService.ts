/**
 * Settings Service
 * 
 * This service manages global application settings stored in Supabase.
 */
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface PaymentMethodSettings {
  cod_enabled: boolean;
  online_payment_enabled: boolean;
}

export interface StoreInfo {
  name: string;
  email: string;
  phone: string;
}

export interface ShippingSettings {
  free_shipping_threshold: number;
  domestic_fee: number;
  estimated_days: string;
}

export interface AppSettings {
  payment_methods: PaymentMethodSettings;
  store_info: StoreInfo;
  shipping_settings: ShippingSettings;
}

/**
 * Get a specific setting by key
 */
export const getSetting = async (settingKey: string): Promise<any> => {
  try {
    const { data, error } = await supabase.rpc('get_app_setting', {
      p_setting_key: settingKey
    });

    if (error) {
      console.error(`Error fetching setting ${settingKey}:`, error);
      return null;
    }

    return data;
  } catch (error) {
    console.error(`Error in getSetting for ${settingKey}:`, error);
    return null;
  }
};

/**
 * Update a specific setting
 */
export const updateSetting = async (
  settingKey: string, 
  settingValue: any, 
  description?: string
): Promise<boolean> => {
  try {
    const { data, error } = await supabase.rpc('update_app_setting', {
      p_setting_key: settingKey,
      p_setting_value: settingValue,
      p_description: description
    });

    if (error) {
      console.error(`Error updating setting ${settingKey}:`, error);
      toast({
        title: 'Error',
        description: `Failed to update ${settingKey} setting`,
        variant: 'destructive'
      });
      return false;
    }

    return data === true;
  } catch (error) {
    console.error(`Error in updateSetting for ${settingKey}:`, error);
    toast({
      title: 'Error',
      description: `Failed to update ${settingKey} setting`,
      variant: 'destructive'
    });
    return false;
  }
};

/**
 * Get payment method settings
 */
export const getPaymentMethodSettings = async (): Promise<PaymentMethodSettings> => {
  const settings = await getSetting('payment_methods');
  return settings || { cod_enabled: true, online_payment_enabled: true };
};

/**
 * Update payment method settings
 */
export const updatePaymentMethodSettings = async (settings: PaymentMethodSettings): Promise<boolean> => {
  return await updateSetting('payment_methods', settings, 'Payment method availability settings');
};

/**
 * Check if COD is enabled
 */
export const isCODEnabled = async (): Promise<boolean> => {
  const settings = await getPaymentMethodSettings();
  return settings.cod_enabled === true;
};

/**
 * Toggle COD availability
 */
export const toggleCOD = async (enabled: boolean): Promise<boolean> => {
  const currentSettings = await getPaymentMethodSettings();
  const newSettings = {
    ...currentSettings,
    cod_enabled: enabled
  };
  
  const success = await updatePaymentMethodSettings(newSettings);
  
  if (success) {
    toast({
      title: 'Settings Updated',
      description: `Cash on Delivery has been ${enabled ? 'enabled' : 'disabled'}`,
    });
  }
  
  return success;
};

/**
 * Get store information
 */
export const getStoreInfo = async (): Promise<StoreInfo> => {
  const settings = await getSetting('store_info');
  return settings || { 
    name: 'The Badhees', 
    email: '<EMAIL>', 
    phone: '+91-XXXXXXXXXX' 
  };
};

/**
 * Update store information
 */
export const updateStoreInfo = async (storeInfo: StoreInfo): Promise<boolean> => {
  return await updateSetting('store_info', storeInfo, 'Store basic information');
};

/**
 * Get shipping settings
 */
export const getShippingSettings = async (): Promise<ShippingSettings> => {
  const settings = await getSetting('shipping_settings');
  return settings || { 
    free_shipping_threshold: 499, 
    domestic_fee: 50, 
    estimated_days: '3-5' 
  };
};

/**
 * Update shipping settings
 */
export const updateShippingSettings = async (shippingSettings: ShippingSettings): Promise<boolean> => {
  return await updateSetting('shipping_settings', shippingSettings, 'Shipping configuration');
};

/**
 * Get all settings
 */
export const getAllSettings = async (): Promise<Partial<AppSettings>> => {
  try {
    const [paymentMethods, storeInfo, shippingSettings] = await Promise.all([
      getPaymentMethodSettings(),
      getStoreInfo(),
      getShippingSettings()
    ]);

    return {
      payment_methods: paymentMethods,
      store_info: storeInfo,
      shipping_settings: shippingSettings
    };
  } catch (error) {
    console.error('Error fetching all settings:', error);
    return {};
  }
};
