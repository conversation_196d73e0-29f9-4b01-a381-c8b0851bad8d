
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { ProjectType } from '@/types/project';
import CompletedProjectForm from '@/components/admin/CompletedProjectForm';
import { createCustomProject, CustomProject } from '@/services/customProjectService';

const AdminCompletedProjectNew = () => {
  const navigate = useNavigate();

  const [isSaving, setIsSaving] = useState(false);

  // Empty project template for new projects
  const emptyProject: Partial<CustomProject> = {
    name: '',
    description: '',
    category: '',
    budget: 0,
    completion_date: new Date().toISOString().split('T')[0],
    client_name: '',
    location: '',
    materials_used: '',
    image_urls: [],
    status: 'draft',
  };

  const handleSaveProject = async (project: CustomProject) => {
    setIsSaving(true);
    try {
      // Save project to Supabase
      const savedProject = await createCustomProject(project);

      if (savedProject) {
        toast({
          title: "Success",
          description: "Custom project created successfully",
        });

        navigate('/admin/completed-projects');
      } else {
        throw new Error('Failed to create project');
      }
    } catch (error: any) {
      console.error('Error creating project:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create project",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/completed-projects');
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-6">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin/completed-projects')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
              <h1 className="text-2xl font-bold">Add New Custom Project</h1>
            </div>

            <div className="bg-white shadow-sm rounded-lg p-6">
              <CompletedProjectForm
                initialData={emptyProject as ProjectType}
                onSave={handleSaveProject}
                onCancel={handleCancel}
              />
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminCompletedProjectNew;
