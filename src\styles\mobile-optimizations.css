/*
  Mobile Optimizations
  This file contains CSS optimizations specifically for mobile devices
*/

/* Safe area insets for notched devices */
.safe-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.safe-top {
  padding-top: env(safe-area-inset-top, 0);
}

/* Improved touch targets */
.touch-target {
  min-height: 48px;
  min-width: 48px;
}

/* Apply touch target size to all interactive elements on mobile */
@media (max-width: 640px) {
  button,
  .btn,
  a[role="button"],
  .clickable,
  input[type="radio"],
  select {
    min-height: 44px;
    min-width: 44px;
  }

  /* Smaller checkbox for login/signup forms */
  input[type="checkbox"] {
    min-height: 16px;
    min-width: 16px;
    height: 16px;
    width: 16px;
  }

  /* Increase spacing between interactive elements */
  .mobile-touch-spacing > * + * {
    margin-top: 12px;
  }
}

/* Prevent text size adjustment on orientation change
   Note: We're using a different approach to avoid browser compatibility warnings */
html {
  font-size: 100%; /* This achieves the same effect without browser warnings */
}

/* Prevent pull-to-refresh on iOS */
@supports (overscroll-behavior-y: contain) {
  html, body {
    overscroll-behavior-y: contain;
  }
}

/* Improve tap highlight color */
* {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* Optimize scrolling */
.optimize-scroll {
  scroll-behavior: smooth;
}

/* Prevent iOS zooming on form elements */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="number"],
input[type="password"],
textarea,
select {
  font-size: 16px; /* Prevents iOS zoom */
}

/* Improve button press states */
button,
a.btn,
[role="button"] {
  touch-action: manipulation;
}

/* Optimize images for high-DPI screens */
img {
  /* Modern approach: let the browser handle it */
  image-rendering: auto;
}

/* Prevent content shift when keyboard appears */
@media (max-width: 640px) {
  .keyboard-aware {
    padding-bottom: 40vh;
  }
}

/* Improve form field focus states for touch */
input:focus,
textarea:focus,
select:focus {
  outline-offset: 2px;
}

/* Optimize mobile grid layouts */
@media (max-width: 640px) {
  .mobile-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }

  .mobile-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .mobile-full-width {
    width: 100% !important;
    max-width: 100% !important;
  }

  .mobile-padding {
    padding-left: 16px !important;
    padding-right: 16px !important;
  }

  .mobile-margin {
    margin-left: 16px !important;
    margin-right: 16px !important;
  }

  .mobile-stack {
    display: flex !important;
    flex-direction: column !important;
  }

  .mobile-stack > * {
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 12px !important;
  }

  .mobile-stack > *:last-child {
    margin-bottom: 0 !important;
  }

  .mobile-center {
    text-align: center !important;
    justify-content: center !important;
    align-items: center !important;
  }

  .mobile-hidden {
    display: none !important;
  }

  .mobile-visible {
    display: block !important;
  }
}

/* Optimize for dark mode */
@media (prefers-color-scheme: dark) {
  .dark-mode-aware {
    background-color: #121212;
    color: #f5f5f5;
  }
}

/* Optimize for reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Optimize for landscape mode */
@media (orientation: landscape) and (max-width: 900px) {
  .landscape-optimize {
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* Improve mobile scrollbars */
::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

/* Optimize for mobile network conditions */
@media (prefers-reduced-data: reduce) {
  img:not([loading="eager"]) {
    /* Use loading="lazy" attribute instead of content-visibility */
    opacity: 0.9; /* Placeholder style that doesn't affect appearance much */
  }
}

/* Improve mobile form elements */
@media (max-width: 640px) {
  select,
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  input[type="number"],
  input[type="password"],
  textarea {
    font-size: 16px; /* Prevents iOS zoom */
    padding: 12px 16px;
    min-height: 48px;
    border-radius: 8px;
  }

  select {
    background-position: right 16px center;
    padding-right: 40px;
  }

  /* Increase form spacing on mobile */
  .form-group + .form-group {
    margin-top: 16px;
  }

  /* Improve form labels on mobile */
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
  }
}

/* Improve mobile buttons */
button,
.btn {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

@media (max-width: 640px) {
  button,
  .btn,
  [role="button"],
  a.button,
  input[type="button"],
  input[type="submit"] {
    min-height: 48px;
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    transition: all 0.2s ease;
  }

  /* Add active state for touch feedback */
  button:active,
  .btn:active,
  [role="button"]:active,
  a.button:active,
  input[type="button"]:active,
  input[type="submit"]:active {
    transform: scale(0.98);
    opacity: 0.9;
  }

  /* Ensure buttons have proper spacing */
  button + button,
  .btn + .btn {
    margin-left: 12px;
  }

  /* For stacked buttons on mobile */
  .mobile-stack-buttons button,
  .mobile-stack-buttons .btn {
    width: 100%;
    margin-left: 0;
    margin-bottom: 12px;
  }
}

/* Image indicator buttons with optimized touch area */
.image-indicator-btn {
  padding: 2px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Make indicators even smaller on mobile */
@media (max-width: 640px) {
  .image-indicator-btn {
    width: 16px;
    height: 16px;
    padding: 1px;
  }

  /* When used with product-indicator class, ensure small size */
  .image-indicator-btn.product-indicator {
    width: 8px !important;
    height: 8px !important;
    min-width: 8px !important;
    min-height: 8px !important;
    padding: 0 !important;
  }
}

.image-indicator-btn:active {
  transform: scale(0.95);
  background-color: rgba(255, 255, 255, 0.9);
}

/* Dot indicators for image sliders - mobile optimized */
.mobile-dot-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  padding: 12px 0;
}

.mobile-dot-indicators .dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.mobile-dot-indicators .dot.active {
  background-color: rgba(0, 0, 0, 0.8);
  transform: scale(1.2);
}

/* Improve mobile links */
a {
  text-decoration: underline;
  text-underline-offset: 0.15em;
}

/* Improve mobile text selection */
::selection {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Mobile Bottom Navigation Optimizations */
.mobile-bottom-nav {
  pointer-events: auto !important;
  touch-action: manipulation !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.mobile-bottom-nav a {
  pointer-events: auto !important;
  touch-action: manipulation !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1) !important;
  cursor: pointer !important;
}

.mobile-bottom-nav a:active {
  background-color: rgba(0, 0, 0, 0.05) !important;
  transform: scale(0.98) !important;
}

/* Ensure mobile nav is always on top */
@media (max-width: 768px) {
  .mobile-bottom-nav {
    z-index: 9998 !important;
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
  }
}
