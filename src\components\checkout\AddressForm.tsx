import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Address } from '@/services/addressService';

interface AddressFormProps {
  initialAddress?: Address;
  onSubmit: (address: Address) => void;
  onCancel?: () => void;
  submitLabel?: string;
  showDefaultOptions?: boolean;
}

const AddressForm = ({
  initialAddress,
  onSubmit,
  onCancel,
  submitLabel = 'Save Address',
  showDefaultOptions = true,
}: AddressFormProps) => {
  const [address, setAddress] = useState<Address>({
    name: '',
    street: '',
    city: '',
    state: '',
    postal_code: '',
    country: '',
    phone: '',
    is_default_shipping: false,
    is_default_billing: false,
    ...initialAddress,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (initialAddress) {
      setAddress({
        name: '',
        street: '',
        city: '',
        state: '',
        postal_code: '',
        country: '',
        phone: '',
        is_default_shipping: false,
        is_default_billing: false,
        ...initialAddress,
      });
    }
  }, [initialAddress]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setAddress(prev => ({ ...prev, [name]: value }));
    
    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleCheckboxChange = (name: string, checked: boolean) => {
    setAddress(prev => ({ ...prev, [name]: checked }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    if (!address.name.trim()) {
      newErrors.name = 'Full name is required';
    }
    
    if (!address.street.trim()) {
      newErrors.street = 'Street address is required';
    }
    
    if (!address.city.trim()) {
      newErrors.city = 'City is required';
    }
    
    if (!address.state.trim()) {
      newErrors.state = 'State/Province is required';
    }
    
    if (!address.postal_code.trim()) {
      newErrors.postal_code = 'Postal code is required';
    }
    
    if (!address.country.trim()) {
      newErrors.country = 'Country is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      onSubmit(address);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="name">Full Name</Label>
        <Input
          id="name"
          name="name"
          value={address.name}
          onChange={handleChange}
          placeholder="Full Name"
          className={errors.name ? 'border-red-500' : ''}
        />
        {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
      </div>
      
      <div>
        <Label htmlFor="street">Street Address</Label>
        <Input
          id="street"
          name="street"
          value={address.street}
          onChange={handleChange}
          placeholder="Street Address"
          className={errors.street ? 'border-red-500' : ''}
        />
        {errors.street && <p className="text-red-500 text-sm mt-1">{errors.street}</p>}
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="city">City</Label>
          <Input
            id="city"
            name="city"
            value={address.city}
            onChange={handleChange}
            placeholder="City"
            className={errors.city ? 'border-red-500' : ''}
          />
          {errors.city && <p className="text-red-500 text-sm mt-1">{errors.city}</p>}
        </div>
        
        <div>
          <Label htmlFor="state">State/Province</Label>
          <Input
            id="state"
            name="state"
            value={address.state}
            onChange={handleChange}
            placeholder="State/Province"
            className={errors.state ? 'border-red-500' : ''}
          />
          {errors.state && <p className="text-red-500 text-sm mt-1">{errors.state}</p>}
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="postal_code">Postal Code</Label>
          <Input
            id="postal_code"
            name="postal_code"
            value={address.postal_code}
            onChange={handleChange}
            placeholder="Postal Code"
            className={errors.postal_code ? 'border-red-500' : ''}
          />
          {errors.postal_code && <p className="text-red-500 text-sm mt-1">{errors.postal_code}</p>}
        </div>
        
        <div>
          <Label htmlFor="country">Country</Label>
          <Input
            id="country"
            name="country"
            value={address.country}
            onChange={handleChange}
            placeholder="Country"
            className={errors.country ? 'border-red-500' : ''}
          />
          {errors.country && <p className="text-red-500 text-sm mt-1">{errors.country}</p>}
        </div>
      </div>
      
      <div>
        <Label htmlFor="phone">Phone Number (Optional)</Label>
        <Input
          id="phone"
          name="phone"
          value={address.phone || ''}
          onChange={handleChange}
          placeholder="Phone Number"
        />
      </div>
      
      {showDefaultOptions && (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_default_shipping"
              checked={address.is_default_shipping}
              onCheckedChange={(checked) => 
                handleCheckboxChange('is_default_shipping', checked as boolean)
              }
            />
            <Label htmlFor="is_default_shipping" className="cursor-pointer">
              Set as default shipping address
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="is_default_billing"
              checked={address.is_default_billing}
              onCheckedChange={(checked) => 
                handleCheckboxChange('is_default_billing', checked as boolean)
              }
            />
            <Label htmlFor="is_default_billing" className="cursor-pointer">
              Set as default billing address
            </Label>
          </div>
        </div>
      )}
      
      <div className="flex justify-end space-x-2 pt-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}
        <Button type="submit">{submitLabel}</Button>
      </div>
    </form>
  );
};

export default AddressForm;
