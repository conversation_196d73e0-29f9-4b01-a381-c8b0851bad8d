
import React from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { Product } from '@/services/editorProductsService';
import { createProduct } from '@/services/supabaseProductsService';
import EditorProductForm from '@/components/editor/EditorProductForm';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card } from '@/components/ui/card';

const AdminProductAdd = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  const emptyProduct: Product = {
    id: '',
    name: '',
    description: '',
    price: 0,
    image: '',
    category: '',
    status: 'active', // Set new products to active by default
    stock: 0,
    sku: '',
    specifications: {},
    customizationAvailable: false,
  };

  // Check authentication and permissions
  React.useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/products/new');
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive"
      });
    } else if (!isAdmin()) {
      navigate('/');
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive"
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  const handleSaveProduct = async (product: Product) => {
    try {
      // Ensure required fields are set
      const newProduct = {
        ...product,
        status: product.status || 'active', // Ensure status is set
        category: product.category ? product.category.trim() : '',
      };

      // Save product to Supabase
      await createProduct(newProduct);

      // Show success message
      toast({
        title: "Product Added",
        description: `${product.name} has been added successfully.`,
      });

      // Redirect to products list
      navigate('/admin/products');
    } catch (error: any) {
      console.error('Error adding product:', error);
      toast({
        title: "Error Adding Product",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive"
      });
    }
  };

  const handleCancel = () => {
    navigate('/admin/products');
  };

  if (!isAuthenticated || !isAdmin()) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex flex-1 pt-20">
        <AdminSidebar />

        <div className="flex-1 p-6 overflow-auto bg-gray-50">
          {/* Breadcrumb */}
          <Breadcrumb className="mb-6">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin/products">Products</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Add New Product</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <Card className="bg-white p-6 shadow-sm border">
            <h1 className="text-2xl font-bold text-badhees-800 mb-6">Add New Product</h1>

            <EditorProductForm
              product={emptyProduct}
              onSave={handleSaveProduct}
              onCancel={handleCancel}
            />
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminProductAdd;
