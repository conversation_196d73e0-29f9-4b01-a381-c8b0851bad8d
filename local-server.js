/**
 * Simple Local Development Server for Testing Payment APIs
 *
 * This server simulates the Vercel environment for local testing
 * Run with: node local-server.js
 */

import http from 'http';
import url from 'url';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const PORT = process.env.VITE_SERVER_PORT || 3001;

// Helper function to parse JSON body
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// Helper function to set CORS headers
function setCORSHeaders(res, origin) {
  if (origin && (
    origin.includes('thebadhees.com') ||
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('vercel.app')
  )) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token, Origin');
  res.setHeader('Access-Control-Max-Age', '86400');
}

// Create HTTP server
const server = http.createServer(async (req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const { pathname, query } = parsedUrl;
  const origin = req.headers.origin;

  console.log(`${new Date().toISOString()} - ${req.method} ${pathname}`);

  // Set CORS headers for all requests
  setCORSHeaders(res, origin);

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    console.log('✅ Handling OPTIONS preflight request');
    res.writeHead(200);
    res.end();
    return;
  }

  try {
    // Health check endpoint
    if (pathname === '/api/health' && req.method === 'GET') {
      const response = {
        success: true,
        message: 'Local development server is running',
        timestamp: new Date().toISOString(),
        environment: {
          RAZORPAY_KEY_ID: !!process.env.RAZORPAY_KEY_ID,
          RAZORPAY_SECRET: !!process.env.RAZORPAY_SECRET,
          VITE_SUPABASE_URL: !!process.env.VITE_SUPABASE_URL,
          VITE_SUPABASE_ANON_KEY: !!process.env.VITE_SUPABASE_ANON_KEY
        }
      };
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(response, null, 2));
      return;
    }

    // API Routes
    if (pathname === '/api/razorpay/create-order' && req.method === 'POST') {
      try {
        const body = await parseBody(req);
        console.log('📋 Create order request body:', body);

        // Import and call the create-order handler
        const createOrderModule = await import('./api/razorpay/create-order.js');

        // Create Vercel-like req/res objects
        const vercelReq = {
          method: req.method,
          headers: req.headers,
          body: body,
          query: query
        };

        const vercelRes = {
          status: (code) => {
            res.statusCode = code;
            return vercelRes;
          },
          json: (data) => {
            res.writeHead(res.statusCode || 200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(data));
            return vercelRes;
          },
          end: (data) => {
            res.end(data);
            return vercelRes;
          },
          setHeader: (name, value) => {
            res.setHeader(name, value);
            return vercelRes;
          }
        };

        await createOrderModule.default(vercelReq, vercelRes);
        return;
      } catch (error) {
        console.error('❌ Create order error:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'Failed to create order',
          details: error.message
        }));
        return;
      }
    }

    // Verify payment route
    if (pathname === '/api/razorpay/verify-payment' && req.method === 'POST') {
      try {
        const body = await parseBody(req);
        console.log('📋 Verify payment request body:', body);

        // Import and call the verify-payment handler
        const verifyPaymentModule = await import('./api/razorpay/verify-payment.js');

        // Create Vercel-like req/res objects
        const vercelReq = {
          method: req.method,
          headers: req.headers,
          body: body,
          query: query
        };

        const vercelRes = {
          status: (code) => {
            res.statusCode = code;
            return vercelRes;
          },
          json: (data) => {
            res.writeHead(res.statusCode || 200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify(data));
            return vercelRes;
          },
          end: (data) => {
            res.end(data);
            return vercelRes;
          },
          setHeader: (name, value) => {
            res.setHeader(name, value);
            return vercelRes;
          }
        };

        await verifyPaymentModule.default(vercelReq, vercelRes);
        return;
      } catch (error) {
        console.error('❌ Verify payment error:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
          success: false,
          error: 'Failed to verify payment',
          details: error.message
        }));
        return;
      }
    }

    // CORS test route
    if (pathname === '/api/cors-test' && req.method === 'GET') {
      const response = {
        success: true,
        message: 'CORS test successful',
        timestamp: new Date().toISOString(),
        origin: origin,
        method: req.method
      };
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(response, null, 2));
      return;
    }

    // 404 handler
    res.writeHead(404, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      error: 'Not found',
      path: pathname
    }));

  } catch (error) {
    console.error('❌ Server Error:', error);
    res.writeHead(500, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({
      success: false,
      error: 'Internal server error',
      details: error.message
    }));
  }
});

// Start server
server.listen(PORT, () => {
  console.log('🚀 Local development server started');
  console.log(`📍 Server running on http://localhost:${PORT}`);
  console.log('🔧 Environment variables:');
  console.log(`   RAZORPAY_KEY_ID: ${process.env.RAZORPAY_KEY_ID ? '✅ Set' : '❌ Missing'}`);
  console.log(`   RAZORPAY_SECRET: ${process.env.RAZORPAY_SECRET ? '✅ Set' : '❌ Missing'}`);
  console.log(`   VITE_SUPABASE_URL: ${process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing'}`);
  console.log(`   VITE_SUPABASE_ANON_KEY: ${process.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}`);
  console.log('');
  console.log('🧪 Test endpoints:');
  console.log(`   GET  http://localhost:${PORT}/api/health`);
  console.log(`   GET  http://localhost:${PORT}/api/cors-test`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/create-order`);
  console.log(`   POST http://localhost:${PORT}/api/razorpay/verify-payment`);
  console.log('');
  console.log('💡 Start your frontend with: npm run dev');
  console.log('🔗 Or use: npm run dev:local (to start both together)');
});
