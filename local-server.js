/**
 * Local Development Server for Testing Payment APIs
 * 
 * This server simulates the Vercel environment for local testing
 * Run with: node local-server.js
 */

import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const app = express();
const PORT = process.env.VITE_SERVER_PORT || 3001;

// Middleware
app.use(cors({
  origin: ['http://localhost:8080', 'http://localhost:5173', 'http://127.0.0.1:8080'],
  credentials: true
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Body:', JSON.stringify(req.body, null, 2));
  }
  next();
});

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Local development server is running',
    timestamp: new Date().toISOString(),
    environment: {
      RAZORPAY_KEY_ID: !!process.env.RAZORPAY_KEY_ID,
      RAZORPAY_SECRET: !!process.env.RAZORPAY_SECRET,
      VITE_SUPABASE_URL: !!process.env.VITE_SUPABASE_URL,
      VITE_SUPABASE_ANON_KEY: !!process.env.VITE_SUPABASE_ANON_KEY
    }
  });
});

// Import and use API routes
async function loadApiRoutes() {
  try {
    // Dynamically import the API routes
    const createOrderModule = await import('./api/razorpay/create-order.js');
    const verifyPaymentModule = await import('./api/razorpay/verify-payment.js');
    const corsTestModule = await import('./api/cors-test.js');

    // Create wrapper functions that match Express middleware signature
    const wrapVercelHandler = (handler) => {
      return async (req, res) => {
        // Create Vercel-like req/res objects
        const vercelReq = {
          ...req,
          method: req.method,
          headers: req.headers,
          body: req.body,
          query: req.query
        };

        const vercelRes = {
          status: (code) => {
            res.status(code);
            return vercelRes;
          },
          json: (data) => {
            res.json(data);
            return vercelRes;
          },
          end: (data) => {
            res.end(data);
            return vercelRes;
          },
          setHeader: (name, value) => {
            res.setHeader(name, value);
            return vercelRes;
          }
        };

        try {
          await handler.default(vercelReq, vercelRes);
        } catch (error) {
          console.error('API Handler Error:', error);
          if (!res.headersSent) {
            res.status(500).json({
              success: false,
              error: 'Internal server error',
              details: error.message
            });
          }
        }
      };
    };

    // Register API routes
    app.post('/api/razorpay/create-order', wrapVercelHandler(createOrderModule));
    app.post('/api/razorpay/verify-payment', wrapVercelHandler(verifyPaymentModule));
    app.get('/api/cors-test', wrapVercelHandler(corsTestModule));
    app.options('/api/razorpay/*', wrapVercelHandler(corsTestModule));

    console.log('✅ API routes loaded successfully');
  } catch (error) {
    console.error('❌ Failed to load API routes:', error);
  }
}

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    details: error.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not found',
    path: req.url
  });
});

// Start server
async function startServer() {
  await loadApiRoutes();
  
  app.listen(PORT, () => {
    console.log('🚀 Local development server started');
    console.log(`📍 Server running on http://localhost:${PORT}`);
    console.log('🔧 Environment variables:');
    console.log(`   RAZORPAY_KEY_ID: ${process.env.RAZORPAY_KEY_ID ? '✅ Set' : '❌ Missing'}`);
    console.log(`   RAZORPAY_SECRET: ${process.env.RAZORPAY_SECRET ? '✅ Set' : '❌ Missing'}`);
    console.log(`   VITE_SUPABASE_URL: ${process.env.VITE_SUPABASE_URL ? '✅ Set' : '❌ Missing'}`);
    console.log(`   VITE_SUPABASE_ANON_KEY: ${process.env.VITE_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}`);
    console.log('');
    console.log('🧪 Test endpoints:');
    console.log(`   GET  http://localhost:${PORT}/api/health`);
    console.log(`   GET  http://localhost:${PORT}/api/cors-test`);
    console.log(`   POST http://localhost:${PORT}/api/razorpay/create-order`);
    console.log(`   POST http://localhost:${PORT}/api/razorpay/verify-payment`);
    console.log('');
    console.log('💡 Start your frontend with: npm run dev');
  });
}

startServer().catch(console.error);
