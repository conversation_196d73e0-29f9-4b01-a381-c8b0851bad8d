<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="180" height="180" viewBox="0 0 180 180" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- White background with rounded corners for iOS -->
  <rect width="180" height="180" rx="45" fill="white"/>

  <!-- The Badhees Logo - Updated apple touch icon -->
  <rect x="20" y="20" width="140" height="140" rx="12" ry="12" fill="#8B4513" stroke="#654321" stroke-width="2"/>
  <rect x="35" y="35" width="110" height="110" rx="8" ry="8" fill="white"/>
  <rect x="40" y="40" width="100" height="100" rx="6" ry="6" fill="none" stroke="#8B4513" stroke-width="1"/>

  <!-- Central "B" letter design -->
  <g transform="translate(90, 90)">
    <!-- Letter B -->
    <path d="M-30 -35 L-30 35 L15 35 C25 35 32 28 32 18 C32 10 27 3 20 0 C27 -3 32 -10 32 -18 C32 -28 25 -35 15 -35 L-30 -35 Z M-15 -20 L10 -20 C15 -20 18 -17 18 -12 C18 -7 15 -4 10 -4 L-15 -4 Z M-15 10 L15 10 C20 10 23 13 23 18 C23 23 20 26 15 26 L-15 26 Z" fill="#8B4513"/>

    <!-- Decorative elements -->
    <circle cx="-40" cy="-30" r="4" fill="#D2691E"/>
    <circle cx="40" cy="-30" r="4" fill="#D2691E"/>
    <circle cx="-40" cy="30" r="4" fill="#D2691E"/>
    <circle cx="40" cy="30" r="4" fill="#D2691E"/>
  </g>
</svg>
