/**
 * Performance monitoring utility for tracking and logging performance metrics
 */

// Store performance marks and measures
const performanceMarks: Record<string, number> = {};

/**
 * Start timing an operation
 * @param operationName Name of the operation to time
 */
export const startTiming = (operationName: string): void => {
  if (typeof window === 'undefined' || !window.performance) return;
  
  performanceMarks[operationName] = performance.now();
};

/**
 * End timing an operation and log the result
 * @param operationName Name of the operation to end timing for
 * @param logLevel Log level (log, warn, error)
 * @param warnThreshold Threshold in ms to trigger a warning
 * @param errorThreshold Threshold in ms to trigger an error
 */
export const endTiming = (
  operationName: string,
  logLevel: 'log' | 'warn' | 'error' = 'log',
  warnThreshold = 1000, // 1 second
  errorThreshold = 3000 // 3 seconds
): number | null => {
  if (typeof window === 'undefined' || !window.performance) return null;
  
  const startTime = performanceMarks[operationName];
  if (!startTime) {
    console.warn(`No start time found for operation: ${operationName}`);
    return null;
  }
  
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  // Remove the mark to avoid memory leaks
  delete performanceMarks[operationName];
  
  // Determine log level based on duration
  let actualLogLevel = logLevel;
  if (duration > errorThreshold) {
    actualLogLevel = 'error';
  } else if (duration > warnThreshold) {
    actualLogLevel = 'warn';
  }
  
  // Log the result
  switch (actualLogLevel) {
    case 'error':
      console.error(`⚠️ Performance issue: ${operationName} took ${duration.toFixed(2)}ms`);
      break;
    case 'warn':
      console.warn(`⚠️ Performance warning: ${operationName} took ${duration.toFixed(2)}ms`);
      break;
    default:
      console.log(`📊 Performance: ${operationName} took ${duration.toFixed(2)}ms`);
  }
  
  return duration;
};

/**
 * Measure the performance of an async function
 * @param fn Function to measure
 * @param operationName Name of the operation
 * @param warnThreshold Threshold in ms to trigger a warning
 * @param errorThreshold Threshold in ms to trigger an error
 * @returns The result of the function
 */
export async function measureAsync<T>(
  fn: () => Promise<T>,
  operationName: string,
  warnThreshold = 1000,
  errorThreshold = 3000
): Promise<T> {
  startTiming(operationName);
  try {
    return await fn();
  } finally {
    endTiming(operationName, 'log', warnThreshold, errorThreshold);
  }
}

/**
 * Create a performance-monitored version of an async function
 * @param fn Function to monitor
 * @param operationName Name of the operation
 * @param warnThreshold Threshold in ms to trigger a warning
 * @param errorThreshold Threshold in ms to trigger an error
 * @returns A wrapped function that measures performance
 */
export function createMonitoredFunction<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  operationName: string,
  warnThreshold = 1000,
  errorThreshold = 3000
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
  return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    return measureAsync(
      () => fn(...args),
      operationName,
      warnThreshold,
      errorThreshold
    );
  };
}
