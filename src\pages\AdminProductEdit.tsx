
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { Product } from '@/services/editorProductsService';
import { getProductById, updateProduct, deleteProduct } from '@/services/supabaseProductsService';
import EditorProductForm from '@/components/editor/EditorProductForm';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const AdminProductEdit = () => {
  const { productId } = useParams<{ productId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  const [product, setProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Check authentication and permissions
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/products');
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive"
      });
    } else if (!isAdmin()) {
      navigate('/');
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive"
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Load product data
  useEffect(() => {
    const loadProduct = async () => {
      setIsLoading(true);

      try {
        if (!productId) {
          setNotFound(true);
          return;
        }

        const foundProduct = await getProductById(productId);

        if (foundProduct) {
          setProduct(foundProduct);
        } else {
          setNotFound(true);
        }
      } catch (error) {
        console.error('Error loading product:', error);
        toast({
          title: 'Error loading product',
          description: 'Failed to load product details',
          variant: 'destructive',
        });
        setNotFound(true);
      } finally {
        setIsLoading(false);
      }
    };

    loadProduct();
  }, [productId]);

  const handleSaveProduct = async (updatedProduct: Product) => {
    try {
      // Update product in Supabase
      await updateProduct(updatedProduct);

      // Show success message
      toast({
        title: "Product Updated",
        description: `${updatedProduct.name} has been updated successfully.`,
      });

      // Redirect to products list
      navigate('/admin/products');
    } catch (error: any) {
      console.error('Error updating product:', error);
      toast({
        title: "Error Updating Product",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive"
      });
    }
  };

  const handleDeleteProduct = async () => {
    if (!productId) return;

    try {
      // Delete product from Supabase
      const success = await deleteProduct(productId);

      if (success) {
        // Show success message
        toast({
          title: "Product Deleted",
          description: "The product has been deleted successfully.",
        });

        // Close dialog and redirect
        setIsDeleteDialogOpen(false);
        navigate('/admin/products');
      }
    } catch (error: any) {
      console.error('Error deleting product:', error);
      toast({
        title: "Error Deleting Product",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive"
      });
      setIsDeleteDialogOpen(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/products');
  };

  if (!isAuthenticated || !isAdmin()) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex flex-1 pt-20">
          <AdminSidebar />
          <div className="flex-1 p-6 flex items-center justify-center">
            <div className="flex flex-col items-center">
              <Loader2 className="h-10 w-10 animate-spin text-primary" />
              <p className="mt-4 text-muted-foreground">Loading product data...</p>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (notFound) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex flex-1 pt-20">
          <AdminSidebar />
          <div className="flex-1 p-6">
            <div className="bg-white rounded-md shadow-sm p-6 text-center">
              <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
              <p className="text-muted-foreground mb-6">
                The product you're looking for doesn't exist or has been removed.
              </p>
              <Button onClick={() => navigate('/admin/products')}>
                Back to Products
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex flex-1 pt-20">
        <AdminSidebar />

        <div className="flex-1 p-6 overflow-auto">
          {/* Breadcrumb */}
          <Breadcrumb className="mb-6">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin/products">Products</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Edit Product</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="bg-white rounded-md shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-bold">Edit Product</h1>
              <Button
                variant="destructive"
                onClick={() => setIsDeleteDialogOpen(true)}
              >
                Delete Product
              </Button>
            </div>

            {product && (
              <EditorProductForm
                product={product}
                onSave={handleSaveProduct}
                onCancel={handleCancel}
              />
            )}
          </div>
        </div>
      </div>

      <Footer />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the product
              from the database.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteProduct} className="bg-red-600 hover:bg-red-700">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminProductEdit;
