/* Styles for authentication forms (login, signup, etc.) */

/* Checkbox styling for auth forms */
.auth-checkbox {
  display: flex;
  align-items: center;
}

.auth-checkbox input[type="checkbox"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-right: 8px;
  position: relative;
  cursor: pointer;
  background-color: white;
}

.auth-checkbox input[type="checkbox"]:checked {
  background-color: #6366f1;
  border-color: #6366f1;
}

.auth-checkbox input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 6px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.auth-checkbox label {
  font-size: 14px;
  color: #4b5563;
  cursor: pointer;
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .auth-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
  }

  .auth-checkbox input[type="checkbox"]:checked::after {
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border-width: 0 2px 2px 0;
  }

  .auth-checkbox label {
    font-size: 13px;
  }
}
