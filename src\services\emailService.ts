/**
 * Email Service Implementation
 *
 * This service provides email functionality using Supabase Edge Functions.
 * For signup confirmations and password resets, Supabase handles these automatically.
 * This service handles custom emails like welcome emails and order notifications.
 */
import { supabase } from '@/lib/supabase';

/**
 * Email template types
 */
export enum EmailTemplate {
  WELCOME = 'welcome_email',
  ORDER_CONFIRMATION = 'order_confirmation',
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  ORDER_SHIPPED = 'order_shipped',
  ORDER_DELIVERED = 'delivery_confirmation',
  ORDER_CANCELLED = 'order_cancelled',
}

/**
 * Email data interface
 */
export interface EmailData {
  to: string;
  subject?: string;
  templateId: EmailTemplate;
  data: Record<string, any>;
}

/**
 * Send an email using Supabase Edge Function
 *
 * @param emailData Email data including recipient, template, and data
 * @returns Success status
 */
export const sendEmail = async (emailData: EmailData): Promise<boolean> => {
  try {
    // Call Supabase Edge Function for sending emails
    const { data, error } = await supabase.functions.invoke('email-service', {
      body: {
        to: emailData.to,
        subject: emailData.subject,
        template: emailData.templateId,
        templateData: emailData.data
      }
    });

    if (error) {
      console.error('Error sending email via Edge Function:', error);
      return false;
    }

    console.log('Email sent successfully:', data);
    return true;
  } catch (error) {
    console.error('Exception sending email:', error);
    return false;
  }
};

/**
 * Send order confirmation email
 *
 * @param orderId Order ID
 * @param email Recipient email
 * @returns Success status
 */
export const sendOrderConfirmationEmail = async (
  orderId: string,
  email: string
): Promise<boolean> => {
  return await sendEmail({
    to: email,
    subject: 'Order Confirmation - The Badhees',
    templateId: EmailTemplate.ORDER_CONFIRMATION,
    data: {
      orderId,
      orderUrl: `${window.location.origin}/orders/${orderId}`
    }
  });
};

/**
 * Send payment success email
 *
 * @param orderId Order ID
 * @param email Recipient email
 * @param paymentId Payment ID
 * @returns Success status
 */
export const sendPaymentSuccessEmail = async (
  orderId: string,
  email: string,
  paymentId: string
): Promise<boolean> => {
  return await sendEmail({
    to: email,
    subject: 'Payment Successful - The Badhees',
    templateId: EmailTemplate.PAYMENT_SUCCESS,
    data: {
      orderId,
      paymentId,
      orderUrl: `${window.location.origin}/orders/${orderId}`
    }
  });
};

/**
 * Send welcome email to new users
 *
 * @param userId User ID
 * @param email User's email address
 * @returns Success status
 */
export const sendWelcomeEmail = async (
  userId: string,
  email: string
): Promise<boolean> => {
  return await sendEmail({
    to: email,
    subject: 'Welcome to The Badhees - Premium Furniture Store',
    templateId: EmailTemplate.WELCOME,
    data: {
      userId,
      websiteUrl: window.location.origin,
      supportEmail: '<EMAIL>'
    }
  });
};

/**
 * Send order status update email
 *
 * @param orderId Order ID
 * @param email Recipient email
 * @param status New order status
 * @returns Success status
 */
export const sendOrderStatusUpdateEmail = async (
  orderId: string,
  email: string,
  status: string
): Promise<boolean> => {
  let templateId: EmailTemplate;
  let subject: string;

  switch (status) {
    case 'shipped':
      templateId = EmailTemplate.ORDER_SHIPPED;
      subject = 'Your Order Has Been Shipped - The Badhees';
      break;
    case 'delivered':
      templateId = EmailTemplate.ORDER_DELIVERED;
      subject = 'Your Order Has Been Delivered - The Badhees';
      break;
    case 'canceled':
      templateId = EmailTemplate.ORDER_CANCELLED;
      subject = 'Order Cancelled - The Badhees';
      break;
    default:
      console.log(`No email template for status: ${status}`);
      return true; // Don't send email for other statuses
  }

  return await sendEmail({
    to: email,
    subject,
    templateId,
    data: {
      orderId,
      status,
      orderUrl: `${window.location.origin}/orders/${orderId}`
    }
  });
};
