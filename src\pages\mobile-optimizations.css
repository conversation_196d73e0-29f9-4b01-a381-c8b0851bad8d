/* Mobile optimizations for Products page */

/* Reduce spacing between elements on mobile */
@media (max-width: 640px) {
  /* Optimize search input for mobile */
  .search-input-mobile {
    font-size: 16px !important; /* Prevents iOS zoom */
    width: 100% !important;
    max-width: 100% !important;
    margin-bottom: 16px !important; /* Reduced from default */
  }

  /* Reduce spacing between product cards - much tighter */
  .product-grid-mobile {
    gap: 8px !important; /* Reduced from 12px */
    margin-top: 8px !important; /* Reduced top margin */
  }

  /* Optimize breadcrumb for mobile */
  .breadcrumb-mobile {
    font-size: 12px;
    margin-bottom: 4px !important; /* Reduced from 8px */
  }

  /* Optimize heading for mobile */
  .heading-mobile {
    font-size: 20px !important;
    margin-bottom: 4px !important; /* Reduced from 8px */
  }

  /* Optimize description for mobile */
  .description-mobile {
    font-size: 14px !important;
    line-height: 1.4 !important;
    margin-bottom: 8px !important; /* Reduced spacing after description */
  }

  /* Reduce padding in main container */
  .products-main-container {
    padding-top: 12px !important; /* Reduced from default */
    padding-bottom: 12px !important;
  }

  /* Reduce spacing between filter/sort section and search */
  .filter-sort-section {
    margin-bottom: 12px !important; /* Reduced spacing */
  }

  /* Fix search input icon overlap */
  .search-icon-fix {
    z-index: 10;
  }

  /* Ensure proper spacing for search input on mobile */
  input[type="text"].mobile-search-input {
    padding-left: 36px !important;
    font-size: 14px !important;
  }

  /* Fix search bar overlap in mobile view - IMPROVED */
  .search-input-container {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
  }

  .search-input {
    width: 100%;
    padding-left: 2.5rem !important; /* Space for search icon */
    padding-right: 3rem !important; /* More space for clear button */
    height: 44px !important; /* Slightly taller for better touch */
    font-size: 16px !important; /* Prevents iOS zoom */
    border-radius: 12px !important; /* More modern rounded corners */
    border: 1px solid #e5e7eb !important;
    background-color: #f9fafb !important;
  }

  .search-input:focus {
    background-color: white !important;
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
  }

  .search-input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    height: 1.25rem !important; /* Slightly larger */
    width: 1.25rem !important;
    color: #6b7280 !important;
    pointer-events: none;
    z-index: 10;
  }

  /* Clear button positioning - NO OVERLAP */
  .search-clear-button {
    position: absolute;
    right: 0.5rem !important; /* Moved further right */
    top: 50%;
    transform: translateY(-50%);
    height: 1.5rem !important;
    width: 1.5rem !important;
    background-color: #f3f4f6 !important;
    border-radius: 50% !important;
    border: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: #6b7280 !important;
    cursor: pointer !important;
    z-index: 20 !important; /* Higher z-index */
  }

  .search-clear-button:hover {
    background-color: #e5e7eb !important;
    color: #374151 !important;
  }

  /* Loading indicator positioning */
  .search-loading-indicator {
    position: absolute;
    right: 0.75rem !important;
    top: 50%;
    transform: translateY(-50%);
    height: 1.25rem !important;
    width: 1.25rem !important;
    color: #6366f1 !important;
    z-index: 15;
  }

  /* Ensure search bar is properly sized in navbar */
  .navbar-search {
    max-width: calc(100% - 100px) !important;
    margin-right: 0.5rem;
  }

  /* Hide search submit button on mobile for cleaner look */
  .search-submit-button {
    display: none !important;
  }

  /* Improve touch targets for mobile */
  .search-clear-button {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  /* Reduce overall container spacing */
  .products-main-container {
    padding-top: 8px !important;
    padding-bottom: 8px !important;
  }

  /* Tighter spacing for product grid */
  .product-grid-mobile {
    gap: 6px !important;
    margin-top: 4px !important;
  }

  /* Reduce filter section spacing */
  .filter-sort-section {
    margin-bottom: 8px !important;
  }
}
