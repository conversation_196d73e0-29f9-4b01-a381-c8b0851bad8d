import { supabase } from '@/lib/supabase';
import { validateImageFile, extractStoragePath, generateUniqueFilename } from '@/utils/supabaseHelpers';

/**
 * Uploads an image for a project to Supabase Storage
 * @param file The file to upload
 * @returns The URL of the uploaded file, or null if upload failed
 */
export const uploadProjectImage = async (file: File): Promise<string | null> => {
  try {
    // Validate the file
    const validationError = validateImageFile(file);
    if (validationError) {
      throw new Error(validationError);
    }

    // Create a unique file name
    const filePath = generateUniqueFilename(file);

    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('project-images')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      throw error;
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('project-images')
      .getPublicUrl(filePath);

    return publicUrl;
  } catch (error) {
    console.error('Error uploading project image:', error);
    return null;
  }
};

/**
 * Deletes an image from Supabase Storage
 * @param url The URL of the image to delete
 * @returns True if successful, false otherwise
 */
export const deleteProjectImage = async (url: string): Promise<boolean> => {
  try {
    // Extract the file path from the URL
    const pathInfo = extractStoragePath(url);
    if (!pathInfo) {
      console.error('Invalid storage URL:', url);
      return false;
    }

    // Delete the file
    const { error } = await supabase.storage
      .from(pathInfo.bucketName)
      .remove([pathInfo.filePath]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting project image:', error);
    return false;
  }
};
