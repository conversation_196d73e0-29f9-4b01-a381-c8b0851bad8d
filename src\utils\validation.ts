/**
 * Validates a phone number
 * Accepts formats like:
 * - ******-456-7890
 * - +91 98765 43210
 * - ************
 * - (*************
 * - 1234567890
 * 
 * @param phone The phone number to validate
 * @returns True if the phone number is valid, false otherwise
 */
export const isValidPhone = (phone: string): boolean => {
  if (!phone) return true; // Allow empty phone numbers
  
  // Basic phone validation - allows various formats
  const phoneRegex = /^(\+\d{1,3}[\s-]?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/;
  return phoneRegex.test(phone);
};

/**
 * Validates a postal code
 * Supports formats for different countries:
 * - US: 12345 or 12345-6789
 * - Canada: A1A 1A1
 * - UK: AA1A 1AA
 * - India: 123456
 * 
 * @param postalCode The postal code to validate
 * @param countryCode The country code (e.g., 'US', 'CA', 'UK', 'IN')
 * @returns True if the postal code is valid for the country, false otherwise
 */
export const isValidPostalCode = (postalCode: string, countryCode: string): boolean => {
  if (!postalCode) return true; // Allow empty postal codes
  if (!countryCode) return true; // Skip validation if no country is selected
  
  // Different regex patterns for different countries
  const patterns: Record<string, RegExp> = {
    US: /^\d{5}(-\d{4})?$/, // US: 12345 or 12345-6789
    CA: /^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/, // Canada: A1A 1A1
    UK: /^[A-Za-z]{1,2}\d[A-Za-z\d]?[ ]?\d[A-Za-z]{2}$/, // UK: AA1A 1AA
    IN: /^\d{6}$/, // India: 123456
    // Add more country-specific patterns as needed
  };
  
  // If we don't have a specific pattern for this country, use a generic one
  const pattern = patterns[countryCode] || /^[A-Za-z0-9\s-]{3,10}$/;
  
  return pattern.test(postalCode);
};

/**
 * Validates a file for image upload
 * @param file The file to validate
 * @returns An error message if invalid, or null if valid
 */
export const validateImageFile = (file: File): string | null => {
  // Check file type
  const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  if (!validTypes.includes(file.type)) {
    return 'File must be an image (JPEG, PNG, GIF, or WEBP)';
  }
  
  // Check file size (max 2MB)
  const maxSize = 2 * 1024 * 1024; // 2MB
  if (file.size > maxSize) {
    return 'Image must be smaller than 2MB';
  }
  
  return null; // File is valid
};
