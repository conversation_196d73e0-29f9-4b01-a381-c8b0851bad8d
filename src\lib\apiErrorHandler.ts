import { toast } from "@/hooks/use-toast";
import { PostgrestError } from "@supabase/supabase-js";

/**
 * Centralized error handler for API calls
 * @param error The error object
 * @param context Additional context about where the error occurred
 * @param fallbackMessage Optional fallback message if error doesn't have a message
 */
export const handleApiError = (
  error: unknown, 
  context: string,
  fallbackMessage = 'An unexpected error occurred'
): void => {
  console.error(`Error in ${context}:`, error);
  
  let errorMessage = fallbackMessage;
  
  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === 'object' && error !== null) {
    // Handle Supabase PostgrestError
    const pgError = error as PostgrestError;
    if (pgError.message) {
      errorMessage = pgError.message;
    }
  }
  
  toast({
    title: `Error: ${context}`,
    description: errorMessage,
    variant: 'destructive',
  });
};

/**
 * Wraps an async function with standardized error handling
 * @param fn The async function to wrap
 * @param context Context for error reporting
 * @param fallbackMessage Optional fallback error message
 * @returns A function with the same signature but with error handling
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context: string,
  fallbackMessage?: string
): (...args: Parameters<T>) => Promise<Awaited<ReturnType<T>> | null> {
  return async (...args: Parameters<T>): Promise<Awaited<ReturnType<T>> | null> => {
    try {
      return await fn(...args);
    } catch (error) {
      handleApiError(error, context, fallbackMessage);
      return null;
    }
  };
}
