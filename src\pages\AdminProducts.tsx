import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import AdminProductsTable from "@/components/admin/AdminProductsTable";
import AdminProductsHeader from "@/components/admin/AdminProductsHeader";
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { Product } from '@/services/editorProductsService';
import { getProducts, deleteProduct, updateProductStock } from '@/services/supabaseProductsService';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

const AdminProducts = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortOption, setSortOption] = useState<string>('name-asc');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [stockFilter, setStockFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  useEffect(() => {
    window.scrollTo(0, 0);
    const fetchProducts = async () => {
      try {
        const supabaseProducts = await getProducts();
        setProducts(supabaseProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast({
          title: 'Error fetching products',
          description: 'Failed to load products from the database',
          variant: 'destructive',
        });
      }
    };
    fetchProducts();
  }, []);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/products');
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive"
      });
    } else if (!isAdmin()) {
      navigate('/');
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive"
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  useEffect(() => {
    let result = [...products];

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(product =>
        product.name.toLowerCase().includes(query) ||
        product.id.toLowerCase().includes(query) ||
        product.category.toLowerCase().includes(query)
      );
    }

    if (categoryFilter !== 'all') {
      result = result.filter(product => product.category === categoryFilter);
    }

    if (statusFilter !== 'all') {
      result = result.filter(product => product.status === statusFilter);
    }

    if (stockFilter !== 'all') {
      switch (stockFilter) {
        case 'in-stock':
          result = result.filter(product => product.stock > 0);
          break;
        case 'out-of-stock':
          result = result.filter(product => product.stock <= 0);
          break;
        case 'low-stock':
          result = result.filter(product => product.stock > 0 && product.stock < 10);
          break;
      }
    }

    const [sortField, sortDirection] = sortOption.split('-');
    result.sort((a: any, b: any) => {
      if (sortField === 'price') {
        return sortDirection === 'asc' ? a.price - b.price : b.price - a.price;
      } else if (sortField === 'stock') {
        return sortDirection === 'asc' ? a.stock - b.stock : b.stock - a.stock;
      } else {
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();
        if (nameA < nameB) return sortDirection === 'asc' ? -1 : 1;
        if (nameA > nameB) return sortDirection === 'asc' ? 1 : -1;
        return 0;
      }
    });

    setFilteredProducts(result);
    setCurrentPage(1);
  }, [products, searchQuery, sortOption, categoryFilter, statusFilter, stockFilter]);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredProducts.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleUpdateProduct = async (updatedProduct: Product) => {
    try {
      // Check if only the stock was updated
      const originalProduct = products.find(p => p.id === updatedProduct.id);
      if (originalProduct && originalProduct.stock !== updatedProduct.stock) {
        // If only stock changed, use the optimized stock update function
        const success = await updateProductStock(updatedProduct.id, updatedProduct.stock);

        if (success) {
          // Update local state
          const updatedProducts = products.map(product =>
            product.id === updatedProduct.id ? updatedProduct : product
          );
          setProducts(updatedProducts);

          toast({
            title: "Stock Updated",
            description: `Stock for "${updatedProduct.name}" has been updated successfully.`
          });
        }
      } else {
        // For other updates, we would use the full updateProduct function
        // But for now, just update the local state
        const updatedProducts = products.map(product =>
          product.id === updatedProduct.id ? updatedProduct : product
        );
        setProducts(updatedProducts);

        toast({
          title: "Product Updated",
          description: `Product "${updatedProduct.name}" has been updated successfully.`
        });
      }
    } catch (error: any) {
      console.error('Error updating product:', error);
      toast({
        title: "Error Updating Product",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive"
      });
    }
  };

  const handleDeleteProduct = async (productId: string) => {
    try {
      // Delete product from Supabase
      const success = await deleteProduct(productId);

      if (success) {
        // Update local state
        const updatedProducts = products.filter(product => product.id !== productId);
        setProducts(updatedProducts);

        toast({
          title: "Product Deleted",
          description: "Product has been deleted successfully."
        });
      }
    } catch (error: any) {
      console.error('Error deleting product:', error);
      toast({
        title: "Error Deleting Product",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive"
      });
    }
  };

  const handleBulkAction = async (productIds: string[], action: string) => {
    // For now, we'll handle bulk actions one by one
    // In a future update, you could implement batch operations in the Supabase service

    try {
      switch (action) {
        case 'delete':
          // Delete products one by one
          for (const productId of productIds) {
            await deleteProduct(productId);
          }

          // Update local state
          const updatedProducts = products.filter(product => !productIds.includes(product.id));
          setProducts(updatedProducts);

          toast({
            title: "Products Deleted",
            description: `${productIds.length} products have been deleted.`
          });
          break;

        // For status updates, we'll need to implement updateProductStatus in the service
        // For now, we'll just show a message that this feature is coming soon
        case 'active':
        case 'draft':
          toast({
            title: "Feature Coming Soon",
            description: `Bulk status updates will be available in a future update.`
          });
          break;
        default:
          return;
      }
    } catch (error: any) {
      console.error('Error performing bulk action:', error);
      toast({
        title: "Error",
        description: error.message || 'An unexpected error occurred',
        variant: "destructive"
      });
    }
  };

  const categories = Array.from(new Set(products.map(product => product.category)));

  if (!isAuthenticated || !isAdmin()) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex flex-1 pt-20">
        <AdminSidebar />

        <div className="flex-1 p-6 overflow-hidden bg-gray-50">
          <Breadcrumb className="mb-6">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/admin">Dashboard</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Products</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-badhees-800">Products Management</h1>
            <Button asChild className="bg-badhees-800 hover:bg-badhees-700">
              <Link to="/admin/products/new" className="flex items-center gap-1">
                <Plus className="h-4 w-4" /> Add New Product
              </Link>
            </Button>
          </div>

          <AdminProductsHeader
            productsCount={products.length}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            sortOption={sortOption}
            setSortOption={setSortOption}
            categoryFilter={categoryFilter}
            setCategoryFilter={setCategoryFilter}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            stockFilter={stockFilter}
            setStockFilter={setStockFilter}
            categories={categories}
          />

          <AdminProductsTable
            products={currentItems}
            onUpdate={handleUpdateProduct}
            onDelete={handleDeleteProduct}
            onBulkAction={handleBulkAction}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminProducts;
