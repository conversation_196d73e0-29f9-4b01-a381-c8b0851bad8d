/* Product gallery styles */
.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease;
  background-color: #f8fafc; /* Light background to prevent white flashes */
}

.gallery-image-zoomed {
  cursor: zoom-out;
}

.gallery-image-normal {
  cursor: zoom-in;
}

/* Zoom levels */
.zoom-level-1 { transform: scale(1); }
.zoom-level-1-5 { transform: scale(1.5); }
.zoom-level-2 { transform: scale(2); }
.zoom-level-2-5 { transform: scale(2.5); }
.zoom-level-3 { transform: scale(3); }

/* Dialog zoom image */
.dialog-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain; /* Keep contain for zoom dialog to see full image */
  object-position: center;
}

/* Scale classes for dialog */
.scale-1 { transform: scale(1); }
.scale-1-5 { transform: scale(1.5); }
.scale-2 { transform: scale(2); }
.scale-2-5 { transform: scale(2.5); }
.scale-3 { transform: scale(3); }
