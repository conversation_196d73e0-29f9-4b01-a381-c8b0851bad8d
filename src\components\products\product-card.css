/* Product card carousel styles */
.product-carousel {
  height: 100%;
  transition: transform 500ms;
  will-change: transform;
  display: flex;
}

.product-carousel-slide {
  height: 100%;
  flex-shrink: 0;
}

/* Carousel position classes */
.carousel-position-0 { transform: translateX(0%); }
.carousel-position-1 { transform: translateX(-100%); }
.carousel-position-2 { transform: translateX(-200%); }
.carousel-position-3 { transform: translateX(-300%); }
.carousel-position-4 { transform: translateX(-400%); }
.carousel-position-5 { transform: translateX(-500%); }
.carousel-position-6 { transform: translateX(-600%); }

/* Dynamic width classes for different number of slides */
.carousel-1-slides { width: 100%; }
.carousel-1-slides .product-carousel-slide { width: 100%; }

.carousel-2-slides { width: 200%; }
.carousel-2-slides .product-carousel-slide { width: 50%; }

.carousel-3-slides { width: 300%; }
.carousel-3-slides .product-carousel-slide { width: 33.333%; }

.carousel-4-slides { width: 400%; }
.carousel-4-slides .product-carousel-slide { width: 25%; }

.carousel-5-slides { width: 500%; }
.carousel-5-slides .product-carousel-slide { width: 20%; }

.carousel-6-slides { width: 600%; }
.carousel-6-slides .product-carousel-slide { width: 16.666%; }

.carousel-7-slides { width: 700%; }
.carousel-7-slides .product-carousel-slide { width: 14.285%; }
