/**
 * Analytics Service (Compatibility Layer)
 * 
 * This module provides backward compatibility with the old analyticsService.
 * It re-exports all functionality from the new modular analytics services.
 * 
 * @deprecated Use the new modular analytics services instead:
 * import { getDashboardStats, etc. } from '@/services/analytics';
 */

// Import and re-export types
export type {
  DashboardStats,
  OrderStats,
  CustomerStats,
  CategoryPerformance,
  SalesTrend,
  WeeklyPerformance,
  TopSellingProduct,
  TopProductsSortBy
} from './analytics';

// Import and re-export functions
export {
  // Revenue functions
  getTotalRevenue,
  getOrderStats,
  getSalesTrend,
  getWeeklyPerformance,
  
  // Customer and product functions
  getCustomerStats,
  getCategoryPerformance,
  getTopSellingProducts,
  getTotalProductsCount,
  getTotalProjectsCount,
  
  // Dashboard functions
  getDashboardStats,
  subscribeToRealtimeUpdates
} from './analytics';
