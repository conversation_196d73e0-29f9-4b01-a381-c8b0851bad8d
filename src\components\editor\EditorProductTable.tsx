
import React, { useState } from 'react';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Checkbox } from '@/components/ui/checkbox';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import EditorProductForm from './EditorProductForm';
import ProductTableRow from './ProductTableRow';
import BulkActions from './BulkActions';
import { Product } from '@/services/editorProductsService';

interface EditorProductTableProps {
  products: Product[];
  onStatusChange: (id: string, status: 'active' | 'draft' | 'deleted') => void;
  onBulkAction: (ids: string[], action: 'delete' | 'restore' | 'activate' | 'draft') => void;
  onSave: (product: Product) => void;
}

const EditorProductTable: React.FC<EditorProductTableProps> = ({ 
  products, 
  onStatusChange,
  onBulkAction,
  onSave
}) => {
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedProducts(products.map(p => p.id));
    } else {
      setSelectedProducts([]);
    }
  };
  
  const handleSelectProduct = (productId: string, checked: boolean) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    }
  };
  
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    setIsDialogOpen(true);
  };
  
  const handleSave = (updatedProduct: Product) => {
    onSave(updatedProduct);
    setIsDialogOpen(false);
    setEditingProduct(null);
  };
  
  return (
    <div className="space-y-4">
      {/* Bulk Actions */}
      <BulkActions
        selectedCount={selectedProducts.length}
        onBulkAction={onBulkAction}
        selectedIds={selectedProducts}
        onClearSelection={() => setSelectedProducts([])}
      />
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox 
                  checked={selectedProducts.length === products.length && products.length > 0}
                  onCheckedChange={(checked) => handleSelectAll(!!checked)}
                />
              </TableHead>
              <TableHead className="w-[80px]">Image</TableHead>
              <TableHead>Product</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Price</TableHead>
              <TableHead>Stock</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {products.length > 0 ? (
              products.map((product) => (
                <ProductTableRow
                  key={product.id}
                  product={product}
                  isSelected={selectedProducts.includes(product.id)}
                  onSelectProduct={handleSelectProduct}
                  onEdit={handleEdit}
                  onStatusChange={onStatusChange}
                />
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                  No products found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Edit Product Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingProduct ? `Edit ${editingProduct.name}` : 'Edit Product'}
            </DialogTitle>
          </DialogHeader>
          
          {editingProduct && (
            <EditorProductForm 
              product={editingProduct}
              onSave={handleSave}
              onCancel={() => setIsDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EditorProductTable;
