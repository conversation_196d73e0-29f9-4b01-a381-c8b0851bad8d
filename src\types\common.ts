/**
 * Common Types
 *
 * This module provides shared type definitions used across the application.
 */

/**
 * Base database entity interface
 * Common fields for all database entities
 */
export interface BaseEntity {
  id: string;
  created_at?: string;
  updated_at?: string;
}

/**
 * Status types for various entities
 */
export type OrderStatus = 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
export type ProductStatus = 'active' | 'draft' | 'deleted';
export type ProjectStatus = 'active' | 'completed' | 'cancelled' | 'deleted';
export type UserStatus = 'active' | 'inactive' | 'suspended';
export type ConsultationStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';

/**
 * Pagination parameters
 */
export interface PaginationParams {
  page: number;
  pageSize: number;
}

/**
 * Pagination result
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * Sort parameters
 */
export interface SortParams {
  column: string;
  direction: 'asc' | 'desc';
}

/**
 * Filter parameters
 */
export interface FilterParams {
  field: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike' | 'in';
  value: string | number | boolean | string[] | number[];
}

/**
 * Query parameters combining pagination, sorting, and filtering
 */
export interface QueryParams {
  pagination?: PaginationParams;
  sort?: SortParams;
  filters?: FilterParams[];
}

/**
 * Address interface
 */
export interface Address {
  id: string;
  user_id: string;
  name: string;
  address_line1: string;
  address_line2?: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone: string;
  is_default: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Re-export Category from dedicated file
 */
export type { Category } from './category';

/**
 * Image interface
 */
export interface ImageFile {
  id: string;
  url: string;
  alt_text?: string;
  is_primary?: boolean;
  display_order?: number;
}

/**
 * Error response interface
 */
export interface ErrorResponse {
  message: string;
  code?: string;
  details?: unknown;
}

/**
 * Success response interface
 */
export interface SuccessResponse<T> {
  data: T;
  message?: string;
}

/**
 * API response interface
 */
export interface ApiResponse<T> {
  data?: T;
  error?: ErrorResponse;
  success: boolean;
}

/**
 * Date range interface
 */
export interface DateRange {
  startDate: string;
  endDate: string;
}

/**
 * Price range interface
 */
export interface PriceRange {
  min: number;
  max: number;
}
