import crypto from 'crypto';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

export default async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  // Handle OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    // Get the webhook signature from headers
    const webhookSignature = req.headers['x-razorpay-signature'];

    if (!webhookSignature) {
      return res.status(400).json({ success: false, error: 'Missing webhook signature' });
    }

    // Get the webhook secret from environment variables
    const webhookSecret = process.env.RAZORPAY_WEBHOOK_SECRET || process.env.RAZORPAY_SECRET;

    // Verify the webhook signature
    const shasum = crypto.createHmac('sha256', webhookSecret);
    shasum.update(JSON.stringify(req.body));
    const digest = shasum.digest('hex');

    if (digest !== webhookSignature) {
      return res.status(400).json({ success: false, error: 'Invalid webhook signature' });
    }

    // Process the webhook event
    const event = req.body;
    const eventType = event.event;

    console.log('Received webhook event:', eventType);

    // Handle different event types
    switch (eventType) {
      case 'payment.authorized':
        await handlePaymentAuthorized(event.payload.payment.entity);
        break;
      case 'payment.captured':
        await handlePaymentCaptured(event.payload.payment.entity);
        break;
      case 'payment.failed':
        await handlePaymentFailed(event.payload.payment.entity);
        break;
      default:
        console.log('Unhandled event type:', eventType);
    }

    return res.status(200).json({ success: true, message: 'Webhook processed successfully' });
  } catch (error) {
    console.error('Error processing webhook:', error);
    return res.status(500).json({ success: false, error: error.message || 'Failed to process webhook' });
  }
};

// Handle payment authorized event
async function handlePaymentAuthorized(payment) {
  try {
    // Update payment record using secure function
    const { error } = await supabase.rpc('update_payment_record', {
      p_razorpay_order_id: payment.order_id,
      p_razorpay_payment_id: payment.id,
      p_status: 'authorized',
      p_method: payment.method,
      p_error_description: null
    });

    if (error) {
      throw error;
    }

    console.log('Payment authorized:', payment.id);
  } catch (error) {
    console.error('Error handling payment authorized:', error);
  }
}

// Handle payment captured event
async function handlePaymentCaptured(payment) {
  try {
    // Update payment record using secure function
    const { error: paymentError } = await supabase.rpc('update_payment_record', {
      p_razorpay_order_id: payment.order_id,
      p_razorpay_payment_id: payment.id,
      p_status: 'captured',
      p_method: payment.method,
      p_error_description: null
    });

    if (paymentError) {
      throw paymentError;
    }

    // Get the order ID from the payment notes
    const orderId = payment.notes?.order_id;

    if (orderId) {
      // Update order status to 'paid'
      const { error: orderError } = await supabase
        .from('orders')
        .update({
          status: 'paid',
          payment_status: 'paid',
          razorpay_payment_id: payment.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (orderError) {
        throw orderError;
      }
    }

    console.log('Payment captured:', payment.id);
  } catch (error) {
    console.error('Error handling payment captured:', error);
  }
}

// Handle payment failed event
async function handlePaymentFailed(payment) {
  try {
    // Update payment record using secure function
    const { error } = await supabase.rpc('update_payment_record', {
      p_razorpay_order_id: payment.order_id,
      p_razorpay_payment_id: payment.id,
      p_status: 'failed',
      p_method: payment.method,
      p_error_description: payment.error_description || payment.error_reason || 'Payment failed'
    });

    if (error) {
      console.error('Error updating payment record in webhook:', error);
      // Don't throw error to prevent webhook retry loops
    } else {
      console.log('Payment failed:', payment.id);
    }
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}
