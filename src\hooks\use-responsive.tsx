
import * as React from "react";

// Breakpoint values in pixels
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  "2xl": 1536,
};

export type Breakpoint = keyof typeof breakpoints;

/**
 * Hook to check if the current viewport matches the given breakpoint
 * @param breakpoint - The breakpoint to check against
 * @param direction - 'up' checks if viewport is >= breakpoint, 'down' checks if viewport is < breakpoint
 * @returns boolean indicating if the condition is met
 */
export function useBreakpoint(
  breakpoint: Breakpoint,
  direction: "up" | "down" = "up"
) {
  const [matches, setMatches] = React.useState<boolean>(false);

  React.useEffect(() => {
    const mediaQuery =
      direction === "up"
        ? `(min-width: ${breakpoints[breakpoint]}px)`
        : `(max-width: ${breakpoints[breakpoint] - 0.1}px)`;

    const mql = window.matchMedia(mediaQuery);
    const handleChange = () => {
      setMatches(mql.matches);
    };

    setMatches(mql.matches);
    mql.addEventListener("change", handleChange);

    return () => {
      mql.removeEventListener("change", handleChange);
    };
  }, [breakpoint, direction]);

  return matches;
}

/**
 * Hook to check if the current viewport is in mobile range (< 768px)
 * @returns boolean indicating if the device is mobile size
 */
export function useIsMobile() {
  return useBreakpoint("md", "down");
}

/**
 * Hook to check if the current viewport is in tablet range (>= 768px and < 1024px)
 * @returns boolean indicating if the device is tablet size
 */
export function useIsTablet() {
  const isAtLeastTablet = useBreakpoint("md", "up");
  const isSmallerThanDesktop = useBreakpoint("lg", "down");
  return isAtLeastTablet && isSmallerThanDesktop;
}

/**
 * Hook to check if the current viewport is in desktop range (>= 1024px)
 * @returns boolean indicating if the device is desktop size
 */
export function useIsDesktop() {
  return useBreakpoint("lg", "up");
}

/**
 * Hook to get the current breakpoint
 * @returns current breakpoint name
 */
export function useCurrentBreakpoint(): Breakpoint {
  const [breakpoint, setBreakpoint] = React.useState<Breakpoint>("xs");

  React.useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      if (width >= breakpoints["2xl"]) {
        setBreakpoint("2xl");
      } else if (width >= breakpoints.xl) {
        setBreakpoint("xl");
      } else if (width >= breakpoints.lg) {
        setBreakpoint("lg");
      } else if (width >= breakpoints.md) {
        setBreakpoint("md");
      } else if (width >= breakpoints.sm) {
        setBreakpoint("sm");
      } else {
        setBreakpoint("xs");
      }
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  return breakpoint;
}
