import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  UserPlus, 
  ClipboardList, 
  Clock, 
  DollarSign, 
  AlertTriangle,
  CheckCircle,
  Calendar,
  Loader2,
  ArrowRight
} from 'lucide-react';
import { format, parseISO, startOfMonth, endOfMonth, isToday } from 'date-fns';
import { useAuth } from '@/context/SupabaseAuthContext';
import { 
  useEmployees, 
  useOvertimeSummary,
  useMonthlyAttendanceSummary,
  useAttendanceByDate
} from '@/hooks/useEmployeeManagement';

const AdminEmployeeDashboard = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();
  
  // Get current date and month
  const today = new Date();
  const currentMonth = today.getMonth() + 1;
  const currentYear = today.getFullYear();
  const formattedToday = format(today, 'yyyy-MM-dd');
  
  // Fetch data
  const { data: employees, isLoading: isLoadingEmployees } = useEmployees();
  const { data: overtimeSummary, isLoading: isLoadingOvertimeSummary } = 
    useOvertimeSummary(currentYear, currentMonth);
  const { data: attendanceSummary, isLoading: isLoadingAttendanceSummary } = 
    useMonthlyAttendanceSummary(currentYear, currentMonth);
  const { data: todayAttendance, isLoading: isLoadingTodayAttendance } = 
    useAttendanceByDate(formattedToday);

  // Check authentication and admin status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/employees/dashboard');
    } else if (!isAdmin()) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Calculate summary metrics
  const activeEmployees = employees?.filter(e => e.status === 'active').length || 0;
  const pendingOvertimeRequests = overtimeSummary?.reduce(
    (sum, record) => sum + (record.pending_hours > 0 ? 1 : 0), 
    0
  ) || 0;
  const presentToday = todayAttendance?.filter(a => a.status === 'present').length || 0;
  const absentToday = todayAttendance?.filter(a => a.status === 'absent').length || 0;
  
  // Get employees with pending overtime
  const employeesWithPendingOvertime = overtimeSummary
    ?.filter(record => record.pending_hours > 0)
    .sort((a, b) => b.pending_hours - a.pending_hours)
    .slice(0, 5) || [];

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
              <div>
                <h1 className="text-2xl font-bold text-badhees-800">Employee Management Dashboard</h1>
                <p className="text-sm text-gray-500 mt-1">Overview of your employee management system</p>
              </div>
              <div className="flex items-center space-x-2">
                <Button onClick={() => navigate('/admin/employees/new')}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Employee
                </Button>
              </div>
            </div>

            {/* Summary Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Active Employees</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Users className="h-8 w-8 text-badhees-600 mr-3" />
                    <div className="text-3xl font-bold">{activeEmployees}</div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" onClick={() => navigate('/admin/employees')}>
                    View All <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Today's Attendance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Calendar className="h-8 w-8 text-badhees-600 mr-3" />
                    <div className="flex flex-col">
                      <div className="text-3xl font-bold">{presentToday}/{activeEmployees}</div>
                      <div className="text-xs text-gray-500">Present/Total</div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" onClick={() => navigate('/admin/employees/attendance')}>
                    Mark Attendance <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Pending Overtime</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <Clock className="h-8 w-8 text-badhees-600 mr-3" />
                    <div className="text-3xl font-bold">{pendingOvertimeRequests}</div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" onClick={() => navigate('/admin/employees/overtime')}>
                    Review <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Payroll</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center">
                    <DollarSign className="h-8 w-8 text-badhees-600 mr-3" />
                    <div className="text-sm">
                      <div className="font-medium">Current Month</div>
                      <div className="text-gray-500">{format(today, 'MMMM yyyy')}</div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="pt-0">
                  <Button variant="ghost" size="sm" onClick={() => navigate('/admin/employees/payroll')}>
                    Process Payroll <ArrowRight className="h-4 w-4 ml-1" />
                  </Button>
                </CardFooter>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {/* Pending Overtime Requests */}
              <Card>
                <CardHeader>
                  <CardTitle>Pending Overtime Requests</CardTitle>
                  <CardDescription>
                    Employees with pending overtime approval
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingOvertimeSummary ? (
                    <div className="flex justify-center items-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin text-badhees-accent" />
                      <span className="ml-2 text-badhees-600">Loading overtime data...</span>
                    </div>
                  ) : employeesWithPendingOvertime.length === 0 ? (
                    <div className="text-center py-4">
                      <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                      <p className="text-gray-500">No pending overtime requests.</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Employee</TableHead>
                            <TableHead>Pending Hours</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {employeesWithPendingOvertime.map((record) => (
                            <TableRow key={record.employee_id}>
                              <TableCell className="font-medium">{record.employee_name}</TableCell>
                              <TableCell>{record.pending_hours}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate('/admin/employees/overtime')}
                  >
                    <Clock className="h-4 w-4 mr-2" />
                    Manage Overtime
                  </Button>
                </CardFooter>
              </Card>

              {/* Today's Attendance */}
              <Card>
                <CardHeader>
                  <CardTitle>Today's Attendance</CardTitle>
                  <CardDescription>
                    Attendance status for {format(today, 'MMMM d, yyyy')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isLoadingTodayAttendance || isLoadingEmployees ? (
                    <div className="flex justify-center items-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin text-badhees-accent" />
                      <span className="ml-2 text-badhees-600">Loading attendance data...</span>
                    </div>
                  ) : todayAttendance?.length === 0 ? (
                    <div className="text-center py-4">
                      <AlertTriangle className="h-8 w-8 text-amber-500 mx-auto mb-2" />
                      <p className="text-gray-500">Attendance not marked for today.</p>
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Status</TableHead>
                            <TableHead>Count</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>
                              <Badge className="bg-green-100 text-green-800">Present</Badge>
                            </TableCell>
                            <TableCell>{presentToday}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Badge className="bg-red-100 text-red-800">Absent</Badge>
                            </TableCell>
                            <TableCell>{absentToday}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Badge className="bg-amber-100 text-amber-800">Half-day</Badge>
                            </TableCell>
                            <TableCell>{todayAttendance?.filter(a => a.status === 'half-day').length || 0}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Badge className="bg-blue-100 text-blue-800">Leave</Badge>
                            </TableCell>
                            <TableCell>{todayAttendance?.filter(a => a.status === 'leave').length || 0}</TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    </div>
                  )}
                </CardContent>
                <CardFooter>
                  <Button 
                    variant="outline" 
                    className="w-full"
                    onClick={() => navigate('/admin/employees/attendance')}
                  >
                    <ClipboardList className="h-4 w-4 mr-2" />
                    Manage Attendance
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminEmployeeDashboard;
