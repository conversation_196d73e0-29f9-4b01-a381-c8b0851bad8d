/**
 * Customer and Product Analytics Service
 *
 * This module provides functions to fetch customer and product-related analytics data.
 */
import { supabase } from '@/lib/supabase';
import { handleSupabaseError } from '@/utils/supabaseHelpers';
import { CategoryPerformance, CustomerStats, TopProductsSortBy, TopSellingProduct } from './types';

/**
 * Get customer statistics (total, active, inactive)
 * @returns Customer statistics
 */
export const getCustomerStats = async (): Promise<CustomerStats> => {
  try {
    // Try to use RPC function first
    const { data: rpcData, error: rpcError } = await supabase
      .rpc('get_customer_stats');

    if (!rpcError && rpcData && rpcData.length > 0) {
      const stats = rpcData[0];
      return {
        total: parseInt(stats.total_customers) || 0,
        active: parseInt(stats.active_customers) || 0,
        inactive: 0, // Not tracked anymore
        totalCustomers: parseInt(stats.total_customers) || 0
      };
    }

    // Fallback to individual queries
    console.warn('RPC function failed, using fallback queries:', rpcError);

    // Initialize default values
    let total = 0;
    let active = 0;
    let inactive = 0;
    let totalCustomers = 0;

    try {
      // Get total customers (users with role 'user')
      const { count, error } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true })
        .eq('role', 'user');

      if (!error) {
        total = count || 0;
      } else {
        console.error('Error fetching total users:', error.message);
      }
    } catch (err) {
      console.error('Exception fetching total users:', err);
    }

    // Note: Active/Inactive user tracking has been removed as it's no longer needed
    // Setting default values for backward compatibility
    active = 0;
    inactive = 0;

    try {
      // Get total customers (all users regardless of role)
      const { count, error } = await supabase
        .from('user_profiles')
        .select('*', { count: 'exact', head: true });

      if (!error) {
        totalCustomers = count || 0;
      } else {
        console.error('Error fetching all users:', error.message);
      }
    } catch (err) {
      console.error('Exception fetching all users:', err);
    }

    return {
      total,
      active,
      inactive,
      totalCustomers
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error in getCustomerStats:', errorMessage);
    handleSupabaseError(error, 'getCustomerStats', false);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      totalCustomers: 0
    };
  }
};

/**
 * Get category performance (sales by category)
 * @returns Category performance data
 */
export const getCategoryPerformance = async (): Promise<CategoryPerformance[]> => {
  try {
    // Use direct query instead of RPC function
    const { data, error } = await supabase
      .from('order_items')
      .select(`
        product:products!inner(
          category:categories(name)
        ),
        quantity,
        price,
        order:orders!inner(status)
      `)
      .eq('order.status', 'delivered');

    if (error) {
      console.error('Error fetching category performance:', error);
      return [];
    }

    // Process the data to calculate totals per category
    const categoryMap = new Map();

    data?.forEach((item: any) => {
      const categoryName = item.product?.category?.name || 'Uncategorized';
      const revenue = item.price * item.quantity;

      if (categoryMap.has(categoryName)) {
        categoryMap.set(categoryName, categoryMap.get(categoryName) + revenue);
      } else {
        categoryMap.set(categoryName, revenue);
      }
    });

    // Convert map to array and sort by value
    const result = Array.from(categoryMap.entries()).map(([name, value]) => ({
      name,
      value
    }));

    return result.sort((a, b) => b.value - a.value);
  } catch (error) {
    handleSupabaseError(error, 'getCategoryPerformance', false);
    return [];
  }
};

/**
 * Get top selling products by revenue or quantity
 * @param limit Number of products to return
 * @param sortBy Sort by revenue or quantity
 * @returns Top selling products data
 */
export const getTopSellingProducts = async (
  limit: number = 5,
  sortBy: TopProductsSortBy = 'revenue'
): Promise<TopSellingProduct[]> => {
  try {
    // Use a simpler approach - get products with their order data
    const { data: orderItemsData, error: orderError } = await supabase
      .from('order_items')
      .select(`
        product_id,
        quantity,
        price,
        product:products(id, name, category:categories(name)),
        order:orders!inner(status)
      `)
      .eq('order.status', 'delivered');

    if (orderError) {
      console.error(`Error fetching order items for top products:`, orderError);
      // Fallback to basic product query
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('products')
        .select(`
          id,
          name,
          category:categories(name),
          images:product_images(image_url)
        `)
        .eq('status', 'active')
        .limit(limit);

      if (!fallbackError && fallbackData) {
        return fallbackData.map(product => ({
          id: product.id,
          name: product.name,
          category: product.category?.name || 'Uncategorized',
          total_sales: 0,
          quantity_sold: 0,
          image_url: product.images?.[0]?.image_url || ''
        }));
      }

      return [];
    }

    // Process the data to calculate totals per product
    const productMap = new Map();

    orderItemsData?.forEach((item: any) => {
      const productId = item.product_id;

      if (!productMap.has(productId)) {
        productMap.set(productId, {
          id: productId,
          name: item.product?.name || 'Unknown Product',
          category: item.product?.category?.name || 'Uncategorized',
          total_sales: 0,
          quantity_sold: 0,
          image_url: ''
        });
      }

      const productData = productMap.get(productId);
      productData.total_sales += (item.price * item.quantity);
      productData.quantity_sold += item.quantity;
    });

    // Get product images for the top products
    const productIds = Array.from(productMap.keys());
    if (productIds.length > 0) {
      const { data: imageData } = await supabase
        .from('product_images')
        .select('product_id, image_url')
        .in('product_id', productIds)
        .eq('is_primary', true);

      if (imageData) {
        imageData.forEach((img: any) => {
          const productData = productMap.get(img.product_id);
          if (productData) {
            productData.image_url = img.image_url;
          }
        });
      }
    }

    // Convert map to array and sort
    const result = Array.from(productMap.values());

    if (sortBy === 'revenue') {
      result.sort((a, b) => b.total_sales - a.total_sales);
    } else {
      result.sort((a, b) => b.quantity_sold - a.quantity_sold);
    }

    return result.slice(0, limit);
  } catch (error) {
    handleSupabaseError(error, `getTopSellingProducts (${sortBy})`, false);
    return [];
  }
};

/**
 * Get total products count
 * @returns Total products count
 */
export const getTotalProductsCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (error) {
      console.error('Error fetching total products count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    handleSupabaseError(error, 'getTotalProductsCount', false);
    return 0;
  }
};

/**
 * Get total projects count
 * @returns Total projects count
 */
export const getTotalProjectsCount = async (): Promise<number> => {
  try {
    // Count completed projects
    const { count: completedCount, error: completedError } = await supabase
      .from('completed_projects')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (completedError) {
      console.error('Error fetching completed projects count:', completedError);
      return 0;
    }

    // Count custom projects
    const { count: customCount, error: customError } = await supabase
      .from('custom_projects')
      .select('*', { count: 'exact', head: true })
      .neq('status', 'deleted');

    if (customError) {
      console.error('Error fetching custom projects count:', customError);
      return 0;
    }

    return (completedCount || 0) + (customCount || 0);
  } catch (error) {
    handleSupabaseError(error, 'getTotalProjectsCount', false);
    return 0;
  }
};
