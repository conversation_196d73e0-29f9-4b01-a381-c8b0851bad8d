/**
 * Auth Guard Component
 * 
 * A component that protects routes requiring authentication.
 * Redirects unauthenticated users to the login page.
 */
import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/context/SupabaseAuthContext';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: React.ReactNode;
  adminOnly?: boolean;
}

export const AuthGuard: React.FC<AuthGuardProps> = ({ 
  children, 
  adminOnly = false 
}) => {
  const { isAuthenticated, isLoading, isAdmin } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Redirect to login with return URL
      navigate(`/login?redirect=${encodeURIComponent(location.pathname)}`, { replace: true });
    } else if (!isLoading && adminOnly && !isAdmin()) {
      // If admin access is required but user is not admin
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, isLoading, adminOnly, isAdmin, navigate, location.pathname]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-badhees-600 mb-4" />
            <p className="text-badhees-600">Loading...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  // If not authenticated or (adminOnly is true and user is not admin), don't render children
  if (!isAuthenticated || (adminOnly && !isAdmin())) {
    return null;
  }

  // User is authenticated and has proper permissions
  return <>{children}</>;
};
