import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff } from 'lucide-react';
import { useNetworkStatus } from '@/hooks/use-network-status';
import { cn } from '@/lib/utils';

interface NetworkStatusProps {
  className?: string;
  showOfflineOnly?: boolean;
}

/**
 * Network status indicator component
 * Shows a visual indicator when the user is offline
 * Can optionally show online status as well
 */
export function NetworkStatus({
  className,
  showOfflineOnly = true
}: NetworkStatusProps) {
  const { isOnline } = useNetworkStatus(false); // Don't show toasts, we'll handle UI here
  const [visible, setVisible] = useState(false);
  const [animateOut, setAnimateOut] = useState(false);

  useEffect(() => {
    // If we're offline or we want to show online status too
    if (!isOnline || !showOfflineOnly) {
      setVisible(true);
      setAnimateOut(false);
    } else if (isOnline && showOfflineOnly) {
      // If we're back online and only showing offline status
      // Animate out before hiding
      setAnimateOut(true);
      const timer = setTimeout(() => {
        setVisible(false);
      }, 1000); // Match this to the animation duration
      
      return () => clearTimeout(timer);
    }
  }, [isOnline, showOfflineOnly]);

  if (!visible) return null;

  return (
    <div
      className={cn(
        'fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-full shadow-lg flex items-center gap-2 transition-all duration-300',
        isOnline 
          ? 'bg-green-100 text-green-800' 
          : 'bg-red-100 text-red-800',
        animateOut ? 'opacity-0 translate-y-4' : 'opacity-100 translate-y-0',
        className
      )}
    >
      {isOnline ? (
        <>
          <Wifi className="h-4 w-4" />
          <span className="text-sm font-medium">Online</span>
        </>
      ) : (
        <>
          <WifiOff className="h-4 w-4" />
          <span className="text-sm font-medium">Offline</span>
        </>
      )}
    </div>
  );
}
