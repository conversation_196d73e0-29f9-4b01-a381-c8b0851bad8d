import React from 'react';
import { useAuth } from '@/context/SupabaseAuthContext';

/**
 * A simple component to display the user's authentication status
 * Email, ID and role information have been removed as requested
 */
const UserRoleDisplay: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return <div className="text-sm text-gray-500">Not logged in</div>;
  }

  return (
    <div className="p-2 rounded-md bg-gray-100 text-sm">
      <p>✓ Authenticated</p>
      {/* Email, role and ID information removed as requested */}
    </div>
  );
};

export default UserRoleDisplay;
