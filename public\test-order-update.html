<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Update Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 300px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Order Update Test</h1>
        <p>This page tests the order update functionality to verify payment ID updates.</p>
        
        <div>
            <h3>Test Order Update</h3>
            <div>
                <label>Order ID:</label>
                <input type="text" id="orderId" placeholder="Enter order ID (e.g., 16f4104d)" />
            </div>
            <div>
                <label>Payment ID:</label>
                <input type="text" id="paymentId" placeholder="Enter payment ID (e.g., pay_QdR1U1xrqmv5t1)" />
            </div>
            <button onclick="testOrderUpdate()">Test Order Update</button>
            <button onclick="fetchOrder()">Fetch Order Details</button>
        </div>

        <div id="results"></div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';
        
        // Initialize Supabase client
        const supabaseUrl = 'https://edthfbjthzcusqtmlzd.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVkdGhmYmp0aHpjdXNxdG1semQiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTczMzU2NzE5NywiZXhwIjoyMDQ5MTQzMTk3fQ.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';
        const supabase = createClient(supabaseUrl, supabaseKey);
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        window.testOrderUpdate = async function() {
            const orderId = document.getElementById('orderId').value.trim();
            const paymentId = document.getElementById('paymentId').value.trim();
            
            if (!orderId || !paymentId) {
                addResult('❌ Please enter both Order ID and Payment ID', 'error');
                return;
            }
            
            try {
                addResult(`🔄 Testing order update for Order: ${orderId}, Payment: ${paymentId}`, 'info');
                
                // First, fetch the current order
                const { data: currentOrder, error: fetchError } = await supabase
                    .from('orders')
                    .select('*')
                    .eq('id', orderId)
                    .single();
                
                if (fetchError) {
                    addResult(`❌ Error fetching order: ${fetchError.message}`, 'error');
                    return;
                }
                
                addResult(`📋 Current order status: ${currentOrder.status}, payment_status: ${currentOrder.payment_status}`, 'info');
                addResult(`💳 Current payment ID: ${currentOrder.razorpay_payment_id || 'None'}`, 'info');
                
                // Update the order with payment details
                const updateData = {
                    razorpay_payment_id: paymentId,
                    payment_status: 'paid',
                    status: 'paid',
                    updated_at: new Date().toISOString()
                };
                
                addResult(`📝 Updating order with: ${JSON.stringify(updateData, null, 2)}`, 'info');
                
                const { data: updatedOrder, error: updateError } = await supabase
                    .from('orders')
                    .update(updateData)
                    .eq('id', orderId)
                    .select()
                    .single();
                
                if (updateError) {
                    addResult(`❌ Error updating order: ${updateError.message}`, 'error');
                    return;
                }
                
                addResult(`✅ Order updated successfully!`, 'success');
                addResult(`📋 Updated order: ${JSON.stringify(updatedOrder, null, 2)}`, 'success');
                
            } catch (error) {
                addResult(`❌ Exception: ${error.message}`, 'error');
            }
        };

        window.fetchOrder = async function() {
            const orderId = document.getElementById('orderId').value.trim();
            
            if (!orderId) {
                addResult('❌ Please enter Order ID', 'error');
                return;
            }
            
            try {
                addResult(`🔍 Fetching order details for: ${orderId}`, 'info');
                
                const { data: order, error } = await supabase
                    .from('orders')
                    .select('*')
                    .eq('id', orderId)
                    .single();
                
                if (error) {
                    addResult(`❌ Error fetching order: ${error.message}`, 'error');
                    return;
                }
                
                addResult(`✅ Order found!`, 'success');
                addResult(`📋 Order details: ${JSON.stringify(order, null, 2)}`, 'success');
                
            } catch (error) {
                addResult(`❌ Exception: ${error.message}`, 'error');
            }
        };

        // Auto-populate with the latest order ID from the logs
        window.addEventListener('load', () => {
            addResult('🚀 Order Update Test Page Loaded', 'info');
            addResult('💡 Enter an order ID from the admin dashboard to test payment ID updates', 'info');
            
            // Pre-fill with the order ID from your screenshot
            document.getElementById('orderId').value = '16f4104d';
            document.getElementById('paymentId').value = 'pay_QdR1U1xrqmv5t1';
        });
    </script>
</body>
</html>
