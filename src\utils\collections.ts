
import { getInitialProducts } from '@/services/editorProductsService';

export interface Collection {
  id: string;
  name: string;
  description: string;
  image: string;
  category?: string; // Category name to filter products by
  productCount?: number;
}

export const collections: Collection[] = [
  {
    id: "modern",
    name: "Modern Projects",
    description: "Contemporary designs featuring clean lines, minimalist aesthetics, and innovative materials.",
    image: "https://images.unsplash.com/photo-1567016376408-0226e4d0c1ea?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Living Room"
  },
  {
    id: "scandinavian",
    name: "Scandinavian Inspired",
    description: "Light, airy designs with natural materials and functional simplicity inspired by Nordic aesthetics.",
    image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Chair"
  },
  {
    id: "rustic",
    name: "Rustic Charm",
    description: "Warm, inviting pieces with natural textures and timeless appeal that bring the outdoors in.",
    image: "https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Bedroom"
  },
  {
    id: "luxury",
    name: "Luxury & Elegance",
    description: "Sophisticated designs featuring premium materials and exquisite craftsmanship for discerning tastes.",
    image: "https://images.unsplash.com/photo-1540574163026-643ea20ade25?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80",
    category: "Sofa"
  }
];

// Get a custom project by ID with error handling
export const getCollectionById = (id: string): Collection | undefined => {
  // Defensive programming to handle undefined or missing IDs
  if (!id) return undefined;

  const foundCollection = collections.find(collection => collection.id === id);
  if (!foundCollection) {
    console.warn(`Custom project with ID '${id}' not found`);
  }
  return foundCollection;
};

// Update custom projects with product counts from actual data
export const getCollectionsWithProductCounts = (): Collection[] => {
  try {
    const products = getInitialProducts();

    return collections.map(collection => {
      if (collection.category) {
        const count = products.filter(product =>
          product.category && product.category.toLowerCase() === collection.category?.toLowerCase()
        ).length;

        return {
          ...collection,
          productCount: count
        };
      }
      return collection;
    });
  } catch (error) {
    console.error("Error getting custom projects with product counts:", error);
    return collections; // Return raw collections as fallback
  }
};
