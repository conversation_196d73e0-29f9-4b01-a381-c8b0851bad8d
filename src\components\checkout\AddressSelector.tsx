import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { PlusCircle, Edit, Trash2 } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Address, deleteAddress } from '@/services/addressService';
import AddressForm from './AddressForm';
import { toast } from '@/hooks/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface AddressSelectorProps {
  addresses: Address[];
  selectedAddressId: string | null;
  onAddressSelect: (addressId: string) => void;
  onAddressCreate: (address: Address) => Promise<Address | null>;
  onAddressUpdate: (address: Address) => Promise<Address | null>;
  onAddressDelete: (addressId: string) => Promise<void>;
  title: string;
}

const AddressSelector = ({
  addresses,
  selectedAddressId,
  onAddressSelect,
  onAddressCreate,
  onAddressUpdate,
  onAddressDelete,
  title,
}: AddressSelectorProps) => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<Address | null>(null);
  const [deletingAddressId, setDeletingAddressId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Set the first address as selected if none is selected and addresses exist
  useEffect(() => {
    if (!selectedAddressId && addresses.length > 0) {
      onAddressSelect(addresses[0].id!);
    }
  }, [addresses, selectedAddressId, onAddressSelect]);

  const handleAddAddress = async (address: Address) => {
    setIsSubmitting(true);
    try {
      const newAddress = await onAddressCreate(address);
      if (newAddress) {
        setIsAddDialogOpen(false);
        onAddressSelect(newAddress.id!);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditAddress = async (address: Address) => {
    setIsSubmitting(true);
    try {
      const updatedAddress = await onAddressUpdate(address);
      if (updatedAddress) {
        setIsEditDialogOpen(false);
        setEditingAddress(null);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAddress = async () => {
    if (!deletingAddressId) return;

    try {
      await onAddressDelete(deletingAddressId);
      setIsDeleteDialogOpen(false);
      setDeletingAddressId(null);

      // If the deleted address was selected, select another one if available
      if (deletingAddressId === selectedAddressId && addresses.length > 1) {
        const remainingAddresses = addresses.filter(a => a.id !== deletingAddressId);
        if (remainingAddresses.length > 0) {
          onAddressSelect(remainingAddresses[0].id!);
        }
      }
    } catch (error) {
      console.error('Error deleting address:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete address. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const openEditDialog = (address: Address) => {
    setEditingAddress(address);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (addressId: string) => {
    setDeletingAddressId(addressId);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-base md:text-lg font-medium">{title}</h3>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsAddDialogOpen(true)}
          className="flex items-center gap-1 text-xs md:text-sm py-1 h-8"
        >
          <PlusCircle className="h-3 w-3 md:h-4 md:w-4" />
          Add New
        </Button>
      </div>

      {addresses.length > 0 ? (
        <div className="space-y-3">
          {addresses.map((address) => (
            <div key={address.id} className="relative">
                <Card
                  className={`w-full cursor-pointer border-2 ${selectedAddressId === address.id ? 'border-badhees-600 bg-badhees-50/20' : 'border-gray-200'} hover:border-badhees-600 transition-colors`}
                  onClick={(e) => {
                    e.preventDefault();
                    onAddressSelect(address.id || '');
                  }}
                >
                  <CardContent className="p-2 md:p-3">
                    <div className="flex justify-between">
                      <div className="flex items-start">
                        {selectedAddressId === address.id && (
                          <div className="mr-2 text-badhees-600">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <path d="M20 6L9 17l-5-5" />
                            </svg>
                          </div>
                        )}
                        <div className="space-y-0.5">
                          <p className="font-medium text-sm md:text-base leading-tight">{address.name}</p>
                          <p className="text-xs md:text-sm text-gray-600 leading-tight">{address.street}</p>
                          <p className="text-xs md:text-sm text-gray-600 leading-tight">
                            {address.city}, {address.state} {address.postal_code}
                          </p>
                          <p className="text-xs md:text-sm text-gray-600 leading-tight">{address.country}</p>
                          {address.phone && (
                            <p className="text-xs md:text-sm text-gray-600 leading-tight">Phone: {address.phone}</p>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-1 md:space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.preventDefault();
                            openEditDialog(address);
                          }}
                          className="h-7 w-7 md:h-8 md:w-8"
                        >
                          <Edit className="h-3 w-3 md:h-4 md:w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.preventDefault();
                            openDeleteDialog(address.id!);
                          }}
                          className="h-7 w-7 md:h-8 md:w-8"
                        >
                          <Trash2 className="h-3 w-3 md:h-4 md:w-4" />
                        </Button>
                      </div>
                    </div>
                    {(address.is_default_shipping || address.is_default_billing) && (
                      <div className="mt-2 flex flex-wrap gap-1 md:gap-2 ml-8">
                        {address.is_default_shipping && (
                          <span className="text-xs bg-badhees-100 text-badhees-800 px-1.5 py-0.5 md:px-2 md:py-1 rounded">
                            Default Shipping
                          </span>
                        )}
                        {address.is_default_billing && (
                          <span className="text-xs bg-badhees-100 text-badhees-800 px-1.5 py-0.5 md:px-2 md:py-1 rounded">
                            Default Billing
                          </span>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8 bg-gray-50 rounded-md">
          <p className="text-gray-500 mb-4">No addresses found</p>
          <Button
            variant="outline"
            onClick={() => setIsAddDialogOpen(true)}
            className="flex items-center gap-1"
          >
            <PlusCircle className="h-4 w-4" />
            Add New Address
          </Button>
        </div>
      )}

      {/* Add Address Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Address</DialogTitle>
          </DialogHeader>
          <AddressForm
            onSubmit={handleAddAddress}
            onCancel={() => setIsAddDialogOpen(false)}
            submitLabel={isSubmitting ? "Saving..." : "Save Address"}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Address Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Address</DialogTitle>
          </DialogHeader>
          {editingAddress && (
            <AddressForm
              initialAddress={editingAddress}
              onSubmit={handleEditAddress}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setEditingAddress(null);
              }}
              submitLabel={isSubmitting ? "Saving..." : "Update Address"}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Address Confirmation */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this address. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setDeletingAddressId(null)}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteAddress}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AddressSelector;
