/* Animation delay classes */
.animation-delay-0 { animation-delay: 0ms; }
.animation-delay-150 { animation-delay: 150ms; }
.animation-delay-300 { animation-delay: 300ms; }
.animation-delay-450 { animation-delay: 450ms; }
.animation-delay-600 { animation-delay: 600ms; }
.animation-delay-750 { animation-delay: 750ms; }
.animation-delay-900 { animation-delay: 900ms; }
.animation-delay-1050 { animation-delay: 1050ms; }
.animation-delay-1200 { animation-delay: 1200ms; }
.animation-delay-1350 { animation-delay: 1350ms; }
.animation-delay-1500 { animation-delay: 1500ms; }

.animation-fill-forwards {
  animation-fill-mode: forwards;
}

/* Swipe indicator animation */
@keyframes swipeIndicator {
  0% { opacity: 0; transform: translateX(0); }
  20% { opacity: 1; }
  80% { opacity: 1; }
  100% { opacity: 0; transform: translateX(-20px); }
}

.animate-swipe-indicator {
  animation: swipeIndicator 2s ease-in-out;
  animation-fill-mode: forwards;
}
