/**
 * Product Consistency Styles
 * 
 * This file contains styles to ensure consistent product display
 * across the application, especially for product images.
 */

/* Ensure all product images maintain a consistent aspect ratio */
.product-image-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1/1 !important; /* Force square aspect ratio */
  overflow: hidden;
}

/* Ensure images fill their container properly */
.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.5s ease;
}

/* Hover effect for product images */
.product-card:hover .product-image {
  transform: scale(1.05);
}

/* Consistent card heights */
.product-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Consistent product title heights */
.product-title {
  height: 2.5rem; /* Approximately 2 lines of text */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* Consistent price display */
.product-price {
  font-weight: bold;
  margin-top: auto;
}

/* Consistent button layout */
.product-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

/* Ensure consistent image sizes in product grids */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}

/* Force aspect ratio for all product images */
img.product-image,
.optimized-image-container {
  aspect-ratio: 1/1 !important;
  object-fit: cover !important;
}

/* Ensure consistent spacing in product cards */
.product-card-padding {
  padding: 1rem;
}

/* Consistent product card heights */
@media (min-width: 768px) {
  .product-grid-item {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .product-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  
  .product-actions {
    margin-top: auto;
  }
}

/* Ensure consistent image sizes on mobile */
@media (max-width: 767px) {
  .product-image-container {
    height: 0;
    padding-bottom: 100%; /* Creates a square aspect ratio */
    position: relative;
  }
  
  .product-image-container img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
