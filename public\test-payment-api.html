<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Payment API Test</h1>
        <p>This page tests the payment API endpoints to ensure they're working correctly.</p>
        
        <div>
            <h3>API Server Tests</h3>
            <button onclick="testHealth()">Test Health Check</button>
            <button onclick="testCORS()">Test CORS</button>
            <button onclick="testCreateOrder()">Test Create Order</button>
            <button onclick="testVerifyPayment()">Test Verify Payment (Mock)</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function testHealth() {
            try {
                addResult('Testing health endpoint...', 'info');
                const response = await fetch(`${API_BASE}/api/health`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult(`✅ Health check passed!\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`❌ Health check failed: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Health check error: ${error.message}`, 'error');
            }
        }

        async function testCORS() {
            try {
                addResult('Testing CORS endpoint...', 'info');
                const response = await fetch(`${API_BASE}/api/cors-test`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult(`✅ CORS test passed!\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`❌ CORS test failed: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ CORS test error: ${error.message}`, 'error');
            }
        }

        async function testCreateOrder() {
            try {
                addResult('Testing create order endpoint...', 'info');
                const orderData = {
                    amount: 100,
                    currency: 'INR',
                    receipt: `test_${Date.now()}`,
                    notes: {
                        test: true,
                        description: 'Test order from API test page'
                    }
                };

                const response = await fetch(`${API_BASE}/api/razorpay/create-order`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(orderData)
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult(`✅ Create order passed!\nOrder ID: ${data.data.id}\nAmount: ₹${data.data.amount / 100}\nStatus: ${data.data.status}`, 'success');
                    
                    // Store order ID for verify test
                    window.testOrderId = data.data.id;
                } else {
                    addResult(`❌ Create order failed: ${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Create order error: ${error.message}`, 'error');
            }
        }

        async function testVerifyPayment() {
            try {
                addResult('Testing verify payment endpoint (with mock data)...', 'info');
                
                // Use mock data for testing
                const verifyData = {
                    razorpay_order_id: window.testOrderId || 'order_test123',
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: 'mock_signature_for_testing',
                    order_id: 'temp_test_order'
                };

                const response = await fetch(`${API_BASE}/api/razorpay/verify-payment`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(verifyData)
                });

                const data = await response.json();
                
                if (response.status === 400 && data.error && data.error.includes('Invalid signature')) {
                    addResult(`✅ Verify payment endpoint working correctly!\n(Expected failure with mock data - signature validation working)\nResponse: ${JSON.stringify(data, null, 2)}`, 'success');
                } else if (response.ok && data.success) {
                    addResult(`✅ Verify payment passed!\n${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    addResult(`⚠️ Verify payment response: ${JSON.stringify(data, null, 2)}`, 'info');
                }
            } catch (error) {
                addResult(`❌ Verify payment error: ${error.message}`, 'error');
            }
        }

        // Auto-run health check on page load
        window.addEventListener('load', () => {
            addResult('🚀 Payment API Test Page Loaded', 'info');
            addResult(`Testing API server at: ${API_BASE}`, 'info');
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>
