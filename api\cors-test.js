export default async function handler(req, res) {
  // Set CORS headers for thebadhees.com
  const origin = req.headers.origin;
  console.log('Request origin:', origin);
  console.log('Request method:', req.method);
  console.log('Request headers:', req.headers);
  
  if (origin === 'https://thebadhees.com' || origin === 'http://localhost:5173') {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS preflight request');
    return res.status(200).json({ message: 'CORS preflight successful' });
  }

  try {
    return res.status(200).json({
      success: true,
      message: 'CORS test successful',
      data: {
        method: req.method,
        origin: origin,
        timestamp: new Date().toISOString(),
        headers: req.headers
      }
    });
  } catch (error) {
    console.error('Error in CORS test:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'CORS test failed'
    });
  }
}
