import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { validateImageFile, extractStoragePath, generateUniqueFilename } from '@/utils/supabaseHelpers';

/**
 * Verify that an uploaded image is accessible
 * @param url The image URL to verify
 * @param maxRetries Maximum number of retry attempts
 * @param delay Delay between retries in milliseconds
 */
const verifyImageAccessibility = async (url: string, maxRetries: number = 3, delay: number = 1000): Promise<void> => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔍 Verifying image accessibility (attempt ${attempt}/${maxRetries}):`, url);

      const response = await fetch(url, { method: 'HEAD' });

      if (response.ok) {
        console.log('✅ Image is accessible');
        return;
      } else {
        console.warn(`⚠️ Image not accessible yet (${response.status}), attempt ${attempt}/${maxRetries}`);
      }
    } catch (error) {
      console.warn(`⚠️ Error verifying image (attempt ${attempt}/${maxRetries}):`, error);
    }

    if (attempt < maxRetries) {
      console.log(`⏳ Waiting ${delay}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  console.warn('⚠️ Image verification failed after all retries, but continuing anyway');
};

/**
 * Uploads a product image to Supabase Storage
 * @param file The file to upload
 * @param productId Optional product ID to organize images
 * @returns The URL of the uploaded file, or null if upload failed
 */
export const uploadProductImage = async (
  file: File,
  productId?: string
): Promise<string | null> => {
  try {
    console.log('Starting product image upload:', { fileName: file.name, fileSize: file.size, productId });

    // Validate the file
    const validationError = validateImageFile(file);
    if (validationError) {
      console.error('File validation failed:', validationError);
      toast({
        title: 'Invalid file',
        description: validationError,
        variant: 'destructive',
      });
      return null;
    }

    // Create a unique file name
    const filePath = generateUniqueFilename(file, productId);
    console.log('Generated file path:', filePath);

    // Upload the file to Supabase Storage
    const { data, error } = await supabase.storage
      .from('product-images')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error('Supabase storage upload error:', error);
      throw error;
    }

    console.log('Upload successful, data:', data);

    // Get the public URL with cache busting
    const { data: { publicUrl } } = supabase.storage
      .from('product-images')
      .getPublicUrl(filePath);

    // Add cache busting parameter to ensure fresh load
    const cacheBustedUrl = `${publicUrl}?t=${Date.now()}`;
    console.log('Generated public URL:', publicUrl);
    console.log('Cache-busted URL:', cacheBustedUrl);

    // Verify the image is accessible (with retry)
    await verifyImageAccessibility(cacheBustedUrl);

    return cacheBustedUrl;
  } catch (error) {
    console.error('Error uploading product image:', error);
    toast({
      title: 'Upload failed',
      description: 'Failed to upload image. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Deletes a product image from Supabase Storage
 * @param url The URL of the file to delete
 * @returns True if deletion was successful, false otherwise
 */
export const deleteProductImage = async (url: string): Promise<boolean> => {
  try {
    // Extract the file path from the URL
    const pathInfo = extractStoragePath(url);
    if (!pathInfo) {
      console.error('Invalid storage URL:', url);
      return false;
    }

    // Delete the file
    const { error } = await supabase.storage
      .from(pathInfo.bucketName)
      .remove([pathInfo.filePath]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting product image:', error);
    return false;
  }
};
