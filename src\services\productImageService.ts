import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { validateImageFile, extractStoragePath, generateUniqueFilename } from '@/utils/supabaseHelpers';

/**
 * Uploads a product image to Supabase Storage
 * @param file The file to upload
 * @param productId Optional product ID to organize images
 * @returns The URL of the uploaded file, or null if upload failed
 */
export const uploadProductImage = async (
  file: File,
  productId?: string
): Promise<string | null> => {
  try {
    console.log('Starting product image upload:', { fileName: file.name, fileSize: file.size, productId });

    // Validate the file
    const validationError = validateImageFile(file);
    if (validationError) {
      console.error('File validation failed:', validationError);
      toast({
        title: 'Invalid file',
        description: validationError,
        variant: 'destructive',
      });
      return null;
    }

    // Create a unique file name
    const filePath = generateUniqueFilename(file, productId);
    const fullPath = `product-images/${filePath}`;
    console.log('Generated file path:', fullPath);

    // Upload the file to Supabase Storage (using 'others' bucket like existing images)
    const { data, error } = await supabase.storage
      .from('others')
      .upload(fullPath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error('Supabase storage upload error:', error);
      throw error;
    }

    console.log('Upload successful, data:', data);

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from('others')
      .getPublicUrl(fullPath);

    console.log('Generated public URL:', publicUrl);
    return publicUrl;
  } catch (error) {
    console.error('Error uploading product image:', error);
    toast({
      title: 'Upload failed',
      description: 'Failed to upload image. Please try again.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Deletes a product image from Supabase Storage
 * @param url The URL of the file to delete
 * @returns True if deletion was successful, false otherwise
 */
export const deleteProductImage = async (url: string): Promise<boolean> => {
  try {
    // Extract the file path from the URL
    const pathInfo = extractStoragePath(url);
    if (!pathInfo) {
      console.error('Invalid storage URL:', url);
      return false;
    }

    // Delete the file
    const { error } = await supabase.storage
      .from(pathInfo.bucketName)
      .remove([pathInfo.filePath]);

    if (error) {
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error deleting product image:', error);
    return false;
  }
};
