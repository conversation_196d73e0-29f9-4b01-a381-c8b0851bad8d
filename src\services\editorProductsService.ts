
/**
 * Editor Products Service (Compatibility Layer)
 *
 * This module provides backward compatibility with the old editorProductsService.
 * It re-exports functionality from the new modular product services.
 *
 * @deprecated Use the modular product services instead:
 * import { getProducts, filterProducts, etc. } from '@/services/product';
 */

import { toast } from '@/hooks/use-toast';
import { getProducts as fetchSupabaseProducts, FrontendProduct } from '@/services/product';

// Re-export FrontendProduct as Product for backward compatibility
export type Product = FrontendProduct;

// Default products to use as fallback
const defaultProducts: Product[] = [
  {
    id: '1',
    name: 'Modern Sofa',
    description: 'A comfortable modern sofa with soft cushions.',
    price: 999.99,
    image: 'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    images: [
      'https://images.unsplash.com/photo-1632935190053-28f78a935ed6?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
      'https://images.unsplash.com/photo-1555041469-a586c61ea9bc?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    ],
    category: 'Living Room',
    status: 'active',
    stock: 15,
    sku: 'SOFA-001',
    specifications: {
      'Dimensions': 'W: 84" x D: 38" x H: 34"',
      'Materials': 'Solid oak frame, premium fabric upholstery',
      'Weight': '120 lbs',
      'Assembly': 'Minimal assembly required',
      'Warranty': '5-year manufacturer warranty'
    },
  },
  {
    id: '2',
    name: 'Dining Table',
    description: 'Elegant wooden dining table for family gatherings.',
    price: 549.99,
    image: 'https://images.unsplash.com/photo-1577140917170-285929fb55b7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    images: [
      'https://images.unsplash.com/photo-1533090367196-08fb5ff12b3a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'
    ],
    category: 'Dining Room',
    status: 'active',
    stock: 8,
    sku: 'TABLE-002',
  },
  {
    id: '3',
    name: 'Office Chair',
    description: 'Ergonomic office chair for long working hours.',
    price: 299.99,
    isSale: true,
    salePrice: 249.99,
    image: 'https://images.unsplash.com/photo-1580480055273-228ff5388ef8?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    category: 'Home Office',
    status: 'active',
    stock: 20,
    sku: 'CHAIR-003',
  },
  {
    id: '4',
    name: 'Queen Bed Frame',
    description: 'Stylish queen-sized bed frame with headboard.',
    price: 799.99,
    isNew: true,
    image: 'https://images.unsplash.com/photo-1505693416388-ac5ce068fe85?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    category: 'Bedroom',
    status: 'active',
    stock: 5,
    sku: 'BED-004',
  },
  {
    id: '5',
    name: 'Outdoor Lounge Set',
    description: 'Weather-resistant outdoor lounge set for your patio.',
    price: 1299.99,
    image: 'https://images.unsplash.com/photo-1601285119831-51f2981ff8a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    category: 'Outdoor',
    status: 'active',
    stock: 3,
    sku: 'PATIO-005',
  },
];

// Get products data from Supabase
export const getInitialProducts = async (): Promise<Product[]> => {
  try {
    // Fetch products from Supabase
    const products = await fetchSupabaseProducts();

    // If we have products from Supabase, return them
    if (products && products.length > 0) {
      console.log('Successfully loaded', products.length, 'products from Supabase');

      // Ensure each product has the correct rating data from the database
      const productsWithCorrectRatings = products.map(product => ({
        ...product,
        // Make sure we're using the database values, not default or random values
        rating: product.rating || 0,
        reviewCount: product.reviewCount || 0
      }));

      return productsWithCorrectRatings;
    }

    console.log('No products found in Supabase, using default products');
    // Fallback to default products if Supabase fetch returns empty
    // This should only happen in a fresh installation
    return defaultProducts;
  } catch (error) {
    console.error('Error fetching products from Supabase:', error);

    // Only show toast in production to avoid spamming during development
    if (import.meta.env.PROD) {
      toast({
        title: 'Error fetching products',
        description: 'Failed to load products from the database. Using default products instead.',
        variant: 'destructive',
      });
    }

    // Return default products as fallback
    return defaultProducts;
  }
};

/**
 * @deprecated This function is deprecated and does nothing.
 * Products are now saved directly to Supabase using the product service functions.
 * Use createProduct, updateProduct, or deleteProduct from '@/services/product' instead.
 */
export const saveProducts = (products: Product[]): void => {
  console.warn('saveProducts is deprecated. Products should be saved to Supabase using the product service functions.');
  // No-op - we don't save to localStorage anymore
};

// Filter products based on search query and filters
export const filterProducts = (
  products: Product[],
  searchQuery: string,
  statusFilter: string,
  categoryFilter: string,
  stockFilter: string
): Product[] => {
  let filtered = [...products];

  if (searchQuery) {
    const query = searchQuery.toLowerCase();
    filtered = filtered.filter(product =>
      product.name.toLowerCase().includes(query) ||
      product.description?.toLowerCase().includes(query) ||
      product.category.toLowerCase().includes(query) ||
      product.sku?.toLowerCase().includes(query)
    );
  }

  if (statusFilter !== 'all') {
    filtered = filtered.filter(product => product.status === statusFilter);
  }

  if (categoryFilter !== 'all') {
    filtered = filtered.filter(product =>
      product.category.toLowerCase() === categoryFilter.toLowerCase()
    );
  }

  if (stockFilter !== 'all') {
    switch (stockFilter) {
      case 'inStock':
        filtered = filtered.filter(product => product.stock > 0);
        break;
      case 'lowStock':
        filtered = filtered.filter(product => product.stock > 0 && product.stock < 10);
        break;
      case 'outOfStock':
        filtered = filtered.filter(product => product.stock <= 0);
        break;
    }
  }

  return filtered;
};

// Get unique categories from products
export const getUniqueCategories = (products: Product[]): string[] => {
  return Array.from(new Set(products.map(p => p.category))).filter(Boolean) as string[];
};

// Export the service as a single object
export const editorProductsService = {
  getInitialProducts,
  saveProducts,
  filterProducts,
  getUniqueCategories
};

// Export a message to encourage migration to the new modular structure
export const MIGRATION_MESSAGE =
  "This service is deprecated. Please use the modular product services instead: " +
  "import { getProducts, filterProducts, etc. } from '@/services/product';";
