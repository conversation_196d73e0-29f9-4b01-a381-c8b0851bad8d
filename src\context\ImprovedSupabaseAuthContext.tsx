import React, { createContext, useContext, useState, useEffect, useCallback, useMemo } from 'react';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import { Session, User as SupabaseUser, AuthError } from '@supabase/supabase-js';
import { sendWelcomeEmail } from '@/services/emailService';

// Define the user profile interface
export interface UserProfile {
  id: string;
  display_name?: string;
  email?: string;
  role: 'user' | 'admin';
  avatar_url?: string;
  phone?: string;
  created_at?: string;
  updated_at?: string;
}

// Define the user interface that combines Supabase user and profile data
export interface User {
  id: string;
  email: string;
  name: string;
  displayName?: string;
  role: 'user' | 'admin';
  avatarUrl?: string;
  phone?: string;
}

// Define the auth context interface
export interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (name: string, email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  isAdmin: () => boolean;
  refreshSession: () => Promise<boolean>;
  updateUserProfile: (updatedData: Partial<User>) => Promise<boolean>;
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Session refresh interval in milliseconds (15 minutes)
const SESSION_REFRESH_INTERVAL = 15 * 60 * 1000;

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch user profile from Supabase
  const fetchUserProfile = useCallback(async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return null;
      }

      return data as UserProfile;
    } catch (error) {
      console.error('Exception fetching user profile:', error);
      return null;
    }
  }, []);

  // Create user profile if it doesn't exist
  const createUserProfile = useCallback(async (
    supabaseUser: SupabaseUser,
    displayName?: string
  ): Promise<UserProfile | null> => {
    try {
      const newProfile: Partial<UserProfile> = {
        id: supabaseUser.id,
        display_name: displayName || supabaseUser.email?.split('@')[0] || 'User',
        email: supabaseUser.email,
        role: 'user', // Default role is always 'user'
      };

      const { data, error } = await supabase
        .from('user_profiles')
        .insert(newProfile)
        .select()
        .single();

      if (error) {
        console.error('Error creating user profile:', error);
        return null;
      }

      return data as UserProfile;
    } catch (error) {
      console.error('Exception creating user profile:', error);
      return null;
    }
  }, []);

  // Update the user state with profile data
  const updateUserState = useCallback((supabaseUser: SupabaseUser, profile: UserProfile | null) => {
    if (!profile) {
      // If no profile, set minimal user info
      setUser({
        id: supabaseUser.id,
        email: supabaseUser.email || '',
        name: supabaseUser.email?.split('@')[0] || 'User',
        displayName: supabaseUser.email?.split('@')[0] || 'User',
        role: 'user', // Default to user role if no profile
      });
      setUserProfile(null);
      return;
    }

    // Set user with profile data
    setUser({
      id: supabaseUser.id,
      email: supabaseUser.email || profile.email || '',
      name: profile.display_name || supabaseUser.email?.split('@')[0] || 'User',
      displayName: profile.display_name || supabaseUser.email?.split('@')[0] || 'User',
      role: profile.role || 'user',
      avatarUrl: profile.avatar_url,
      phone: profile.phone,
    });
    setUserProfile(profile);
  }, []);

  // Handle user authentication
  const handleUserAuthentication = useCallback(async (supabaseUser: SupabaseUser) => {
    // Fetch user profile
    let profile = await fetchUserProfile(supabaseUser.id);

    // If profile doesn't exist, create it
    if (!profile) {
      profile = await createUserProfile(
        supabaseUser,
        supabaseUser.user_metadata?.name
      );

      // Send welcome email if profile was just created
      if (profile && supabaseUser.email) {
        try {
          console.log('Sending welcome email to new user from auth handler:', supabaseUser.id);
          sendWelcomeEmail(supabaseUser.id, supabaseUser.email).catch(emailError => {
            console.error('Error sending welcome email:', emailError);
            // Don't block authentication if email fails
          });
        } catch (emailError) {
          console.error('Exception sending welcome email:', emailError);
          // Don't block authentication if email fails
        }
      }
    }

    // Update user state
    updateUserState(supabaseUser, profile);
  }, [fetchUserProfile, createUserProfile, updateUserState]);

  // Refresh the session
  const refreshSession = useCallback(async (): Promise<boolean> => {
    try {
      const { data, error } = await supabase.auth.refreshSession();

      if (error) {
        console.error('Session refresh failed:', error.message);
        return false;
      }

      if (data.session) {
        setSession(data.session);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error refreshing session:', error);
      return false;
    }
  }, []);

  // Initialize auth state
  useEffect(() => {
    let isMounted = true;
    let refreshTimer: NodeJS.Timeout | null = null;

    const initializeAuth = async () => {
      if (!isMounted) return;
      setIsLoading(true);

      try {
        // Get the current session
        const { data: { session } } = await supabase.auth.getSession();

        if (!isMounted) return;
        setSession(session);

        if (session?.user) {
          await handleUserAuthentication(session.user);

          // Set up session refresh timer
          refreshTimer = setInterval(refreshSession, SESSION_REFRESH_INTERVAL);
        } else {
          // Try to refresh the session if no active session is found
          const refreshed = await refreshSession();

          if (!refreshed) {
            setUser(null);
            setUserProfile(null);
          }
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (isMounted) {
          setUser(null);
          setUserProfile(null);
        }
      }

      if (isMounted) {
        setIsLoading(false);
      }
    };

    // Set up auth state change listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (_event, session) => {
        if (!isMounted) return;
        setSession(session);

        if (session?.user) {
          await handleUserAuthentication(session.user);
        } else {
          setUser(null);
          setUserProfile(null);
        }

        if (isMounted) {
          setIsLoading(false);
        }
      }
    );

    initializeAuth();

    // Cleanup function
    return () => {
      isMounted = false;
      if (refreshTimer) clearInterval(refreshTimer);
      subscription.unsubscribe();
    };
  }, [handleUserAuthentication, refreshSession]);

  // Login with Supabase
  const login = useCallback(async (email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Input validation
      if (!email || !email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        toast({
          title: "Login failed",
          description: "Please enter a valid email address.",
          variant: "destructive"
        });
        return false;
      }

      if (!password || password.length < 6) {
        toast({
          title: "Login failed",
          description: "Password must be at least 6 characters.",
          variant: "destructive"
        });
        return false;
      }

      // Sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      toast({
        title: "Login successful",
        description: `Welcome back!`,
      });

      return true;
    } catch (error) {
      const authError = error as AuthError;
      console.error('Login error:', authError);

      toast({
        title: "Login failed",
        description: authError.message || "Invalid email or password. Please try again.",
        variant: "destructive"
      });

      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Register with Supabase
  const register = useCallback(async (name: string, email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true);

      // Input validation
      if (!name || !name.trim()) {
        toast({
          title: "Registration failed",
          description: "Name is required.",
          variant: "destructive"
        });
        return false;
      }

      if (!email || !email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        toast({
          title: "Registration failed",
          description: "Please enter a valid email address.",
          variant: "destructive"
        });
        return false;
      }

      if (!password || password.length < 6) {
        toast({
          title: "Registration failed",
          description: "Password must be at least 6 characters.",
          variant: "destructive"
        });
        return false;
      }

      // Sign up with Supabase
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name,
          },
        },
      });

      if (error) throw error;

      // If user is created but needs email confirmation
      if (data.user && !data.session) {
        toast({
          title: "Registration successful",
          description: "Please check your email to confirm your account.",
        });
        return true;
      }

      // If user is created and automatically signed in
      if (data.user && data.session) {
        toast({
          title: "Registration successful",
          description: `Welcome, ${name}!`,
        });

        // Send welcome email
        try {
          console.log('Sending welcome email to new user:', data.user.id);
          sendWelcomeEmail(data.user.id, email).catch(emailError => {
            console.error('Error sending welcome email:', emailError);
            // Don't block registration if email fails
          });
        } catch (emailError) {
          console.error('Exception sending welcome email:', emailError);
          // Don't block registration if email fails
        }

        return true;
      }

      return false;
    } catch (error) {
      const authError = error as AuthError;
      console.error('Registration error:', authError);

      toast({
        title: "Registration failed",
        description: authError.message || "An unexpected error occurred. Please try again.",
        variant: "destructive"
      });

      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Logout with Supabase
  const logout = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Sign out with scope: 'local' to only remove from current browser
      const { error } = await supabase.auth.signOut({ scope: 'local' });

      if (error) throw error;

      // Clear session and user state
      setSession(null);
      setUser(null);
      setUserProfile(null);

      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
    } catch (error) {
      console.error('Logout error:', error);

      toast({
        title: "Error",
        description: "There was an error logging out. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update user profile
  const updateUserProfile = useCallback(async (updatedData: Partial<User>): Promise<boolean> => {
    if (!user || !userProfile) return false;

    try {
      setIsLoading(true);

      // Prepare update data for user_profiles table
      const profileUpdateData: Partial<UserProfile> = {};

      if (updatedData.displayName) {
        profileUpdateData.display_name = updatedData.displayName;
      }

      if (updatedData.phone) {
        profileUpdateData.phone = updatedData.phone;
      }

      if (updatedData.avatarUrl) {
        profileUpdateData.avatar_url = updatedData.avatarUrl;
      }

      // Add updated_at timestamp
      profileUpdateData.updated_at = new Date().toISOString();

      // Update auth metadata if name is being updated
      if (updatedData.name) {
        const { error: authUpdateError } = await supabase.auth.updateUser({
          data: { name: updatedData.name }
        });

        if (authUpdateError) throw authUpdateError;
      }

      // Update profile in the database
      const { error: profileUpdateError } = await supabase
        .from('user_profiles')
        .update(profileUpdateData)
        .eq('id', user.id);

      if (profileUpdateError) throw profileUpdateError;

      // Fetch the updated profile
      const updatedProfile = await fetchUserProfile(user.id);

      if (updatedProfile) {
        // Update local state
        updateUserState(
          { id: user.id, email: user.email } as SupabaseUser,
          updatedProfile
        );
      }

      toast({
        title: "Profile updated",
        description: "Your profile information has been updated.",
      });

      return true;
    } catch (error) {
      console.error('Error updating profile:', error);

      toast({
        title: "Update failed",
        description: "There was an error updating your profile. Please try again.",
        variant: "destructive"
      });

      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user, userProfile, fetchUserProfile, updateUserState]);

  // Check if user is an admin
  const isAdmin = useCallback((): boolean => {
    // Only return true if the user role is explicitly 'admin'
    return user?.role === 'admin';
  }, [user?.role]);

  // Memoize the context value to prevent unnecessary re-renders
  const value = useMemo(() => ({
    user,
    userProfile,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    isAdmin,
    refreshSession,
    updateUserProfile,
  }), [
    user,
    userProfile,
    isLoading,
    login,
    register,
    logout,
    isAdmin,
    refreshSession,
    updateUserProfile
  ]);

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
