import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";

import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import {
  getContactSubmissions,
  updateContactSubmissionStatus,
  ContactSubmission
} from '@/services/contactService';

import { formatDistanceToNow } from 'date-fns';
import { Search, Filter, Eye, Loader2, Mail, RefreshCw } from 'lucide-react';

const AdminContactSubmissions = () => {
  const [submissions, setSubmissions] = useState<ContactSubmission[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<ContactSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedSubmission, setSelectedSubmission] = useState<ContactSubmission | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  // Check authentication and permissions
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/contact-submissions');
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive"
      });
    } else if (!isAdmin()) {
      navigate('/');
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive"
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Fetch contact submissions
  const fetchSubmissions = async () => {
    setIsLoading(true);
    try {
      // Get contact submissions
      const data = await getContactSubmissions();
      setSubmissions(data);
      setFilteredSubmissions(data);
    } catch (error) {
      console.error('Error fetching contact submissions:', error);
      toast({
        title: 'Error',
        description: 'Failed to load contact submissions',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchSubmissions();
  }, []);

  // Set up real-time subscription to contact submissions changes
  useEffect(() => {
    if (!isAuthenticated || !isAdmin()) return;

    // Subscribe to changes in the contact_submissions table
    const subscription = supabase
      .channel('contact-submissions-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'contact_submissions' },
        () => {
          console.log('Contact submissions changed, refetching...');
          fetchSubmissions();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [isAuthenticated, isAdmin]);

  // Filter submissions when search query or status filter changes
  useEffect(() => {
    let result = [...submissions];

    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(submission => submission.status === statusFilter);
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        submission =>
          submission.full_name.toLowerCase().includes(query) ||
          submission.email.toLowerCase().includes(query) ||
          submission.subject.toLowerCase().includes(query) ||
          submission.message.toLowerCase().includes(query)
      );
    }

    setFilteredSubmissions(result);
  }, [submissions, searchQuery, statusFilter]);

  const handleViewSubmission = (submission: ContactSubmission) => {
    setSelectedSubmission(submission);
    setIsViewDialogOpen(true);

    // If the submission is new, mark it as read
    if (submission.status === 'new') {
      handleUpdateStatus('read');
    }
  };

  const handleUpdateStatus = async (status: 'new' | 'read' | 'replied' | 'archived') => {
    if (!selectedSubmission) return;

    setIsUpdating(true);
    try {
      const success = await updateContactSubmissionStatus(selectedSubmission.id!, status);

      if (success) {
        // Update the local state
        const updatedSubmissions = submissions.map(sub =>
          sub.id === selectedSubmission.id ? { ...sub, status } : sub
        );
        setSubmissions(updatedSubmissions);

        // Update the selected submission
        setSelectedSubmission({ ...selectedSubmission, status });

        // Show success message
        toast({
          title: 'Status Updated',
          description: `Submission status changed to ${status}`,
        });
      }
    } catch (error) {
      console.error('Error updating submission status:', error);
      toast({
        title: 'Update Failed',
        description: 'Could not update the submission status. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'new':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">New</Badge>;
      case 'read':
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Read</Badge>;
      case 'replied':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Replied</Badge>;
      case 'archived':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Archived</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const handleReplyByEmail = (email: string, subject: string) => {
    const mailtoUrl = `mailto:${email}?subject=Re: ${encodeURIComponent(subject)}`;
    window.open(mailtoUrl, '_blank');

    // Mark as replied
    handleUpdateStatus('replied');
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <Breadcrumb className="mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">Dashboard</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Contact Submissions</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Contact Form Submissions</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={fetchSubmissions}
                    disabled={isLoading}
                    className="flex items-center gap-2"
                  >
                    <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                    <Input
                      placeholder="Search submissions..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <div className="flex items-center gap-2">
                    <Filter size={18} className="text-gray-500" />
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Filter by status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        <SelectItem value="new">New</SelectItem>
                        <SelectItem value="read">Read</SelectItem>
                        <SelectItem value="replied">Replied</SelectItem>
                        <SelectItem value="archived">Archived</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {isLoading ? (
                  <div className="flex flex-col justify-center items-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-badhees-600 mb-2" />
                    <p className="text-badhees-600">Loading contact submissions...</p>
                    <p className="text-sm text-gray-500 mt-1">This may take a moment</p>
                  </div>
                ) : filteredSubmissions.length === 0 ? (
                  <div className="text-center py-12 text-gray-500">
                    {searchQuery || statusFilter !== 'all' ? (
                      <p>No matching contact submissions found.</p>
                    ) : (
                      <p>No contact submissions yet.</p>
                    )}
                  </div>
                ) : (
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Subject</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredSubmissions.map((submission) => (
                          <TableRow key={submission.id} className={submission.status === 'new' ? 'bg-blue-50' : ''}>
                            <TableCell>
                              <div>
                                <div className="flex items-center gap-2">
                                  <span className="font-medium">{submission.full_name}</span>
                                  {submission.is_customer ? (
                                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Customer</Badge>
                                  ) : (
                                    <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Visitor</Badge>
                                  )}
                                </div>
                                <div className="text-sm text-gray-500">{submission.email}</div>
                                {submission.phone && (
                                  <div className="text-sm text-gray-500">{submission.phone}</div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell className="max-w-[200px] truncate">{submission.subject}</TableCell>
                            <TableCell>
                              {submission.created_at ? (
                                <span title={new Date(submission.created_at).toLocaleString()}>
                                  {formatDistanceToNow(new Date(submission.created_at), { addSuffix: true })}
                                </span>
                              ) : (
                                'Unknown'
                              )}
                            </TableCell>
                            <TableCell>{getStatusBadge(submission.status)}</TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleViewSubmission(submission)}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* View Submission Dialog */}
      {selectedSubmission && (
        <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Contact Submission Details</DialogTitle>
              <DialogDescription>
                Message from {selectedSubmission.full_name}
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Status:</div>
                <div className="col-span-3">{getStatusBadge(selectedSubmission.status)}</div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">From:</div>
                <div className="col-span-3 flex items-center gap-2">
                  <span>{selectedSubmission.full_name}</span>
                  {selectedSubmission.is_customer ? (
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Customer</Badge>
                  ) : (
                    <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">Visitor</Badge>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Email:</div>
                <div className="col-span-3">
                  <a href={`mailto:${selectedSubmission.email}`} className="text-blue-600 hover:underline">
                    {selectedSubmission.email}
                  </a>
                </div>
              </div>

              {selectedSubmission.phone && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="font-medium">Phone:</div>
                  <div className="col-span-3">
                    <a href={`tel:${selectedSubmission.phone}`} className="text-blue-600 hover:underline">
                      {selectedSubmission.phone}
                    </a>
                  </div>
                </div>
              )}

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Subject:</div>
                <div className="col-span-3">{selectedSubmission.subject}</div>
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <div className="font-medium">Message:</div>
                <div className="col-span-3 whitespace-pre-wrap bg-gray-50 p-3 rounded-md">
                  {selectedSubmission.message}
                </div>
              </div>

              {selectedSubmission.user_id && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <div className="font-medium">User ID:</div>
                  <div className="col-span-3 text-sm font-mono bg-gray-50 p-1 rounded">
                    {selectedSubmission.user_id}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-4 items-center gap-4">
                <div className="font-medium">Date:</div>
                <div className="col-span-3">
                  {selectedSubmission.created_at ? new Date(selectedSubmission.created_at).toLocaleString() : 'Unknown'}
                </div>
              </div>
            </div>

            <DialogFooter className="flex-col sm:flex-row gap-2">
              <Button
                variant="default"
                onClick={() => handleReplyByEmail(selectedSubmission.email, selectedSubmission.subject)}
                className="flex items-center"
              >
                <Mail className="mr-2 h-4 w-4" />
                Reply by Email
              </Button>

              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleUpdateStatus('archived')}
                  disabled={selectedSubmission.status === 'archived' || isUpdating}
                  className={selectedSubmission.status === 'archived' ? 'bg-yellow-50' : ''}
                >
                  {isUpdating ? <Loader2 className="h-4 w-4 mr-1 animate-spin" /> : null}
                  Archive
                </Button>
                <Button variant="secondary" onClick={() => setIsViewDialogOpen(false)}>
                  Close
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      <Footer />
    </div>
  );
};

export default AdminContactSubmissions;
