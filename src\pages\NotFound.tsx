
import { useLocation, Link } from "react-router-dom";
import { useEffect } from "react";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Home, Search, ChevronRight } from "lucide-react";
import { collections } from "@/utils/collections";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  const isProductError = location.pathname.includes("/product/");
  const productId = isProductError ? location.pathname.split("/product/")[1] : null;

  // Check for custom project path errors
  const isCollectionError = location.pathname.includes("/collections/");
  const collectionPath = isCollectionError ? location.pathname.split("/collections/")[1] : null;
  const suggestedCollection = collectionPath ?
    collections.find(c =>
      c.name.toLowerCase().includes(collectionPath.toLowerCase()) ||
      c.id.toLowerCase().includes(collectionPath.toLowerCase())
    ) : null;

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow flex items-center justify-center bg-gray-50 py-12">
        <div className="max-w-md w-full mx-auto text-center px-4">
          <h1 className="text-6xl font-bold text-badhees-800 mb-4">404</h1>
          <p className="text-xl text-badhees-600 mb-8">Oops! Page not found</p>

          {isProductError && (
            <div className="mb-8 p-4 border border-amber-200 bg-amber-50 rounded-lg">
              <p className="text-amber-700 mb-2">It looks like you're trying to view a product.</p>
              <p className="text-amber-700 mb-4">The correct URL format is: <code className="bg-white px-2 py-1 rounded">/products/{productId}</code></p>
              <Link
                to={`/products/${productId}`}
                className="text-badhees-accent hover:underline inline-flex items-center"
              >
                Go to correct product page
                <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          )}

          {isCollectionError && suggestedCollection && (
            <div className="mb-8 p-4 border border-amber-200 bg-amber-50 rounded-lg">
              <p className="text-amber-700 mb-2">It looks like you're trying to view a custom project.</p>
              <p className="text-amber-700 mb-4">Did you mean to visit: <code className="bg-white px-2 py-1 rounded">{suggestedCollection.name}</code>?</p>
              <Link
                to="/custom-interiors"
                className="text-badhees-accent hover:underline inline-flex items-center"
              >
                Go to Custom Projects
                <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/"
              className="px-6 py-3 bg-badhees-800 text-white rounded-md font-medium hover:bg-badhees-700 inline-flex items-center justify-center"
            >
              <Home className="h-4 w-4 mr-2" />
              Return to Home
            </Link>
            <Link
              to="/products"
              className="px-6 py-3 border border-badhees-300 text-badhees-700 rounded-md font-medium hover:bg-badhees-50 inline-flex items-center justify-center"
            >
              <Search className="h-4 w-4 mr-2" />
              Browse Products
            </Link>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default NotFound;
