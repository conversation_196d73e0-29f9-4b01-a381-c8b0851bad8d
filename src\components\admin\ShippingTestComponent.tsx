/**
 * Shipping Test Component
 * 
 * A simple component to test shipping calculations
 */
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { calculateShippingFee, formatShippingFee, getShippingMessage } from '@/services/shippingService';

const ShippingTestComponent: React.FC = () => {
  const [isBangaloreDelivery, setIsBangaloreDelivery] = useState<boolean | null>(null);
  const [shippingResult, setShippingResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Sample cart items for testing
  const sampleCartItems = [
    {
      id: '1',
      product_id: 'sample-product-1',
      quantity: 2,
      price: 500,
      product: {
        name: 'Test Product 1',
        shippingFeeBangalore: 50,
        shippingFeeOutsideBangalore: 0,
        freeShippingThreshold: 1000,
        shippingNotes: 'Free shipping within Bangalore for orders above ₹1000'
      }
    },
    {
      id: '2',
      product_id: 'sample-product-2',
      quantity: 1,
      price: 800,
      product: {
        name: 'Test Product 2',
        shippingFeeBangalore: 75,
        shippingFeeOutsideBangalore: 0,
        freeShippingThreshold: 1500,
        shippingNotes: 'Free shipping within Bangalore for orders above ₹1500'
      }
    }
  ];

  const testShippingCalculation = async () => {
    setIsLoading(true);
    try {
      const result = await calculateShippingFee(sampleCartItems, isBangaloreDelivery);
      setShippingResult(result);
    } catch (error) {
      console.error('Error testing shipping calculation:', error);
      setShippingResult({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  const subtotal = sampleCartItems.reduce((total, item) => total + (item.price * item.quantity), 0);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Shipping System Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Sample Cart Items */}
        <div>
          <h3 className="font-semibold mb-2">Sample Cart Items:</h3>
          <div className="space-y-2">
            {sampleCartItems.map((item, index) => (
              <div key={index} className="bg-gray-50 p-2 rounded text-sm">
                <div className="font-medium">{item.product.name}</div>
                <div>Price: ₹{item.price} × {item.quantity} = ₹{item.price * item.quantity}</div>
                <div className="text-xs text-gray-600">
                  Bangalore Shipping: ₹{item.product.shippingFeeBangalore} | 
                  Free Shipping Threshold: ₹{item.product.freeShippingThreshold}
                </div>
              </div>
            ))}
            <div className="font-semibold">Subtotal: ₹{subtotal}</div>
          </div>
        </div>

        {/* Delivery Location Selection */}
        <div className="space-y-2">
          <h3 className="font-semibold">Delivery Location:</h3>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="bangalore-test"
              checked={isBangaloreDelivery === true}
              onCheckedChange={(checked) => setIsBangaloreDelivery(checked === true ? true : null)}
            />
            <label htmlFor="bangalore-test" className="text-sm">
              Within Bangalore area limits
            </label>
          </div>
          <div className="text-sm text-gray-600">
            Current selection: {
              isBangaloreDelivery === true ? 'Bangalore' : 
              isBangaloreDelivery === false ? 'Outside Bangalore' : 
              'Not specified (Outside Bangalore)'
            }
          </div>
        </div>

        {/* Test Button */}
        <Button 
          onClick={testShippingCalculation} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? 'Calculating...' : 'Test Shipping Calculation'}
        </Button>

        {/* Results */}
        {shippingResult && (
          <div className="space-y-2">
            <h3 className="font-semibold">Shipping Calculation Result:</h3>
            {shippingResult.error ? (
              <div className="bg-red-50 border border-red-200 p-3 rounded text-red-700">
                Error: {shippingResult.error}
              </div>
            ) : (
              <div className="bg-blue-50 border border-blue-200 p-3 rounded space-y-2">
                <div><strong>Shipping Fee:</strong> {formatShippingFee(shippingResult)}</div>
                <div><strong>Is Free Shipping:</strong> {shippingResult.isFreeShipping ? 'Yes' : 'No'}</div>
                <div><strong>Is Manual Calculation:</strong> {shippingResult.isManualCalculation ? 'Yes' : 'No'}</div>
                <div><strong>Eligible for Free Shipping:</strong> {shippingResult.eligibleForFreeShipping ? 'Yes' : 'No'}</div>
                {shippingResult.freeShippingThreshold && (
                  <div><strong>Free Shipping Threshold:</strong> ₹{shippingResult.freeShippingThreshold}</div>
                )}
                <div><strong>Message:</strong> {getShippingMessage(shippingResult)}</div>
                {shippingResult.notes && shippingResult.notes.length > 0 && (
                  <div>
                    <strong>Notes:</strong>
                    <ul className="list-disc list-inside text-sm">
                      {shippingResult.notes.map((note, index) => (
                        <li key={index}>{note}</li>
                      ))}
                    </ul>
                  </div>
                )}
                <div className="border-t pt-2 mt-2">
                  <strong>Total Order Amount:</strong> ₹{subtotal + (shippingResult.shippingFee || 0)}
                  {shippingResult.isManualCalculation && (
                    <span className="text-amber-600 text-sm ml-1">+ shipping (TBD)</span>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ShippingTestComponent;
