import { PostgrestError, AuthError } from '@supabase/supabase-js';
import { toast } from '@/hooks/use-toast';

/**
 * Error codes that might indicate a network or connection issue
 */
const NETWORK_ERROR_CODES = ['NETWORK_ERROR', 'CONNECTION_ERROR', 'TIMEOUT_ERROR'];

/**
 * Error codes that might indicate an authentication issue
 */
const AUTH_ERROR_CODES = ['UNAUTHORIZED', 'UNAUTHENTICATED', 'JWT_INVALID', 'JWT_EXPIRED'];

/**
 * Maps common Supabase error codes to user-friendly messages
 */
const ERROR_MESSAGE_MAP: Record<string, string> = {
  'PGRST116': 'The requested resource was not found.',
  'PGRST201': 'The database query failed due to invalid parameters.',
  'PGRST202': 'The requested function was not found.',
  '23505': 'A record with this information already exists.',
  '23503': 'This operation failed because it references a record that does not exist.',
  '42P01': 'The requested table does not exist.',
  '42703': 'The requested column does not exist.',
  'P0001': 'The operation was cancelled by a database trigger.',
};

/**
 * Handle Supabase PostgrestError with user-friendly messages
 */
export const handleSupabaseError = (
  error: PostgrestError | AuthError | Error | unknown,
  context: string = 'operation'
): void => {
  console.error(`Error in ${context}:`, error);
  
  let title = `Error`;
  let description = 'An unexpected error occurred. Please try again.';
  
  // Handle PostgrestError
  if (error && typeof error === 'object' && 'code' in error) {
    const errorCode = (error as PostgrestError).code;
    
    // Check for network errors
    if (NETWORK_ERROR_CODES.includes(errorCode)) {
      title = 'Connection Error';
      description = 'Please check your internet connection and try again.';
    } 
    // Check for auth errors
    else if (AUTH_ERROR_CODES.includes(errorCode)) {
      title = 'Authentication Error';
      description = 'Your session may have expired. Please log in again.';
    }
    // Check for mapped error messages
    else if (errorCode && ERROR_MESSAGE_MAP[errorCode]) {
      description = ERROR_MESSAGE_MAP[errorCode];
    }
    // Use the error message if available
    else if ('message' in error && typeof error.message === 'string') {
      description = error.message;
    }
  } 
  // Handle standard Error objects
  else if (error instanceof Error) {
    description = error.message;
  }
  
  toast({
    title,
    description,
    variant: 'destructive',
  });
};

/**
 * Wraps a Supabase operation with error handling
 */
export const withSupabaseErrorHandling = <T>(
  operation: () => Promise<{ data: T | null; error: PostgrestError | null }>,
  context: string = 'operation'
): Promise<T | null> => {
  return operation()
    .then(({ data, error }) => {
      if (error) {
        handleSupabaseError(error, context);
        return null;
      }
      return data;
    })
    .catch((error) => {
      handleSupabaseError(error, context);
      return null;
    });
};
