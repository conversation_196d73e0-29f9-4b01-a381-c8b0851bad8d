import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { createCustomizationRequest } from '@/services/customizationRequestService';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Loader2, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

interface CustomizationRequestFormProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string;
  productName: string;
}

const CustomizationRequestForm: React.FC<CustomizationRequestFormProps> = ({
  isOpen,
  onClose,
  productId,
  productName,
}) => {
  const { user, isAuthenticated, userProfile } = useAuth();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAuthPrompt, setShowAuthPrompt] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    description: '',
  });

  // Update form data when user data changes
  useEffect(() => {
    if (isAuthenticated && user) {
      setFormData(prev => ({
        ...prev,
        name: userProfile?.display_name || user.name || user.email?.split('@')[0] || '',
        email: user.email || '',
        phone: userProfile?.phone || '',
      }));
    }
  }, [isAuthenticated, user, userProfile]);

  // Check authentication when form opens
  useEffect(() => {
    if (isOpen && !isAuthenticated) {
      setShowAuthPrompt(true);
    } else {
      setShowAuthPrompt(false);
    }
  }, [isOpen, isAuthenticated]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleAuthRedirect = () => {
    onClose();
    navigate('/login', {
      state: {
        returnTo: window.location.pathname,
        message: 'Please sign in to submit a customization request'
      }
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Double-check authentication
    if (!isAuthenticated) {
      toast({
        title: 'Authentication required',
        description: 'Please sign in to submit a customization request',
        variant: 'destructive',
      });
      setShowAuthPrompt(true);
      return;
    }

    if (!formData.name.trim() || !formData.email.trim() || !formData.description.trim()) {
      toast({
        title: 'Missing information',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const request = {
        user_id: user?.id, // Always include user_id for authenticated users
        product_id: productId,
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim() || null,
        description: formData.description.trim(),
        status: 'pending' as const,
      };

      const result = await createCustomizationRequest(request);

      if (result) {
        // Reset only the description field, keep user data
        setFormData(prev => ({
          ...prev,
          description: '',
        }));
        onClose();
      }
    } catch (error) {
      console.error('Error submitting customization request:', error);
      toast({
        title: 'Error',
        description: 'Failed to submit your customization request. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show authentication prompt if user is not signed in
  if (showAuthPrompt) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[400px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Sign In Required
            </DialogTitle>
            <DialogDescription>
              You need to sign in to submit a customization request. This helps us track your request and contact you with updates.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <p className="text-sm text-gray-600 mb-4">
              Product: <span className="font-medium">{productName}</span>
            </p>
            <p className="text-sm text-gray-500">
              After signing in, you'll be able to submit your customization request with your details automatically filled in.
            </p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleAuthRedirect}>
              Sign In
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Request Customization</DialogTitle>
          <DialogDescription>
            Tell us how you'd like to customize the {productName}. Our team will contact you to discuss your requirements.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4 py-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name <span className="text-red-500">*</span></Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email <span className="text-red-500">*</span></Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <Input
              id="phone"
              name="phone"
              type="tel"
              value={formData.phone}
              onChange={handleChange}
              placeholder="Optional"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">
              Customization Details <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Describe how you'd like to customize this product..."
              rows={5}
              required
            />
          </div>

          <div className="text-sm text-gray-500">
            <p>Product: {productName}</p>
            <p>Product ID: {productId}</p>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                'Submit Request'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CustomizationRequestForm;
