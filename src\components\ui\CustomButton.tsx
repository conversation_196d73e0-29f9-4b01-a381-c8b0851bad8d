
import React from "react";
import { cn } from "@/lib/utils";

interface CustomButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "primary" | "secondary" | "outline" | "ghost" | "link";
  size?: "sm" | "md" | "lg";
  isLoading?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const CustomButton = React.forwardRef<HTMLButtonElement, CustomButtonProps>(
  (
    {
      children,
      className,
      variant = "primary",
      size = "md",
      isLoading = false,
      fullWidth = false,
      leftIcon,
      rightIcon,
      disabled,
      ...props
    },
    ref
  ) => {
    // Variant styles
    const variantStyles = {
      primary: "bg-badhees-800 text-white hover:bg-badhees-700",
      secondary: "bg-badhees-100 text-badhees-800 hover:bg-badhees-200",
      outline: "bg-transparent border border-badhees-200 text-badhees-800 hover:bg-badhees-50",
      ghost: "bg-transparent text-badhees-800 hover:bg-badhees-100",
      link: "bg-transparent text-badhees-accent underline-offset-4 hover:underline p-0",
    };

    // Size styles
    const sizeStyles = {
      sm: "text-xs px-3 py-2",
      md: "text-sm px-4 py-2.5",
      lg: "text-base px-6 py-3",
    };

    return (
      <button
        ref={ref}
        disabled={disabled || isLoading}
        className={cn(
          "inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-badhees-accent focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",
          variantStyles[variant],
          variant !== "link" && sizeStyles[size],
          fullWidth && "w-full",
          className
        )}
        {...props}
      >
        {isLoading && (
          <div className="mr-2 flex items-center">
            <span className="loading-dot mr-1"></span>
            <span className="loading-dot mr-1" style={{ animationDelay: "150ms" }}></span>
            <span className="loading-dot" style={{ animationDelay: "300ms" }}></span>
          </div>
        )}
        {!isLoading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        {children}
        {!isLoading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </button>
    );
  }
);

CustomButton.displayName = "CustomButton";
export default CustomButton;
