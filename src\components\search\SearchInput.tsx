import React, { useState, useEffect, useRef } from 'react';
import { Search, Loader2, X } from 'lucide-react';
import { useDebounce } from '@/hooks/use-optimized-render';
import { cn } from '@/lib/utils';
import './search-input.css';

export interface SearchInputProps {
  /**
   * Placeholder text for the search input
   */
  placeholder?: string;

  /**
   * Current search query value
   */
  value: string;

  /**
   * Callback when the search query changes
   */
  onChange: (value: string) => void;

  /**
   * Callback when the search is submitted
   */
  onSearch: (query: string) => void;

  /**
   * Whether the search is currently loading
   */
  isLoading?: boolean;

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Whether to show the search button
   */
  showSearchButton?: boolean;

  /**
   * Whether to show the clear button
   */
  showClearButton?: boolean;

  /**
   * Whether to auto-focus the input
   */
  autoFocus?: boolean;

  /**
   * Debounce delay in milliseconds
   */
  debounceMs?: number;

  /**
   * Whether to submit on Enter key
   */
  submitOnEnter?: boolean;

  /**
   * Minimum characters required before triggering search
   */
  minChars?: number;
}

/**
 * A reusable search input component with debounce functionality
 */
export const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = "Search...",
  value,
  onChange,
  onSearch,
  isLoading = false,
  className = "",
  showSearchButton = true,
  showClearButton = true,
  autoFocus = false,
  debounceMs = 300,
  submitOnEnter = true,
  minChars = 2,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);

  // Debounced search function with improved stability
  const debouncedSearch = useDebounce((query: string) => {
    if (query.trim().length >= minChars) {
      onSearch(query);
    } else if (query.trim().length === 0) {
      // Clear results when query is empty
      onSearch('');
    }
  }, debounceMs);

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (value.trim().length >= minChars) {
      onSearch(value);
    }
  };

  // Handle clear button click
  const handleClear = () => {
    onChange('');
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Handle key press
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && submitOnEnter) {
      e.preventDefault();
      if (value.trim().length >= minChars) {
        onSearch(value);
      }
    }
  };

  // Trigger debounced search when value changes (only for typing, not programmatic changes)
  useEffect(() => {
    if (value.trim().length >= minChars || value.trim().length === 0) {
      debouncedSearch(value);
    }
  }, [value]); // Remove debouncedSearch from dependencies to prevent infinite loop

  return (
    <form onSubmit={handleSubmit} className={cn("search-input-container", className)}>
      <Search className="search-input-icon" />

      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className={cn(
          "search-input",
          showSearchButton && "rounded-r-none"
        )}
        autoFocus={autoFocus}
        aria-label={placeholder}
      />

      {showClearButton && value && (
        <button
          type="button"
          onClick={handleClear}
          className={cn(
            "search-clear-button",
            showSearchButton && "right-[60px]"
          )}
          aria-label="Clear search"
        >
          <X className="h-5 w-5" />
        </button>
      )}

      {isLoading && (
        <Loader2
          className={cn(
            "search-loading-indicator animate-spin",
            showSearchButton ? "right-[60px]" : "right-3"
          )}
        />
      )}

      {showSearchButton && (
        <button
          type="submit"
          className="search-submit-button"
          disabled={isLoading || value.trim().length < minChars}
          aria-label="Search"
        >
          <Search className="h-5 w-5" />
        </button>
      )}
    </form>
  );
};
