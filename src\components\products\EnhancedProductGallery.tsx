import React, { useState, useEffect, useRef } from 'react';
import { ChevronLeft, ChevronRight, ZoomIn, ZoomOut, RotateCw, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button-system';
import './product-gallery.css';

interface EnhancedProductGalleryProps {
  images: string[];
  productName: string;
  enable360View?: boolean;
  view360Images?: string[];
}

const EnhancedProductGallery: React.FC<EnhancedProductGalleryProps> = ({
  images,
  productName,
  enable360View = false,
  view360Images = [],
}) => {
  const [activeImage, setActiveImage] = useState(0);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [is360ViewActive, setIs360ViewActive] = useState(false);
  const [rotation, setRotation] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartX, setDragStartX] = useState(0);
  const [autoRotate, setAutoRotate] = useState(false);
  const [touchStartX, setTouchStartX] = useState<number | null>(null);

  const imageContainerRef = useRef<HTMLDivElement>(null);
  const rotationInterval = useRef<NodeJS.Timeout | null>(null);

  // Auto-rotate images every 4 seconds if not being interacted with
  useEffect(() => {
    if (images.length <= 1 || isZoomed || is360ViewActive) return;

    const interval = setInterval(() => {
      setActiveImage((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    }, 4000);

    return () => clearInterval(interval);
  }, [images.length, isZoomed, is360ViewActive]);

  // Handle 360 view auto-rotation
  useEffect(() => {
    if (!is360ViewActive || !autoRotate) {
      if (rotationInterval.current) {
        clearInterval(rotationInterval.current);
        rotationInterval.current = null;
      }
      return;
    }

    rotationInterval.current = setInterval(() => {
      setRotation((prev) => (prev >= 350 ? 0 : prev + 10));
    }, 100);

    return () => {
      if (rotationInterval.current) {
        clearInterval(rotationInterval.current);
        rotationInterval.current = null;
      }
    };
  }, [is360ViewActive, autoRotate]);

  const handlePrevImage = () => {
    setActiveImage((prev) => (prev === 0 ? images.length - 1 : prev - 1));
  };

  const handleNextImage = () => {
    setActiveImage((prev) => (prev === images.length - 1 ? 0 : prev + 1));
  };

  const handleZoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 0.5, 3));
  };

  const handleZoomOut = () => {
    setZoomLevel((prev) => Math.max(prev - 0.5, 1));
  };

  const handleZoomToggle = () => {
    setIsZoomed((prev) => !prev);
    if (!isZoomed) {
      setZoomLevel(1.5);
    } else {
      setZoomLevel(1);
    }
  };

  const handle360ViewToggle = () => {
    setIs360ViewActive((prev) => !prev);
    setRotation(0);
    setAutoRotate(false);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!is360ViewActive) return;

    setIsDragging(true);
    setDragStartX(e.clientX);
    setAutoRotate(false);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !is360ViewActive) return;

    const deltaX = e.clientX - dragStartX;
    const rotationDelta = deltaX * 0.5; // Adjust sensitivity

    setRotation((prev) => {
      let newRotation = prev + rotationDelta;
      if (newRotation < 0) newRotation += 360;
      if (newRotation >= 360) newRotation -= 360;
      return newRotation;
    });

    setDragStartX(e.clientX);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Touch event handlers for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX);

    if (is360ViewActive) {
      setIsDragging(true);
      setDragStartX(e.touches[0].clientX);
      setAutoRotate(false);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    if (is360ViewActive && isDragging) {
      const deltaX = e.touches[0].clientX - dragStartX;
      const rotationDelta = deltaX * 0.5;

      setRotation((prev) => {
        let newRotation = prev + rotationDelta;
        if (newRotation < 0) newRotation += 360;
        if (newRotation >= 360) newRotation -= 360;
        return newRotation;
      });

      setDragStartX(e.touches[0].clientX);
    }
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (is360ViewActive) {
      setIsDragging(false);
      return;
    }

    if (touchStartX === null) return;

    const touchEndX = e.changedTouches[0].clientX;
    const diff = touchStartX - touchEndX;

    // Swipe threshold of 50px
    if (Math.abs(diff) > 50) {
      if (diff > 0) {
        // Swipe left - next image
        handleNextImage();
      } else {
        // Swipe right - previous image
        handlePrevImage();
      }
    }

    setTouchStartX(null);
  };

  // Get the current image to display based on mode
  const getCurrentImage = () => {
    if (is360ViewActive) {
      if (view360Images.length === 0) return images[activeImage];

      // Calculate which 360 image to show based on rotation
      const imageIndex = Math.floor((rotation / 360) * view360Images.length) % view360Images.length;
      return view360Images[imageIndex];
    }

    return images[activeImage];
  };

  return (
    <div className="space-y-4">
      {/* Main image container */}
      <div
        ref={imageContainerRef}
        className="relative overflow-hidden rounded-xl bg-badhees-50 cursor-pointer"
        onClick={handleZoomToggle}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className="aspect-square relative">
          <img
            src={getCurrentImage()}
            alt={productName}
            className={cn(
              "gallery-image",
              isZoomed ? "gallery-image-zoomed" : "gallery-image-normal",
              `zoom-level-${zoomLevel.toString().replace('.', '-')}`
            )}
          />

          {/* Controls overlay */}
          <div className="absolute bottom-4 right-4 flex space-x-2">
            <Button
              variant="secondary"
              size="icon-sm"
              rounded="full"
              className="bg-white bg-opacity-70 hover:bg-opacity-100"
              onClick={(e) => {
                e.stopPropagation();
                handleZoomToggle();
              }}
              aria-label={isZoomed ? "Zoom out" : "Zoom in"}
            >
              {isZoomed ? <ZoomOut className="h-4 w-4" /> : <ZoomIn className="h-4 w-4" />}
            </Button>

            {enable360View && (
              <Button
                variant="secondary"
                size="icon-sm"
                rounded="full"
                className={cn(
                  "bg-white bg-opacity-70 hover:bg-opacity-100",
                  is360ViewActive && "bg-badhees-accent text-white hover:bg-badhees-700"
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  handle360ViewToggle();
                }}
                aria-label="Toggle 360° view"
              >
                <RotateCw className="h-4 w-4" />
              </Button>
            )}
          </div>

          {/* 360 view indicator */}
          {is360ViewActive && (
            <div className="absolute top-4 left-4 bg-badhees-accent text-white text-xs font-medium px-3 py-1 rounded-full">
              360° View - Drag to rotate
            </div>
          )}
        </div>
      </div>

      {/* Thumbnail navigation */}
      {images.length > 1 && !is360ViewActive && (
        <div className="grid grid-cols-5 sm:grid-cols-6 md:grid-cols-5 lg:grid-cols-6 gap-2 sm:gap-4">
          {images.map((img, index) => (
            <button
              type="button"
              key={index}
              className={cn(
                "rounded-lg overflow-hidden border-2 transition-colors touch-target",
                activeImage === index
                  ? "border-badhees-accent"
                  : "border-transparent hover:border-badhees-200"
              )}
              onClick={() => setActiveImage(index)}
              aria-label={`View image ${index + 1} of ${productName}`}
            >
              <img
                src={img}
                alt={`${productName} view ${index + 1}`}
                className="w-full h-full object-cover aspect-square"
                loading="lazy"
              />
            </button>
          ))}

          {enable360View && (
            <button
              type="button"
              className={cn(
                "rounded-lg overflow-hidden border-2 transition-colors flex items-center justify-center bg-badhees-50",
                is360ViewActive
                  ? "border-badhees-accent"
                  : "border-transparent hover:border-badhees-200"
              )}
              onClick={handle360ViewToggle}
              aria-label="Toggle 360° view"
            >
              <RotateCw className="h-6 w-6 text-badhees-600" />
            </button>
          )}
        </div>
      )}

      {/* 360 view controls */}
      {is360ViewActive && (
        <div className="flex items-center justify-between bg-badhees-50 p-3 rounded-lg">
          <span className="text-sm text-badhees-600">Drag to rotate or use controls</span>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setAutoRotate(!autoRotate)}
              className={autoRotate ? "bg-badhees-100" : ""}
            >
              {autoRotate ? "Stop" : "Auto-Rotate"}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handle360ViewToggle}
            >
              Exit 360° View
            </Button>
          </div>
        </div>
      )}

      {/* Zoom Dialog */}
      <Dialog open={isZoomed} onOpenChange={setIsZoomed}>
        <DialogContent className="max-w-5xl w-[90vw] h-[90vh] p-0 bg-black/90 border-none">
          <DialogTitle className="sr-only">Product Image Zoom</DialogTitle>
          <div className="relative w-full h-full flex items-center justify-center">
            <div className="absolute top-4 right-4 flex space-x-2 z-10">
              <Button
                variant="secondary"
                size="icon-sm"
                rounded="full"
                className="bg-white/20 text-white hover:bg-white/30"
                onClick={handleZoomIn}
                aria-label="Zoom in"
              >
                <ZoomIn className="h-5 w-5" />
              </Button>
              <Button
                variant="secondary"
                size="icon-sm"
                rounded="full"
                className="bg-white/20 text-white hover:bg-white/30"
                onClick={handleZoomOut}
                aria-label="Zoom out"
              >
                <ZoomOut className="h-5 w-5" />
              </Button>
              <Button
                variant="secondary"
                size="icon-sm"
                rounded="full"
                className="bg-white/20 text-white hover:bg-white/30"
                onClick={() => setIsZoomed(false)}
                aria-label="Close zoom view"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <div className="w-full h-full overflow-auto flex items-center justify-center p-8 cursor-grab">
              <img
                src={getCurrentImage()}
                alt={productName}
                className={cn(
                  "dialog-image",
                  `scale-${zoomLevel.toString().replace('.', '-')}`
                )}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default EnhancedProductGallery;
