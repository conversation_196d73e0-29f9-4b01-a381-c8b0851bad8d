
import React from 'react';
import { Check, X, Archive, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface BulkActionsProps {
  selectedCount: number;
  onBulkAction: (ids: string[], action: 'delete' | 'restore' | 'activate' | 'draft') => void;
  selectedIds: string[];
  onClearSelection: () => void;
}

const BulkActions: React.FC<BulkActionsProps> = ({
  selectedCount,
  onBulkAction,
  selectedIds,
  onClearSelection
}) => {
  if (selectedCount === 0) return null;
  
  return (
    <div className="bg-muted/50 p-2 rounded-md flex items-center gap-2 mb-4">
      <span className="text-sm font-medium">{selectedCount} selected</span>
      <Button 
        size="sm" 
        variant="outline"
        onClick={() => onBulkAction(selectedIds, 'activate')}
      >
        <Check className="mr-1 h-4 w-4" /> Activate
      </Button>
      <Button 
        size="sm" 
        variant="outline"
        onClick={() => onBulkAction(selectedIds, 'draft')}
      >
        <X className="mr-1 h-4 w-4" /> Draft
      </Button>
      <Button 
        size="sm" 
        variant="outline"
        onClick={() => onBulkAction(selectedIds, 'delete')}
      >
        <Archive className="mr-1 h-4 w-4" /> Archive
      </Button>
      <Button 
        size="sm" 
        variant="outline"
        onClick={() => onBulkAction(selectedIds, 'restore')}
      >
        <RotateCcw className="mr-1 h-4 w-4" /> Restore
      </Button>
      <Button 
        size="sm" 
        variant="outline"
        onClick={onClearSelection}
      >
        Clear Selection
      </Button>
    </div>
  );
};

export default BulkActions;
