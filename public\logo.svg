<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- White background -->
  <rect width="400" height="400" fill="white"/>

  <!-- The Badhees Logo - Matching original design from Supabase -->
  <g transform="translate(200, 200)">
    <!-- Main circular background -->
    <circle cx="0" cy="0" r="180" fill="#8B4513" stroke="#654321" stroke-width="3"/>

    <!-- Inner white circle -->
    <circle cx="0" cy="0" r="160" fill="white"/>

    <!-- Decorative inner border -->
    <circle cx="0" cy="0" r="140" fill="none" stroke="#8B4513" stroke-width="2"/>

    <!-- Central "B" letter design - larger and more prominent -->
    <g transform="translate(0, 0)">
      <!-- Letter B - refined design -->
      <path d="M-50 -70 L-50 70 L25 70 C45 70 60 55 60 35 C60 20 52 8 40 2 C52 -4 60 -16 60 -31 C60 -51 45 -66 25 -66 L-50 -66 Z M-25 -41 L20 -41 C30 -41 35 -36 35 -31 C35 -26 30 -21 20 -21 L-25 -21 Z M-25 19 L25 19 C35 19 40 24 40 29 C40 34 35 39 25 39 L-25 39 Z" fill="#8B4513"/>

      <!-- Decorative elements around B -->
      <circle cx="-90" cy="-50" r="6" fill="#D2691E"/>
      <circle cx="90" cy="-50" r="6" fill="#D2691E"/>
      <circle cx="-90" cy="50" r="6" fill="#D2691E"/>
      <circle cx="90" cy="50" r="6" fill="#D2691E"/>

      <!-- Additional decorative lines -->
      <line x1="-70" y1="-70" x2="-70" y2="70" stroke="#D2691E" stroke-width="2"/>
      <line x1="70" y1="-70" x2="70" y2="70" stroke="#D2691E" stroke-width="2"/>
    </g>
  </g>

  <!-- "THE BADHEES" text below -->
  <text x="200" y="360" text-anchor="middle" font-family="serif" font-size="24" font-weight="bold" fill="#8B4513">THE BADHEES</text>
</svg>
