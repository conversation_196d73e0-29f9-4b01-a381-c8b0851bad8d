<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="400" height="400" viewBox="0 0 400 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- White background -->
  <rect width="400" height="400" fill="white"/>

  <!-- The Badhees Logo - Updated to match original design -->
  <g transform="translate(50, 50)">
    <!-- Main logo container with rounded corners -->
    <rect x="0" y="0" width="300" height="300" rx="20" ry="20" fill="#8B4513" stroke="#654321" stroke-width="2"/>

    <!-- Inner white background -->
    <rect x="20" y="20" width="260" height="260" rx="15" ry="15" fill="white"/>

    <!-- Decorative border -->
    <rect x="30" y="30" width="240" height="240" rx="10" ry="10" fill="none" stroke="#8B4513" stroke-width="1"/>

    <!-- Central "B" letter design -->
    <g transform="translate(150, 150)">
      <!-- Letter B -->
      <path d="M-60 -80 L-60 80 L20 80 C40 80 55 65 55 45 C55 30 47 18 35 12 C47 6 55 -6 55 -21 C55 -41 40 -56 20 -56 L-60 -56 Z M-35 -31 L15 -31 C25 -31 30 -26 30 -21 C30 -16 25 -11 15 -11 L-35 -11 Z M-35 14 L20 14 C30 14 35 19 35 24 C35 29 30 34 20 34 L-35 34 Z" fill="#8B4513"/>

      <!-- Decorative elements around B -->
      <circle cx="-80" cy="-60" r="8" fill="#D2691E"/>
      <circle cx="80" cy="-60" r="8" fill="#D2691E"/>
      <circle cx="-80" cy="60" r="8" fill="#D2691E"/>
      <circle cx="80" cy="60" r="8" fill="#D2691E"/>
    </g>
  </g>

  <!-- "THE BADHEES" text below -->
  <text x="200" y="350" text-anchor="middle" font-family="serif" font-size="28" font-weight="bold" fill="#8B4513">THE BADHEES</text>
</svg>
