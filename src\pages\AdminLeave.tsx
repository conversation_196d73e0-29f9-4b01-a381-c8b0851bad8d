import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ArrowLeft, Check, Calendar, Loader2, Save, X } from 'lucide-react';
import { format, parseISO, differenceInDays, addDays } from 'date-fns';
import { useAuth } from '@/context/SupabaseAuthContext';
import { 
  useEmployees, 
  useEmployeeLeaveByEmployee,
  useEmployeeLeaveByStatus,
  useEmployeeLeaveBalance,
  useRequestEmployeeLeave,
  useUpdateEmployeeLeaveStatus,
  useUpdateEmployeeLeaveBalance
} from '@/hooks/useEmployeeManagement';
import { EmployeeLeave } from '@/services/employee/types';

const AdminLeave = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated, isAdmin, user } = useAuth();
  
  // Get employee ID from URL if provided
  const employeeIdFromUrl = searchParams.get('employee');
  
  // State for date selection
  const today = new Date();
  const currentYear = today.getFullYear();
  
  const [activeTab, setActiveTab] = useState('request');
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(employeeIdFromUrl || '');
  
  // State for leave request
  const [startDate, setStartDate] = useState(format(today, 'yyyy-MM-dd'));
  const [endDate, setEndDate] = useState(format(today, 'yyyy-MM-dd'));
  const [leaveType, setLeaveType] = useState<'annual' | 'sick' | 'personal' | 'unpaid' | 'other'>('annual');
  const [leaveReason, setLeaveReason] = useState('');
  
  // State for approval dialog
  const [approvalDialogOpen, setApprovalDialogOpen] = useState(false);
  const [leaveToApprove, setLeaveToApprove] = useState<string | null>(null);
  const [approvalAction, setApprovalAction] = useState<'approved' | 'rejected'>('approved');
  const [approvalNotes, setApprovalNotes] = useState('');
  
  // State for balance dialog
  const [balanceDialogOpen, setBalanceDialogOpen] = useState(false);
  const [annualLeaveBalance, setAnnualLeaveBalance] = useState('');
  const [sickLeaveBalance, setSickLeaveBalance] = useState('');
  const [personalLeaveBalance, setPersonalLeaveBalance] = useState('');

  // Fetch data
  const { data: employees, isLoading: isLoadingEmployees } = useEmployees();
  const { data: employeeLeave, isLoading: isLoadingEmployeeLeave } = 
    useEmployeeLeaveByEmployee(selectedEmployeeId);
  const { data: pendingLeave, isLoading: isLoadingPendingLeave } = 
    useEmployeeLeaveByStatus('pending');
  const { data: leaveBalance, isLoading: isLoadingLeaveBalance } = 
    useEmployeeLeaveBalance(selectedEmployeeId, currentYear);
  
  const requestLeaveMutation = useRequestEmployeeLeave();
  const updateLeaveStatusMutation = useUpdateEmployeeLeaveStatus();
  const updateLeaveBalanceMutation = useUpdateEmployeeLeaveBalance();

  // Check authentication and admin status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/employees/leave');
    } else if (!isAdmin()) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Set balance values when leave balance data is loaded
  useEffect(() => {
    if (leaveBalance) {
      setAnnualLeaveBalance(leaveBalance.annual_leave_balance.toString());
      setSickLeaveBalance(leaveBalance.sick_leave_balance.toString());
      setPersonalLeaveBalance(leaveBalance.personal_leave_balance.toString());
    }
  }, [leaveBalance]);

  // Calculate leave duration
  const calculateLeaveDuration = () => {
    if (!startDate || !endDate) return 0;
    
    const start = parseISO(startDate);
    const end = parseISO(endDate);
    
    return differenceInDays(end, start) + 1;
  };

  // Handle leave request submission
  const handleRequestLeave = async () => {
    if (!selectedEmployeeId || !startDate || !endDate) return;
    
    try {
      const leave: Omit<EmployeeLeave, 'id' | 'created_at' | 'updated_at'> = {
        employee_id: selectedEmployeeId,
        start_date: startDate,
        end_date: endDate,
        leave_type: leaveType,
        status: 'pending',
        reason: leaveReason,
        created_by: user?.id
      };
      
      await requestLeaveMutation.mutateAsync(leave);
      
      // Reset form
      setLeaveReason('');
    } catch (error) {
      console.error('Error requesting leave:', error);
    }
  };

  // Handle approval dialog open
  const handleApprovalClick = (id: string, action: 'approved' | 'rejected') => {
    setLeaveToApprove(id);
    setApprovalAction(action);
    setApprovalNotes('');
    setApprovalDialogOpen(true);
  };

  // Handle approval confirmation
  const confirmApproval = async () => {
    if (leaveToApprove && user?.id) {
      try {
        await updateLeaveStatusMutation.mutateAsync({
          id: leaveToApprove,
          status: approvalAction,
          approvedBy: user.id,
          notes: approvalNotes
        });
        setApprovalDialogOpen(false);
        setLeaveToApprove(null);
      } catch (error) {
        console.error('Error updating leave status:', error);
      }
    }
  };

  // Handle balance update
  const handleUpdateBalance = async () => {
    if (!leaveBalance?.id) return;
    
    try {
      await updateLeaveBalanceMutation.mutateAsync({
        id: leaveBalance.id,
        balance: {
          annual_leave_balance: parseFloat(annualLeaveBalance) || 0,
          sick_leave_balance: parseFloat(sickLeaveBalance) || 0,
          personal_leave_balance: parseFloat(personalLeaveBalance) || 0
        }
      });
      setBalanceDialogOpen(false);
    } catch (error) {
      console.error('Error updating leave balance:', error);
    }
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get leave type badge color
  const getLeaveTypeBadgeColor = (type: string) => {
    switch (type) {
      case 'annual':
        return 'bg-blue-100 text-blue-800';
      case 'sick':
        return 'bg-purple-100 text-purple-800';
      case 'personal':
        return 'bg-indigo-100 text-indigo-800';
      case 'unpaid':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-6">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin/employees')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Employees
              </Button>
              <h1 className="text-2xl font-bold">Leave Management</h1>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="request">Request Leave</TabsTrigger>
                <TabsTrigger value="employee">Employee Leave History</TabsTrigger>
                <TabsTrigger value="approval">Approval Queue</TabsTrigger>
                <TabsTrigger value="balance">Leave Balance</TabsTrigger>
              </TabsList>

              {/* Request Leave Tab */}
              <TabsContent value="request">
                <Card>
                  <CardHeader>
                    <CardTitle>Request Leave</CardTitle>
                    <CardDescription>
                      Submit a leave request for an employee.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <Label htmlFor="employee-select">Select Employee</Label>
                        <Select
                          value={selectedEmployeeId}
                          onValueChange={setSelectedEmployeeId}
                        >
                          <SelectTrigger id="employee-select">
                            <SelectValue placeholder="Select an employee" />
                          </SelectTrigger>
                          <SelectContent>
                            {employees?.map(employee => (
                              <SelectItem key={employee.id} value={employee.id}>
                                {employee.first_name} {employee.last_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="leave-type">Leave Type</Label>
                        <Select
                          value={leaveType}
                          onValueChange={(value) => setLeaveType(value as any)}
                        >
                          <SelectTrigger id="leave-type">
                            <SelectValue placeholder="Select leave type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="annual">Annual Leave</SelectItem>
                            <SelectItem value="sick">Sick Leave</SelectItem>
                            <SelectItem value="personal">Personal Leave</SelectItem>
                            <SelectItem value="unpaid">Unpaid Leave</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="start-date">Start Date</Label>
                        <Input
                          id="start-date"
                          type="date"
                          value={startDate}
                          onChange={(e) => setStartDate(e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="end-date">End Date</Label>
                        <Input
                          id="end-date"
                          type="date"
                          value={endDate}
                          onChange={(e) => setEndDate(e.target.value)}
                        />
                      </div>
                      <div className="md:col-span-2">
                        <div className="flex justify-between items-center mb-2">
                          <Label htmlFor="leave-reason">Reason for Leave</Label>
                          <span className="text-sm text-gray-500">
                            Duration: {calculateLeaveDuration()} day(s)
                          </span>
                        </div>
                        <Textarea
                          id="leave-reason"
                          value={leaveReason}
                          onChange={(e) => setLeaveReason(e.target.value)}
                          placeholder="Enter reason for leave"
                          rows={3}
                        />
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button 
                      onClick={handleRequestLeave}
                      disabled={
                        requestLeaveMutation.isPending || 
                        !selectedEmployeeId || 
                        !startDate ||
                        !endDate
                      }
                    >
                      {requestLeaveMutation.isPending && (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      )}
                      <Save className="h-4 w-4 mr-2" />
                      Submit Request
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              {/* Employee Leave History Tab */}
              <TabsContent value="employee">
                <Card>
                  <CardHeader>
                    <CardTitle>Employee Leave History</CardTitle>
                    <CardDescription>
                      View leave records for a specific employee.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="employee-history-select">Select Employee</Label>
                      <Select
                        value={selectedEmployeeId}
                        onValueChange={setSelectedEmployeeId}
                      >
                        <SelectTrigger id="employee-history-select">
                          <SelectValue placeholder="Select an employee" />
                        </SelectTrigger>
                        <SelectContent>
                          {employees?.map(employee => (
                            <SelectItem key={employee.id} value={employee.id}>
                              {employee.first_name} {employee.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {!selectedEmployeeId ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Please select an employee to view leave records.</p>
                      </div>
                    ) : isLoadingEmployeeLeave ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading leave data...</span>
                      </div>
                    ) : employeeLeave?.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No leave records found for this employee.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Type</TableHead>
                              <TableHead>Start Date</TableHead>
                              <TableHead>End Date</TableHead>
                              <TableHead>Duration</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Reason</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {employeeLeave?.map((record) => (
                              <TableRow key={record.id}>
                                <TableCell>
                                  <Badge 
                                    variant="outline"
                                    className={getLeaveTypeBadgeColor(record.leave_type)}
                                  >
                                    {record.leave_type.charAt(0).toUpperCase() + record.leave_type.slice(1)}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {format(parseISO(record.start_date), 'MMM d, yyyy')}
                                </TableCell>
                                <TableCell>
                                  {format(parseISO(record.end_date), 'MMM d, yyyy')}
                                </TableCell>
                                <TableCell>
                                  {differenceInDays(parseISO(record.end_date), parseISO(record.start_date)) + 1} day(s)
                                </TableCell>
                                <TableCell>
                                  <Badge 
                                    variant="outline"
                                    className={getStatusBadgeColor(record.status)}
                                  >
                                    {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                                  </Badge>
                                </TableCell>
                                <TableCell>{record.reason || '-'}</TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Approval Queue Tab */}
              <TabsContent value="approval">
                <Card>
                  <CardHeader>
                    <CardTitle>Leave Approval Queue</CardTitle>
                    <CardDescription>
                      Approve or reject pending leave requests.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isLoadingPendingLeave ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading pending requests...</span>
                      </div>
                    ) : !pendingLeave || pendingLeave.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No pending leave requests found.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Employee</TableHead>
                              <TableHead>Type</TableHead>
                              <TableHead>Start Date</TableHead>
                              <TableHead>End Date</TableHead>
                              <TableHead>Duration</TableHead>
                              <TableHead>Reason</TableHead>
                              <TableHead className="text-right">Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {pendingLeave.map((record) => (
                              <TableRow key={record.id}>
                                <TableCell className="font-medium">
                                  {record.employee_name}
                                </TableCell>
                                <TableCell>
                                  <Badge 
                                    variant="outline"
                                    className={getLeaveTypeBadgeColor(record.leave_type)}
                                  >
                                    {record.leave_type.charAt(0).toUpperCase() + record.leave_type.slice(1)}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {format(parseISO(record.start_date), 'MMM d, yyyy')}
                                </TableCell>
                                <TableCell>
                                  {format(parseISO(record.end_date), 'MMM d, yyyy')}
                                </TableCell>
                                <TableCell>
                                  {differenceInDays(parseISO(record.end_date), parseISO(record.start_date)) + 1} day(s)
                                </TableCell>
                                <TableCell>{record.reason || '-'}</TableCell>
                                <TableCell className="text-right">
                                  <div className="flex justify-end gap-2">
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700"
                                      onClick={() => handleApprovalClick(record.id, 'approved')}
                                    >
                                      <Check className="h-4 w-4 mr-1" />
                                      Approve
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700"
                                      onClick={() => handleApprovalClick(record.id, 'rejected')}
                                    >
                                      <X className="h-4 w-4 mr-1" />
                                      Reject
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Leave Balance Tab */}
              <TabsContent value="balance">
                <Card>
                  <CardHeader>
                    <CardTitle>Employee Leave Balance</CardTitle>
                    <CardDescription>
                      View and update leave balances for employees.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="balance-employee-select">Select Employee</Label>
                      <Select
                        value={selectedEmployeeId}
                        onValueChange={setSelectedEmployeeId}
                      >
                        <SelectTrigger id="balance-employee-select">
                          <SelectValue placeholder="Select an employee" />
                        </SelectTrigger>
                        <SelectContent>
                          {employees?.map(employee => (
                            <SelectItem key={employee.id} value={employee.id}>
                              {employee.first_name} {employee.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {!selectedEmployeeId ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Please select an employee to view leave balance.</p>
                      </div>
                    ) : isLoadingLeaveBalance ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading balance data...</span>
                      </div>
                    ) : (
                      <div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm font-medium text-gray-500">Annual Leave</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-3xl font-bold">{leaveBalance?.annual_leave_balance || 0} days</div>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm font-medium text-gray-500">Sick Leave</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-3xl font-bold">{leaveBalance?.sick_leave_balance || 0} days</div>
                            </CardContent>
                          </Card>
                          <Card>
                            <CardHeader className="pb-2">
                              <CardTitle className="text-sm font-medium text-gray-500">Personal Leave</CardTitle>
                            </CardHeader>
                            <CardContent>
                              <div className="text-3xl font-bold">{leaveBalance?.personal_leave_balance || 0} days</div>
                            </CardContent>
                          </Card>
                        </div>
                        <Button 
                          onClick={() => setBalanceDialogOpen(true)}
                          className="w-full"
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          Update Leave Balance
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      <Footer />

      {/* Approval Dialog */}
      <Dialog open={approvalDialogOpen} onOpenChange={setApprovalDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {approvalAction === 'approved' ? 'Approve Leave Request' : 'Reject Leave Request'}
            </DialogTitle>
            <DialogDescription>
              {approvalAction === 'approved' 
                ? 'Are you sure you want to approve this leave request?' 
                : 'Are you sure you want to reject this leave request?'}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Label htmlFor="approval-notes">Notes (Optional)</Label>
            <Textarea
              id="approval-notes"
              value={approvalNotes}
              onChange={(e) => setApprovalNotes(e.target.value)}
              placeholder="Add any notes about this decision"
              rows={3}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setApprovalDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant={approvalAction === 'approved' ? 'default' : 'destructive'} 
              onClick={confirmApproval}
              disabled={updateLeaveStatusMutation.isPending}
            >
              {updateLeaveStatusMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              {approvalAction === 'approved' ? 'Approve' : 'Reject'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Balance Update Dialog */}
      <Dialog open={balanceDialogOpen} onOpenChange={setBalanceDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Leave Balance</DialogTitle>
            <DialogDescription>
              Update the leave balance for this employee.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div>
              <Label htmlFor="annual-leave-balance">Annual Leave Balance (days)</Label>
              <Input
                id="annual-leave-balance"
                type="number"
                min="0"
                step="0.5"
                value={annualLeaveBalance}
                onChange={(e) => setAnnualLeaveBalance(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="sick-leave-balance">Sick Leave Balance (days)</Label>
              <Input
                id="sick-leave-balance"
                type="number"
                min="0"
                step="0.5"
                value={sickLeaveBalance}
                onChange={(e) => setSickLeaveBalance(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="personal-leave-balance">Personal Leave Balance (days)</Label>
              <Input
                id="personal-leave-balance"
                type="number"
                min="0"
                step="0.5"
                value={personalLeaveBalance}
                onChange={(e) => setPersonalLeaveBalance(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setBalanceDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleUpdateBalance}
              disabled={updateLeaveBalanceMutation.isPending}
            >
              {updateLeaveBalanceMutation.isPending && (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              )}
              Update Balance
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminLeave;
