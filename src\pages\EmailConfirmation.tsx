import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, XCircle, Loader2, Home, LogIn, Mail } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { toast } from '@/hooks/use-toast';

const EmailConfirmation = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('');
  const [userEmail, setUserEmail] = useState<string>('');
  const [countdown, setCountdown] = useState<number>(3);

  useEffect(() => {
    const confirmEmail = async () => {
      try {
        // Get the token from URL parameters
        const token = searchParams.get('token');
        const type = searchParams.get('type');

        if (!token) {
          setStatus('error');
          setMessage('Invalid confirmation link. Please check your email and try again.');
          return;
        }

        // Verify the email with Supabase
        const { data, error } = await supabase.auth.verifyOtp({
          token_hash: token,
          type: type === 'signup' ? 'signup' : 'email'
        });

        if (error) {
          console.error('Email confirmation error:', error);
          setStatus('error');

          if (error.message.includes('expired')) {
            setMessage('The confirmation link has expired. Please request a new confirmation email from the login page.');
          } else if (error.message.includes('already been used')) {
            setMessage('This confirmation link has already been used. Your email is already confirmed! You can now sign in to your account.');
            // If already confirmed, treat as success after a delay
            setTimeout(() => {
              setStatus('success');
              setMessage('🎉 Your email is already confirmed! Welcome to The Badhees!');
            }, 2000);
          } else {
            setMessage('Failed to confirm your email. The link may have expired or already been used.');
          }
          return;
        }

        if (data.user) {
          setStatus('success');
          setUserEmail(data.user.email || '');
          setMessage('🎉 Your email has been successfully confirmed! Welcome to The Badhees!');

          // Show success toast
          toast({
            title: "Email Confirmed Successfully!",
            description: "Welcome to The Badhees! You can now access all features.",
            duration: 5000,
          });

          // Start countdown for auto-redirect
          let countdownTimer = 3;
          const countdownInterval = setInterval(() => {
            setCountdown(countdownTimer);
            countdownTimer--;

            if (countdownTimer < 0) {
              clearInterval(countdownInterval);
              navigate('/login', {
                state: {
                  message: 'Email confirmed successfully! Please sign in to continue.',
                  email: data.user.email
                }
              });
            }
          }, 1000);
        } else {
          setStatus('error');
          setMessage('Email confirmation failed. Please try again or contact support.');
        }
      } catch (error) {
        console.error('Unexpected error during email confirmation:', error);
        setStatus('error');
        setMessage('An unexpected error occurred. Please try again later.');
      }
    };

    confirmEmail();
  }, [searchParams]);

  const handleContinue = () => {
    if (status === 'success') {
      navigate('/login', {
        state: {
          message: 'Email confirmed successfully! Please sign in to continue.'
        }
      });
    } else {
      navigate('/register');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />

      <div className="pt-28 pb-16">
        <div className="max-w-md mx-auto px-4 sm:px-8">
          <div className="bg-white rounded-xl overflow-hidden shadow-sm border border-badhees-100 p-8 text-center">

            {/* Header */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-badhees-800 mb-2">
                Email Confirmation
              </h1>
              <p className="text-sm text-badhees-600">
                The Badhees - Premium Furniture Store
              </p>
            </div>

            {/* Status Icon and Message */}
            <div className="mb-8">
              {status === 'loading' && (
                <div className="flex flex-col items-center">
                  <Loader2 className="h-16 w-16 text-badhees-600 animate-spin mb-4" />
                  <p className="text-badhees-700 font-medium">
                    Confirming your email...
                  </p>
                  <p className="text-sm text-badhees-500 mt-2">
                    Please wait while we verify your email address.
                  </p>
                </div>
              )}

              {status === 'success' && (
                <div className="flex flex-col items-center">
                  <div className="relative mb-4">
                    <CheckCircle className="h-16 w-16 text-green-500 animate-pulse" />
                    <div className="absolute -top-2 -right-2 text-2xl">🎉</div>
                  </div>
                  <p className="text-green-700 font-bold text-lg mb-2">
                    Email Confirmed Successfully!
                  </p>
                  <p className="text-sm text-badhees-600 mb-4">
                    {message}
                  </p>
                  {userEmail && (
                    <p className="text-xs text-badhees-500 mb-4">
                      Confirmed for: <span className="font-medium">{userEmail}</span>
                    </p>
                  )}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 w-full">
                    <p className="text-sm text-green-700 text-center">
                      Redirecting to login in <span className="font-bold text-green-800">{countdown}</span> seconds...
                    </p>
                    <div className="mt-2 w-full bg-green-200 rounded-full h-2">
                      <div
                        className={`bg-green-500 h-2 rounded-full transition-all duration-1000 ${
                          countdown === 3 ? 'w-0' :
                          countdown === 2 ? 'w-1/3' :
                          countdown === 1 ? 'w-2/3' :
                          'w-full'
                        }`}
                      ></div>
                    </div>
                  </div>
                </div>
              )}

              {status === 'error' && (
                <div className="flex flex-col items-center">
                  <XCircle className="h-16 w-16 text-red-500 mb-4" />
                  <p className="text-red-700 font-medium mb-2">
                    Confirmation Failed
                  </p>
                  <p className="text-sm text-badhees-600">
                    {message}
                  </p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {status !== 'loading' && (
              <div className="space-y-4">
                {status === 'success' ? (
                  <div className="space-y-3">
                    <button
                      type="button"
                      onClick={() => navigate('/login', { state: { email: userEmail } })}
                      className="w-full bg-badhees-800 text-white py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors flex items-center justify-center gap-2"
                    >
                      <LogIn className="h-4 w-4" />
                      Continue to Sign In
                    </button>
                    <button
                      type="button"
                      onClick={() => navigate('/')}
                      className="w-full bg-gray-100 text-badhees-800 py-3 rounded-md font-medium hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
                    >
                      <Home className="h-4 w-4" />
                      Go to Homepage
                    </button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <button
                      type="button"
                      onClick={handleContinue}
                      className="w-full bg-badhees-800 text-white py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors"
                    >
                      Try Again
                    </button>
                    <button
                      type="button"
                      onClick={() => navigate('/register')}
                      className="w-full bg-gray-100 text-badhees-800 py-3 rounded-md font-medium hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
                    >
                      <Mail className="h-4 w-4" />
                      Request New Email
                    </button>
                  </div>
                )}

                {status === 'error' && (
                  <p className="text-xs text-badhees-500">
                    If you continue to experience issues, please contact our support team at{' '}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-badhees-600 hover:text-badhees-700 underline"
                    >
                      <EMAIL>
                    </a>
                  </p>
                )}
              </div>
            )}

            {/* Company Info */}
            <div className="mt-8 pt-6 border-t border-badhees-100">
              <p className="text-xs text-badhees-500">
                This email confirmation is from The Badhees, your trusted partner for premium furniture.
                <br />
                <a
                  href="https://thebadhees.com"
                  className="text-badhees-600 hover:text-badhees-700 underline"
                >
                  Visit our website
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default EmailConfirmation;
