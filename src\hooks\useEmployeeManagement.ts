import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getEmployees,
  getEmployee,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  getAttendanceByDate,
  getAttendanceByEmployeeAndMonth,
  getMonthlyAttendanceSummary,
  markAttendance,
  getOvertimeByEmployee,
  getOvertimeByMonth,
  getOvertimeSummary,
  recordOvertime,
  updateOvertimeStatus,
  calculatePayroll,
  createPayroll,
  getPayrollByEmployee,
  getPayrollByPeriod,
  updatePayrollStatus,
  getEmployeeLeaveByEmployee,
  getEmployeeLeaveByStatus,
  getEmployeeLeaveByDateRange,
  requestEmployeeLeave,
  updateEmployeeLeaveStatus,
  getEmployeeLeaveBalance,
  updateEmployeeLeaveBalance
} from '@/services/employee/employeeService';
import {
  Employee,
  Attendance,
  Overtime,
  Payroll,
  PayrollCalculationInput,
  EmployeeLeave,
  EmployeeLeaveBalance
} from '@/services/employee/types';
import { toast } from 'sonner';

// Employee hooks
export const useEmployees = () => {
  return useQuery({
    queryKey: ['employees'],
    queryFn: getEmployees,
  });
};

export const useEmployee = (id: string) => {
  return useQuery({
    queryKey: ['employee', id],
    queryFn: () => getEmployee(id),
    enabled: !!id,
  });
};

export const useCreateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (employee: Omit<Employee, 'id' | 'created_at' | 'updated_at'>) =>
      createEmployee(employee),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast.success('Employee created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create employee: ${error.message}`);
    },
  });
};

export const useUpdateEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, employee }: { id: string, employee: Partial<Omit<Employee, 'id' | 'created_at' | 'updated_at'>> }) =>
      updateEmployee(id, employee),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      queryClient.invalidateQueries({ queryKey: ['employee', variables.id] });
      toast.success('Employee updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update employee: ${error.message}`);
    },
  });
};

export const useDeleteEmployee = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteEmployee(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employees'] });
      toast.success('Employee deleted successfully');
    },
    onError: (error) => {
      toast.error(`Failed to delete employee: ${error.message}`);
    },
  });
};

// Attendance hooks
export const useAttendanceByDate = (date: string) => {
  return useQuery({
    queryKey: ['attendance', 'date', date],
    queryFn: () => getAttendanceByDate(date),
    enabled: !!date,
  });
};

export const useAttendanceByEmployeeAndMonth = (employeeId: string, year: number, month: number) => {
  return useQuery({
    queryKey: ['attendance', 'employee', employeeId, year, month],
    queryFn: () => getAttendanceByEmployeeAndMonth(employeeId, year, month),
    enabled: !!employeeId && !!year && !!month,
  });
};

export const useMonthlyAttendanceSummary = (year: number, month: number) => {
  return useQuery({
    queryKey: ['attendance', 'summary', year, month],
    queryFn: () => getMonthlyAttendanceSummary(year, month),
    enabled: !!year && !!month,
  });
};

export const useMarkAttendance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (records: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>[]) =>
      markAttendance(records),
    onSuccess: (_, variables) => {
      // Extract the date from the first record to invalidate the specific date query
      if (variables.length > 0) {
        const date = variables[0].date;
        queryClient.invalidateQueries({ queryKey: ['attendance', 'date', date] });
      }

      // Invalidate all attendance queries to be safe
      queryClient.invalidateQueries({ queryKey: ['attendance'] });
      toast.success('Attendance marked successfully');
    },
    onError: (error) => {
      toast.error(`Failed to mark attendance: ${error.message}`);
    },
  });
};

// Overtime hooks
export const useOvertimeByEmployee = (employeeId: string) => {
  return useQuery({
    queryKey: ['overtime', 'employee', employeeId],
    queryFn: () => getOvertimeByEmployee(employeeId),
    enabled: !!employeeId,
  });
};

export const useOvertimeByMonth = (year: number, month: number) => {
  return useQuery({
    queryKey: ['overtime', 'month', year, month],
    queryFn: () => getOvertimeByMonth(year, month),
    enabled: !!year && !!month,
  });
};

export const useOvertimeSummary = (year: number, month: number) => {
  return useQuery({
    queryKey: ['overtime', 'summary', year, month],
    queryFn: () => getOvertimeSummary(year, month),
    enabled: !!year && !!month,
  });
};

export const useRecordOvertime = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (overtime: Omit<Overtime, 'id' | 'created_at' | 'updated_at'>) =>
      recordOvertime(overtime),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['overtime', 'employee', variables.employee_id] });
      queryClient.invalidateQueries({ queryKey: ['overtime'] });
      toast.success('Overtime recorded successfully');
    },
    onError: (error) => {
      toast.error(`Failed to record overtime: ${error.message}`);
    },
  });
};

export const useUpdateOvertimeStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status, approvedBy }: { id: string, status: 'approved' | 'rejected', approvedBy: string }) =>
      updateOvertimeStatus(id, status, approvedBy),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['overtime'] });
      toast.success(`Overtime ${status === 'approved' ? 'approved' : 'rejected'} successfully`);
    },
    onError: (error) => {
      toast.error(`Failed to update overtime status: ${error.message}`);
    },
  });
};

// Payroll hooks
export const useCalculatePayroll = () => {
  return useMutation({
    mutationFn: (input: PayrollCalculationInput) => calculatePayroll(input),
    onError: (error) => {
      toast.error(`Failed to calculate payroll: ${error.message}`);
    },
  });
};

export const useCreatePayroll = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payroll: Omit<Payroll, 'id' | 'created_at' | 'updated_at'>) =>
      createPayroll(payroll),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['payroll', 'employee', variables.employee_id] });
      queryClient.invalidateQueries({ queryKey: ['payroll'] });
      toast.success('Payroll created successfully');
    },
    onError: (error) => {
      toast.error(`Failed to create payroll: ${error.message}`);
    },
  });
};

export const usePayrollByEmployee = (employeeId: string) => {
  return useQuery({
    queryKey: ['payroll', 'employee', employeeId],
    queryFn: () => getPayrollByEmployee(employeeId),
    enabled: !!employeeId,
  });
};

export const usePayrollByPeriod = (startDate: string, endDate: string) => {
  return useQuery({
    queryKey: ['payroll', 'period', startDate, endDate],
    queryFn: () => getPayrollByPeriod(startDate, endDate),
    enabled: !!startDate && !!endDate,
  });
};

export const useUpdatePayrollStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status, paymentDate }: { id: string, status: 'pending' | 'paid' | 'cancelled', paymentDate?: string }) =>
      updatePayrollStatus(id, status, paymentDate),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payroll'] });
      toast.success('Payroll status updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update payroll status: ${error.message}`);
    },
  });
};

// Employee Leave hooks
export const useEmployeeLeaveByEmployee = (employeeId: string) => {
  return useQuery({
    queryKey: ['employee-leave', 'employee', employeeId],
    queryFn: () => getEmployeeLeaveByEmployee(employeeId),
    enabled: !!employeeId,
  });
};

export const useEmployeeLeaveByStatus = (status: 'pending' | 'approved' | 'rejected') => {
  return useQuery({
    queryKey: ['employee-leave', 'status', status],
    queryFn: () => getEmployeeLeaveByStatus(status),
  });
};

export const useEmployeeLeaveByDateRange = (startDate: string, endDate: string) => {
  return useQuery({
    queryKey: ['employee-leave', 'date-range', startDate, endDate],
    queryFn: () => getEmployeeLeaveByDateRange(startDate, endDate),
    enabled: !!startDate && !!endDate,
  });
};

export const useRequestEmployeeLeave = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (leave: Omit<EmployeeLeave, 'id' | 'created_at' | 'updated_at'>) =>
      requestEmployeeLeave(leave),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['employee-leave'] });
      queryClient.invalidateQueries({ queryKey: ['employee-leave', 'employee', variables.employee_id] });
      toast.success('Leave request submitted successfully');
    },
    onError: (error) => {
      toast.error(`Failed to submit leave request: ${error.message}`);
    },
  });
};

export const useUpdateEmployeeLeaveStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status, approvedBy, notes }: { id: string, status: 'approved' | 'rejected', approvedBy: string, notes?: string }) =>
      updateEmployeeLeaveStatus(id, status, approvedBy, notes),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['employee-leave'] });
      toast.success('Leave request status updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update leave request status: ${error.message}`);
    },
  });
};

export const useEmployeeLeaveBalance = (employeeId: string, year: number) => {
  return useQuery({
    queryKey: ['employee-leave-balance', employeeId, year],
    queryFn: () => getEmployeeLeaveBalance(employeeId, year),
    enabled: !!employeeId && !!year,
  });
};

export const useUpdateEmployeeLeaveBalance = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, balance }: { id: string, balance: Partial<Pick<EmployeeLeaveBalance, 'annual_leave_balance' | 'sick_leave_balance' | 'personal_leave_balance'>> }) =>
      updateEmployeeLeaveBalance(id, balance),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['employee-leave-balance'] });
      toast.success('Leave balance updated successfully');
    },
    onError: (error) => {
      toast.error(`Failed to update leave balance: ${error.message}`);
    },
  });
};
