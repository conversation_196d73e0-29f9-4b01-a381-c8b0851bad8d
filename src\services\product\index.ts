/**
 * Product Service Index
 *
 * This module exports all product-related functionality.
 */

// Export types
export * from './types';

// Export mappers
export * from './mappers';

// Export services
export * from './productService';
export * from './productManagementService';

// Re-export for backward compatibility
import {
  getProducts,
  getProductById,
  getCategories,
  updateProductStock
} from './productService';

import {
  createProduct,
  updateProduct,
  deleteProduct
} from './productManagementService';

import { mapSupabaseProductToFrontend } from './mappers';
import type { Category, FrontendProduct, ProductImage, ProductInput, SupabaseProduct } from './types';

// Export everything for backward compatibility
export {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  updateProductStock,
  getCategories,
  mapSupabaseProductToFrontend
};

// Export types
export type {
  Category,
  FrontendProduct,
  ProductImage,
  ProductInput,
  SupabaseProduct
};
