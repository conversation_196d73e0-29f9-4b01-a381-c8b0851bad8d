
import React from "react";
import { Link } from "react-router-dom";
import {
  Mail,
  Phone,
  MapPin,
  ArrowRight,
  ChevronDown
} from "lucide-react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useIsMobile } from "@/hooks/use-mobile";
import { openWhatsApp } from "@/utils/whatsapp";

// Colorful Social Media Icons Components
const InstagramIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <defs>
      <radialGradient id="instagram-gradient" cx="0.5" cy="1" r="1">
        <stop offset="0%" stopColor="#fdf497" />
        <stop offset="5%" stopColor="#fdf497" />
        <stop offset="45%" stopColor="#fd5949" />
        <stop offset="60%" stopColor="#d6249f" />
        <stop offset="90%" stopColor="#285AEB" />
      </radialGradient>
    </defs>
    <rect x="2" y="2" width="20" height="20" rx="5" ry="5" fill="url(#instagram-gradient)" />
    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" fill="white" />
    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5" stroke="white" strokeWidth="2" strokeLinecap="round" />
  </svg>
);

const YouTubeIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"
      fill="#FF0000"
    />
    <polygon points="9.75,15.02 15.5,11.75 9.75,8.48" fill="white" />
  </svg>
);

const WhatsAppIcon = ({ className }: { className?: string }) => (
  <svg
    className={className}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"
      fill="#25D366"
    />
  </svg>
);

const Footer = () => {
  const isMobile = useIsMobile();

  const quickLinks = [
    { name: "Home", path: "/" },
    { name: "Products", path: "/products" },
    { name: "Custom Projects", path: "/custom-interiors" },
    { name: "About", path: "/about" },
  ];

  const customerServiceLinks = [
    { name: "FAQ", path: "/faq" },
    { name: "Shipping & Returns", path: "/shipping-returns" },
    { name: "Care Instructions", path: "/care-instructions" },
    { name: "Warranty", path: "/warranty" },
  ];

  // WhatsApp functionality
  const handleWhatsAppClick = () => {
    openWhatsApp();
  };

  const renderMobileFooter = () => (
    <div className="py-8">
      {/* Logo and Description */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-badhees-800 font-display mb-4">
          The Badhees
        </h2>
        <p className="text-sm text-badhees-600 mb-4">
          Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality.
        </p>
        <div className="flex items-center justify-start space-x-6">
          <a
            href="https://www.instagram.com/thebadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-10 h-10 hover:scale-110 transition-transform duration-200"
            aria-label="Instagram"
          >
            <InstagramIcon className="h-7 w-7" />
          </a>
          <a
            href="https://www.youtube.com/@TheBadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-10 h-10 hover:scale-110 transition-transform duration-200"
            aria-label="YouTube"
          >
            <YouTubeIcon className="h-7 w-7" />
          </a>
          <button
            type="button"
            onClick={handleWhatsAppClick}
            className="flex items-center justify-center w-10 h-10 hover:scale-110 transition-transform duration-200"
            aria-label="WhatsApp"
          >
            <WhatsAppIcon className="h-7 w-7" />
          </button>
        </div>
      </div>

      {/* Accordion Links */}
      <Accordion type="single" collapsible className="w-full mb-6">
        <AccordionItem value="quick-links" className="border-b border-badhees-100">
          <AccordionTrigger className="text-lg font-medium text-badhees-800 py-4 touch-target">
            Quick Links
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-4 pt-2 pl-1 pb-2">
              {quickLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.path}
                  className="text-base text-badhees-600 hover:text-badhees-accent flex items-center touch-target py-2"
                >
                  <ArrowRight className="h-4 w-4 mr-3" />
                  {link.name}
                </Link>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="customer-service" className="border-b border-badhees-100">
          <AccordionTrigger className="text-lg font-medium text-badhees-800 py-4 touch-target">
            Customer Service
          </AccordionTrigger>
          <AccordionContent>
            <div className="flex flex-col space-y-4 pt-2 pl-1 pb-2">
              {customerServiceLinks.map((link) => (
                <Link
                  key={link.name}
                  to={link.path}
                  className="text-base text-badhees-600 hover:text-badhees-accent flex items-center touch-target py-2"
                >
                  <ArrowRight className="h-4 w-4 mr-3" />
                  {link.name}
                </Link>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="contact" className="border-b border-badhees-100">
          <AccordionTrigger className="text-lg font-medium text-badhees-800 py-4 touch-target">
            Contact Us
          </AccordionTrigger>
          <AccordionContent>
            <ul className="space-y-4 pt-2 pb-2">
              <li className="flex items-start touch-target py-2">
                <MapPin className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-0.5" />
                <span className="text-base text-badhees-600">
                  The Badhees, Opposite Vasudha layout, Appajipura road, Koraluru, Bangalore 560067
                </span>
              </li>
              <li className="flex items-start touch-target py-2">
                <Phone className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-1" />
                <div className="flex flex-col">
                  <a href="tel:9108344363" className="text-base text-badhees-600">9108344363</a>
                  <a href="tel:8197705438" className="text-base text-badhees-600 mt-1">8197705438</a>
                </div>
              </li>
              <li className="flex items-center touch-target py-2">
                <Mail className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-base text-badhees-600"><EMAIL></a>
              </li>
            </ul>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );

  const renderDesktopFooter = () => (
    <div className="grid grid-cols-1 gap-12 md:grid-cols-2 lg:grid-cols-4">
      {/* Column 1: Logo and Description */}
      <div className="space-y-4">
        <h2 className="text-2xl font-bold text-badhees-800 font-display">
          The Badhees
        </h2>
        <p className="text-sm text-badhees-600 max-w-xs">
          Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality.
        </p>
        <div className="flex items-center justify-start space-x-5 pt-2">
          <a
            href="https://www.instagram.com/thebadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-9 h-9 hover:scale-110 transition-transform duration-200"
            aria-label="Instagram"
          >
            <InstagramIcon className="h-6 w-6" />
          </a>
          <a
            href="https://www.youtube.com/@TheBadhees"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center justify-center w-9 h-9 hover:scale-110 transition-transform duration-200"
            aria-label="YouTube"
          >
            <YouTubeIcon className="h-6 w-6" />
          </a>
          <button
            type="button"
            onClick={handleWhatsAppClick}
            className="flex items-center justify-center w-9 h-9 hover:scale-110 transition-transform duration-200"
            aria-label="WhatsApp"
          >
            <WhatsAppIcon className="h-6 w-6" />
          </button>
        </div>
      </div>

      {/* Column 2: Quick Links */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-badhees-800">Quick Links</h3>
        <ul className="space-y-2">
          {quickLinks.map((link) => (
            <li key={link.name}>
              <Link
                to={link.path}
                className="text-sm text-badhees-600 hover:text-badhees-accent flex items-center group"
              >
                <ArrowRight className="h-3 w-3 mr-2 opacity-0 -ml-5 group-hover:opacity-100 group-hover:ml-0 transition-all duration-300" />
                {link.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>

      {/* Column 3: Customer Service */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-badhees-800">Customer Service</h3>
        <ul className="space-y-2">
          {customerServiceLinks.map((link) => (
            <li key={link.name}>
              <Link
                to={link.path}
                className="text-sm text-badhees-600 hover:text-badhees-accent flex items-center group"
              >
                <ArrowRight className="h-3 w-3 mr-2 opacity-0 -ml-5 group-hover:opacity-100 group-hover:ml-0 transition-all duration-300" />
                {link.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>

      {/* Column 4: Contact */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-badhees-800">Contact Us</h3>
        <ul className="space-y-3">
          <li className="flex items-start">
            <MapPin className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-0.5" />
            <span className="text-sm text-badhees-600">
              The Badhees, Opposite Vasudha layout, Appajipura road, Koraluru, Bangalore 560067
            </span>
          </li>
          <li className="flex items-start">
            <Phone className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0 mt-1" />
            <div className="flex flex-col">
              <span className="text-sm text-badhees-600">9108344363</span>
              <span className="text-sm text-badhees-600 mt-1">8197705438</span>
            </div>
          </li>
          <li className="flex items-center">
            <Mail className="h-5 w-5 mr-3 text-badhees-400 flex-shrink-0" />
            <span className="text-sm text-badhees-600"><EMAIL></span>
          </li>
        </ul>
      </div>
    </div>
  );

  return (
    <footer className="bg-badhees-50 border-t border-badhees-100 safe-bottom">
      <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-16">
        {isMobile ? renderMobileFooter() : renderDesktopFooter()}

        {/* Copyright - Same for both mobile and desktop */}
        <div className="mt-8 pt-6 border-t border-badhees-100">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-badhees-400 mb-4 md:mb-0">
              © {new Date().getFullYear()} The Badhees. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <Link to="/privacy-policy" className="text-xs sm:text-sm text-badhees-400 hover:text-badhees-600 touch-target py-2">
                Privacy Policy
              </Link>
              <Link to="/terms-of-service" className="text-xs sm:text-sm text-badhees-400 hover:text-badhees-600 touch-target py-2">
                Terms of Service
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
