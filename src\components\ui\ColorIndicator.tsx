import React, { useRef, useEffect } from 'react';
import '@/styles/colorIndicator.css';

interface ColorIndicatorProps {
  color: string;
  className?: string;
}

// Component for Tailwind color names
const TailwindColorIndicator: React.FC<ColorIndicatorProps> = ({ color, className = '' }) => {
  return (
    <div className={`h-3 w-3 rounded-full mr-2 bg-${color}-500 ${className}`} />
  );
};

// Component for hex colors
const HexColorIndicator: React.FC<ColorIndicatorProps> = ({ color, className = '' }) => {
  const indicatorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (indicatorRef.current) {
      indicatorRef.current.style.setProperty('--indicator-color', color);
    }
  }, [color]);

  return (
    <div
      ref={indicatorRef}
      className={`color-indicator color-indicator-custom ${className}`}
    />
  );
};

// Main component that decides which indicator to use
export const ColorIndicator: React.FC<ColorIndicatorProps> = ({ color, className = '' }) => {
  // For Tailwind colors (like 'red', 'blue', etc.)
  if (!color.startsWith('#')) {
    return <TailwindColorIndicator color={color} className={className} />;
  }

  // For hex colors
  return <HexColorIndicator color={color} className={className} />;
};

export default ColorIndicator;
