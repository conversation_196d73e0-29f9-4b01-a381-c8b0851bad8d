
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Home, ShoppingCart, Grid, User, BookOpen } from "lucide-react";
import { useCart } from "@/context/SupabaseCartContext";
import { useAuth } from "@/context/SupabaseAuthContext";

interface NavItem {
  icon: React.ElementType;
  label: string;
  path: string;
  badge?: number;
}

/**
 * Bottom navigation bar for mobile devices
 */
const MobileBottomNav: React.FC = () => {
  const location = useLocation();
  const { itemCount } = useCart();
  const { isAuthenticated, isAdmin } = useAuth();

  const navItems: NavItem[] = [
    {
      icon: Home,
      label: "Home",
      path: "/",
    },
    {
      icon: Grid,
      label: "Categories",
      path: "/shop",
    },
    {
      icon: BookOpen,
      label: "Custom Projects",
      path: "/custom-interiors",
    },
    {
      icon: ShoppingCart,
      label: "Cart",
      path: "/cart",
      badge: itemCount > 0 ? itemCount : undefined,
    },
    {
      icon: User,
      label: "Profile",
      path: "/profile",
    },
  ];

  // Admin users see a different set of navigation
  if (isAdmin()) {
    return null; // Admin users use the sidebar navigation instead
  }

  return (
    <nav
      className="md:hidden fixed bottom-0 left-0 right-0 z-[9998] bg-white border-t border-badhees-100 shadow-lg safe-bottom mobile-bottom-nav"
    >
      <div className="grid grid-cols-5 h-[76px]">
        {navItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={cn(
              "flex flex-col items-center justify-center text-xs py-3 touch-target relative",
              "min-h-[76px] min-w-[44px] cursor-pointer",
              "hover:bg-badhees-50 active:bg-badhees-100 transition-colors",
              location.pathname === item.path
                ? "text-badhees-accent font-medium"
                : "text-badhees-600"
            )}
            aria-label={item.label}
            role="button"
            tabIndex={0}
          >
            <div className="relative">
              <div className={cn(
                "flex items-center justify-center mb-1.5 transition-all",
                location.pathname === item.path
                  ? "scale-110"
                  : ""
              )}>
                <item.icon className="h-6 w-6 pointer-events-none" />
              </div>
              {item.badge !== undefined && (
                <span className="absolute -top-1.5 -right-1.5 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-[10px] font-medium text-white pointer-events-none">
                  {item.badge > 99 ? "99+" : item.badge}
                </span>
              )}
            </div>
            <span className={cn(
              "font-medium transition-all pointer-events-none",
              location.pathname === item.path
                ? "scale-105"
                : ""
            )}>{item.label}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
};

export default MobileBottomNav;
