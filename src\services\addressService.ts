import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface Address {
  id?: string;
  user_id?: string;
  name: string;
  street: string;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  phone?: string;
  is_default_shipping?: boolean;
  is_default_billing?: boolean;
  created_at?: string;
  updated_at?: string;
}

/**
 * Get all addresses for a user
 * @param userId The user ID
 * @returns Array of addresses
 */
export const getUserAddresses = async (userId: string): Promise<Address[]> => {
  try {
    const { data, error } = await supabase
      .from('user_addresses')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user addresses:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getUserAddresses:', error);
    return [];
  }
};

/**
 * Get the default shipping address for a user
 * @param userId The user ID
 * @returns The default shipping address or null if not found
 */
export const getDefaultShippingAddress = async (userId: string): Promise<Address | null> => {
  try {
    const { data, error } = await supabase
      .from('user_addresses')
      .select('*')
      .eq('user_id', userId)
      .eq('is_default_shipping', true)
      .single();

    if (error) {
      if (error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error fetching default shipping address:', error);
      }
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getDefaultShippingAddress:', error);
    return null;
  }
};

/**
 * Get the default billing address for a user
 * @param userId The user ID
 * @returns The default billing address or null if not found
 */
export const getDefaultBillingAddress = async (userId: string): Promise<Address | null> => {
  try {
    const { data, error } = await supabase
      .from('user_addresses')
      .select('*')
      .eq('user_id', userId)
      .eq('is_default_billing', true)
      .single();

    if (error) {
      if (error.code !== 'PGRST116') { // PGRST116 is "no rows returned" error
        console.error('Error fetching default billing address:', error);
      }
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getDefaultBillingAddress:', error);
    return null;
  }
};

/**
 * Create a new address for a user
 * @param address The address to create
 * @returns The created address or null if creation failed
 */
export const createAddress = async (address: Address): Promise<Address | null> => {
  try {
    const { data, error } = await supabase
      .from('user_addresses')
      .insert([address])
      .select()
      .single();

    if (error) {
      console.error('Error creating address:', error);
      toast({
        title: 'Error creating address',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    toast({
      title: 'Address created',
      description: 'Your address has been saved successfully.',
    });

    return data;
  } catch (error) {
    console.error('Error in createAddress:', error);
    toast({
      title: 'Error creating address',
      description: 'An unexpected error occurred while creating your address.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Update an existing address
 * @param address The address to update
 * @returns The updated address or null if update failed
 */
export const updateAddress = async (address: Address): Promise<Address | null> => {
  if (!address.id) {
    console.error('Cannot update address without ID');
    return null;
  }

  try {
    const { data, error } = await supabase
      .from('user_addresses')
      .update(address)
      .eq('id', address.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating address:', error);
      toast({
        title: 'Error updating address',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    toast({
      title: 'Address updated',
      description: 'Your address has been updated successfully.',
    });

    return data;
  } catch (error) {
    console.error('Error in updateAddress:', error);
    toast({
      title: 'Error updating address',
      description: 'An unexpected error occurred while updating your address.',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Delete an address
 * @param addressId The address ID to delete
 * @returns True if successful, false otherwise
 */
export const deleteAddress = async (addressId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_addresses')
      .delete()
      .eq('id', addressId);

    if (error) {
      console.error('Error deleting address:', error);
      toast({
        title: 'Error deleting address',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Address deleted',
      description: 'Your address has been deleted successfully.',
    });

    return true;
  } catch (error) {
    console.error('Error in deleteAddress:', error);
    toast({
      title: 'Error deleting address',
      description: 'An unexpected error occurred while deleting your address.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Set an address as the default shipping address
 * @param addressId The address ID to set as default
 * @param userId The user ID
 * @returns True if successful, false otherwise
 */
export const setDefaultShippingAddress = async (addressId: string, userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_addresses')
      .update({ is_default_shipping: true })
      .eq('id', addressId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error setting default shipping address:', error);
      toast({
        title: 'Error setting default address',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Default address updated',
      description: 'Your default shipping address has been updated.',
    });

    return true;
  } catch (error) {
    console.error('Error in setDefaultShippingAddress:', error);
    toast({
      title: 'Error setting default address',
      description: 'An unexpected error occurred while updating your default address.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Set an address as the default billing address
 * @param addressId The address ID to set as default
 * @param userId The user ID
 * @returns True if successful, false otherwise
 */
export const setDefaultBillingAddress = async (addressId: string, userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('user_addresses')
      .update({ is_default_billing: true })
      .eq('id', addressId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error setting default billing address:', error);
      toast({
        title: 'Error setting default address',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Default address updated',
      description: 'Your default billing address has been updated.',
    });

    return true;
  } catch (error) {
    console.error('Error in setDefaultBillingAddress:', error);
    toast({
      title: 'Error setting default address',
      description: 'An unexpected error occurred while updating your default address.',
      variant: 'destructive',
    });
    return false;
  }
};

/**
 * Convert an Address object to the JSONB format used in orders
 * @param address The address to convert
 * @returns The address in JSONB format
 */
export const addressToJsonb = (address: Address): any => {
  return {
    name: address.name,
    street: address.street,
    city: address.city,
    state: address.state,
    postal_code: address.postal_code,
    country: address.country,
    phone: address.phone || '',
  };
};
