import { useEffect, useState, useRef, useCallback } from 'react';

/**
 * Hook for managing tab visibility and performance optimization
 * @param onVisibilityChange Optional callback when tab becomes visible after being hidden
 * @returns Object with tab visibility state and handlers
 */
export function useTabVisibility(onVisibilityChange?: () => void) {
  const [isVisible, setIsVisible] = useState(!document.hidden);
  const [wasHidden, setWasHidden] = useState(false);
  const onVisibilityChangeRef = useRef(onVisibilityChange);

  // Update callback ref
  useEffect(() => {
    onVisibilityChangeRef.current = onVisibilityChange;
  }, [onVisibilityChange]);

  useEffect(() => {
    const handleVisibilityChange = () => {
      const visible = !document.hidden;
      const previouslyVisible = isVisible;

      setIsVisible(visible);

      if (!visible) {
        setWasHidden(true);
      } else if (!previouslyVisible && wasHidden && onVisibilityChangeRef.current) {
        // Tab became visible after being hidden - trigger callback
        onVisibilityChangeRef.current();
        setWasHidden(false);
      }
    };

    const handleFocus = () => {
      const visible = !document.hidden;
      if (visible && wasHidden && onVisibilityChangeRef.current) {
        onVisibilityChangeRef.current();
        setWasHidden(false);
      }
      setIsVisible(visible);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);
    window.addEventListener('blur', () => setIsVisible(false));

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
      window.removeEventListener('blur', () => setIsVisible(false));
    };
  }, [isVisible, wasHidden]);

  const resetHiddenState = useCallback(() => {
    setWasHidden(false);
  }, []);

  return {
    isVisible,
    wasHidden,
    resetHiddenState,
  };
}

/**
 * Hook for managing intervals with tab visibility awareness
 * @param callback - Function to call on interval
 * @param delay - Delay in milliseconds
 * @param enabled - Whether the interval should be active
 */
export function useVisibilityAwareInterval(
  callback: () => void,
  delay: number,
  enabled: boolean = true
) {
  const { isVisible } = useTabVisibility();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const callbackRef = useRef(callback);

  // Update callback ref
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    if (!enabled || !isVisible) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    intervalRef.current = setInterval(() => {
      callbackRef.current();
    }, delay);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [delay, enabled, isVisible]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);
}

/**
 * Hook for implementing virtualized lists
 * @param itemCount Total number of items
 * @param itemHeight Height of each item in pixels
 * @param containerHeight Height of the container in pixels
 * @param overscan Number of items to render outside of the visible area
 * @returns Object with visible item indices and ref for the container
 */
export function useVirtualization(
  itemCount: number,
  itemHeight: number,
  containerHeight: number,
  overscan = 3
) {
  const [scrollTop, setScrollTop] = useState(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  const handleScroll = useCallback(() => {
    if (containerRef.current) {
      setScrollTop(containerRef.current.scrollTop);
    }
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    itemCount - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = Array.from(
    { length: endIndex - startIndex + 1 },
    (_, index) => startIndex + index
  );

  const totalHeight = itemCount * itemHeight;
  const offsetY = startIndex * itemHeight;

  return {
    containerRef,
    visibleItems,
    totalHeight,
    offsetY,
  };
}

/**
 * Hook for lazy loading images
 * @returns Object with image ref and loading state
 */
export function useLazyImage() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLImageElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      observer.disconnect();
    };
  }, []);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  return { imgRef, isLoaded, isInView, handleLoad };
}

/**
 * Hook for debouncing function calls
 * @param fn Function to debounce
 * @param delay Delay in milliseconds
 * @returns Debounced function
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number
): (...args: Parameters<T>) => void {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const fnRef = useRef(fn);

  // Update function reference without causing re-renders
  useEffect(() => {
    fnRef.current = fn;
  }, [fn]);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      timeoutRef.current = setTimeout(() => {
        fnRef.current(...args);
      }, delay);
    },
    [delay] // Remove fn from dependencies to prevent infinite loops
  );
}
