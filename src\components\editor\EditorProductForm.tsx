
import React, { useState, useCallback } from 'react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { X, Plus, Image as ImageIcon } from 'lucide-react';
import ProductImageUpload from './ProductImageUpload';

interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  salePrice?: number;
  isSale?: boolean;
  isNew?: boolean;
  isFeatured?: boolean;
  image: string;
  images?: string[];
  category: string;
  status: 'active' | 'draft' | 'deleted';
  stock: number;
  sku?: string;
  specifications?: Record<string, string>;
  customizationAvailable?: boolean;
  // Shipping fields
  shippingFeeBangalore?: number;
  shippingFeeOutsideBangalore?: number;
  freeShippingThreshold?: number;
  shippingNotes?: string;
}

interface EditorProductFormProps {
  product: Product;
  onSave: (product: Product) => void;
  onCancel: () => void;
}

// Define form schema
const formSchema = z.object({
  name: z.string().min(2, { message: "Product name must be at least 2 characters" }),
  description: z.string().optional(),
  price: z.coerce.number().positive({ message: "Price must be a positive number" }),
  salePrice: z.coerce.number().nonnegative().optional(),
  isSale: z.boolean().default(false),
  isNew: z.boolean().default(false),
  isFeatured: z.boolean().default(false),
  customizationAvailable: z.boolean().default(false),
  image: z.string().optional(), // Now accepts any string for image URL
  images: z.array(z.string()).optional(), // Now accepts any string array for image URLs
  category: z.string(),
  status: z.enum(['active', 'draft', 'deleted']),
  stock: z.coerce.number().nonnegative(),
  sku: z.string().optional(),
  specifications: z.record(z.string(), z.string()).optional(),
  // Shipping fields
  shippingFeeBangalore: z.coerce.number().nonnegative().optional(),
  shippingFeeOutsideBangalore: z.coerce.number().nonnegative().optional(),
  freeShippingThreshold: z.coerce.number().nonnegative().optional(),
  shippingNotes: z.string().optional(),
});

// Complete list of categories for the furniture store
const categories = [
  { value: "Living Room", label: "Living Room" },
  { value: "Bedroom", label: "Bedroom" },
  { value: "Dining Room", label: "Dining Room" },
  { value: "Home Office", label: "Home Office" },
  { value: "Outdoor", label: "Outdoor" },
  { value: "Storage", label: "Storage" },
  { value: "Kids & Nursery", label: "Kids & Nursery" },
  { value: "Accent Furniture", label: "Accent Furniture" },
  { value: "Decorative Accessories", label: "Decorative Accessories" },
  { value: "Custom & Specialty", label: "Custom & Specialty" },
];

const EditorProductForm: React.FC<EditorProductFormProps> = ({
  product,
  onSave,
  onCancel
}) => {
  const [newSpecName, setNewSpecName] = useState('');
  const [newSpecValue, setNewSpecValue] = useState('');

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      ...product,
      // Ensure numeric fields are numbers not strings
      price: Number(product.price),
      salePrice: product.salePrice ? Number(product.salePrice) : undefined,
      stock: Number(product.stock),
      images: product.images || [],
      specifications: product.specifications || {},
      // Shipping fields with defaults
      shippingFeeBangalore: product.shippingFeeBangalore || 50,
      shippingFeeOutsideBangalore: product.shippingFeeOutsideBangalore || 0,
      freeShippingThreshold: product.freeShippingThreshold || 2000,
      shippingNotes: product.shippingNotes || 'Free shipping within Bangalore for orders above threshold. Outside Bangalore: Our team will contact you for shipping calculation.',
    },
  });

  const handleSubmit = (values: z.infer<typeof formSchema>) => {
    onSave({
      ...product,
      ...values,
    });
  };

  const handleMainImageChange = useCallback((url: string | null) => {
    form.setValue('image', url || '');
  }, [form]);

  const addImage = useCallback((url: string) => {
    if (!url) return;
    const currentImages = form.getValues('images') || [];
    form.setValue('images', [...currentImages, url]);
  }, [form]);

  const removeImage = useCallback((indexToRemove: number) => {
    const currentImages = form.getValues('images') || [];
    form.setValue('images', currentImages.filter((_, index) => index !== indexToRemove));
  }, [form]);

  const addSpecification = useCallback(() => {
    if (!newSpecName.trim() || !newSpecValue.trim()) return;

    const currentSpecs = form.getValues('specifications') || {};
    form.setValue('specifications', {
      ...currentSpecs,
      [newSpecName.trim()]: newSpecValue.trim()
    });

    setNewSpecName('');
    setNewSpecValue('');
  }, [newSpecName, newSpecValue, form]);

  const removeSpecification = useCallback((specName: string) => {
    const currentSpecs = form.getValues('specifications') || {};
    const updatedSpecs = { ...currentSpecs };
    delete updatedSpecs[specName];
    form.setValue('specifications', updatedSpecs);
  }, [form]);

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Basic Info */}
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Product Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Product name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Product description"
                      className="min-h-[120px]"
                      {...field}
                      value={field.value || ''}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div>
              <FormLabel>Main Product Image</FormLabel>
              <p className="text-sm text-badhees-500 mb-2">Upload the primary product image</p>
              <ProductImageUpload
                productId={product.id}
                currentImageUrl={form.watch("image")}
                onImageChange={handleMainImageChange}
                isPrimary={true}
              />
              <FormMessage />
            </div>

            {/* Additional Images Section */}
            <div className="space-y-4">
              <div>
                <FormLabel>Additional Images</FormLabel>
                <p className="text-sm text-badhees-500 mb-2">Upload up to 6 additional product images</p>

                <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mt-3">
                  {/* Show existing additional images */}
                  {form.watch("images") && form.watch("images").map((img, index) => (
                    <div key={index} className="relative">
                      <ProductImageUpload
                        productId={product.id}
                        currentImageUrl={img}
                        onImageChange={(url) => {
                          if (url === null) {
                            removeImage(index);
                          }
                        }}
                      />
                    </div>
                  ))}

                  {/* Add new image slot if less than 6 images */}
                  {(!form.watch("images") || form.watch("images").length < 6) && (
                    <ProductImageUpload
                      productId={product.id}
                      onImageChange={(url) => {
                        if (url) addImage(url);
                      }}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Middle column - Pricing & Status */}
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Price (₹)</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" min="0" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isSale"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>On Sale</FormLabel>
                    <FormDescription>
                      Mark this product as on sale
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {form.watch("isSale") && (
              <FormField
                control={form.control}
                name="salePrice"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Sale Price (₹)</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" min="0" {...field} value={field.value || ''} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="isNew"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>New Product</FormLabel>
                    <FormDescription>
                      Mark this product as new
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isFeatured"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Featured Product</FormLabel>
                    <FormDescription>
                      Show this product on the home page in featured collection
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customizationAvailable"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Available for Customization</FormLabel>
                    <FormDescription>
                      Allow customers to request customization for this product
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="product-status">Product Status</FormLabel>
                  <select
                    id="product-status"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={field.value}
                    onChange={field.onChange}
                    aria-label="Product Status"
                  >
                    <option value="active">Active (Visible)</option>
                    <option value="draft">Draft (Hidden)</option>
                    <option value="deleted">Archived</option>
                  </select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Right column - Inventory & Categories */}
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="stock"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stock Quantity</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="sku"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SKU</FormLabel>
                  <FormControl>
                    <Input placeholder="SKU123" {...field} value={field.value || ''} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel htmlFor="product-category">Category</FormLabel>
                  <select
                    id="product-category"
                    className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={field.value}
                    onChange={field.onChange}
                    aria-label="Product Category"
                  >
                    <option value="" disabled>Select a category</option>
                    {categories.map(({ value, label }) => (
                      <option key={value} value={value}>{label}</option>
                    ))}
                  </select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Product Specifications */}
            <div className="space-y-4">
              <div>
                <FormLabel>Product Specifications</FormLabel>
                <p className="text-sm text-badhees-500 mb-2">Add specifications like dimensions, materials, weight, etc.</p>

                <div className="grid grid-cols-2 gap-2 mb-2">
                  <Input
                    placeholder="Specification name (e.g. Dimensions)"
                    value={newSpecName}
                    onChange={(e) => setNewSpecName(e.target.value)}
                  />
                  <Input
                    placeholder="Value (e.g. W: 30in x D: 32in x H: 36in)"
                    value={newSpecValue}
                    onChange={(e) => setNewSpecValue(e.target.value)}
                  />
                </div>

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={addSpecification}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Specification
                </Button>
              </div>

              {/* Show existing specifications */}
              {form.watch("specifications") && Object.keys(form.watch("specifications") || {}).length > 0 && (
                <div className="border rounded-md p-4">
                  <p className="text-sm font-medium mb-2">Current Specifications:</p>
                  <div className="space-y-2">
                    {Object.entries(form.watch("specifications") || {}).map(([name, value]) => (
                      <div key={name} className="flex justify-between items-center p-2 bg-badhees-50 rounded-md">
                        <div>
                          <span className="font-medium">{name}:</span> {value}
                        </div>
                        <button
                          type="button"
                          className="text-red-500 hover:text-red-700"
                          onClick={() => removeSpecification(name)}
                          aria-label={`Remove ${name} specification`}
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Shipping Configuration */}
            <div className="space-y-4">
              <div>
                <FormLabel className="text-lg font-semibold">Shipping Configuration</FormLabel>
                <p className="text-sm text-badhees-500 mb-4">Set shipping fees and policies for this product</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="shippingFeeBangalore"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Shipping Fee - Bangalore (₹)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="50.00"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>
                          Shipping fee for deliveries within Bangalore area
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="shippingFeeOutsideBangalore"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Shipping Fee - Outside Bangalore (₹)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            placeholder="0.00"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormDescription>
                          Leave as 0 for manual calculation. Team will contact customer.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="freeShippingThreshold"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Free Shipping Threshold (₹)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          min="0"
                          placeholder="2000.00"
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        Orders above this amount get free shipping within Bangalore. Leave empty for no free shipping.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="shippingNotes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Shipping Notes</FormLabel>
                      <FormControl>
                        <textarea
                          className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                          placeholder="Additional shipping information for customers..."
                          {...field}
                          value={field.value || ''}
                        />
                      </FormControl>
                      <FormDescription>
                        Additional shipping information displayed to customers
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 pt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button type="submit">
            Save Product
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default EditorProductForm;
