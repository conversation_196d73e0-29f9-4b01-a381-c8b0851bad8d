/**
 * Supabase Products Service (Compatibility Layer)
 *
 * This module provides backward compatibility with the old supabaseProductsService.
 * It re-exports all functionality from the new modular product services.
 *
 * @deprecated Use the new modular product services instead:
 * import { getProducts, createProduct, etc. } from '@/services/product';
 */

// Import and re-export types
export type {
  FrontendProduct,
  ProductInput,
  SupabaseProduct,
  ProductImage
} from './product';

// Import Category directly from the dedicated file
export type { Category } from '@/types/category';

// Import and re-export functions
export {
  mapSupabaseProductToFrontend,
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct,
  updateProductStock,
  getCategories
} from './product';
