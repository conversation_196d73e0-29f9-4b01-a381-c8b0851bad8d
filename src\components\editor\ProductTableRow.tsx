
import React from 'react';
import { Eye, Pencil, Archive, RotateCcw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import {
  TableCell,
  TableRow,
} from "@/components/ui/table";
import { Product } from '@/services/editorProductsService';

interface ProductTableRowProps {
  product: Product;
  isSelected: boolean;
  onSelectProduct: (productId: string, checked: boolean) => void;
  onEdit: (product: Product) => void;
  onStatusChange: (id: string, status: 'active' | 'draft' | 'deleted') => void;
}

const ProductTableRow: React.FC<ProductTableRowProps> = ({
  product,
  isSelected,
  onSelectProduct,
  onEdit,
  onStatusChange
}) => {
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Active</Badge>;
      case 'draft':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Draft</Badge>;
      case 'deleted':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Archived</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStockBadge = (stock: number) => {
    if (stock <= 0) {
      return <Badge variant="outline" className="bg-red-100 text-red-800">Out of Stock</Badge>;
    } else if (stock < 10) {
      return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Low Stock: {stock}</Badge>;
    } else {
      return <Badge variant="outline" className="bg-green-100 text-green-800">In Stock: {stock}</Badge>;
    }
  };

  return (
    <TableRow className={product.status === 'deleted' ? 'bg-muted/30' : ''}>
      <TableCell>
        <Checkbox
          checked={isSelected}
          onCheckedChange={(checked) => onSelectProduct(product.id, !!checked)}
        />
      </TableCell>
      <TableCell>
        <div className="h-10 w-10 rounded-md overflow-hidden">
          <img
            src={product.image}
            alt={product.name}
            className="h-full w-full object-cover"
          />
        </div>
      </TableCell>
      <TableCell>
        <div>
          <p className="font-medium">{product.name}</p>
          <p className="text-xs text-muted-foreground truncate max-w-[200px]">
            {product.description?.substring(0, 60)}
            {product.description && product.description.length > 60 ? '...' : ''}
          </p>
        </div>
      </TableCell>
      <TableCell>
        <div>
          <p>{product.category}</p>
        </div>
      </TableCell>
      <TableCell>
        {product.isSale && product.salePrice ? (
          <div>
            <p>₹{product.salePrice.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</p>
            <p className="text-xs text-muted-foreground line-through">₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</p>
          </div>
        ) : (
          <p>₹{product.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</p>
        )}
      </TableCell>
      <TableCell>
        {getStockBadge(product.stock)}
      </TableCell>
      <TableCell>
        {getStatusBadge(product.status)}
      </TableCell>
      <TableCell className="text-right">
        <div className="flex justify-end gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => onEdit(product)}
          >
            <Pencil className="h-4 w-4" />
          </Button>

          <Button
            size="sm"
            variant="outline"
            asChild
          >
            <a href={`/products/${product.id}`} target="_blank" aria-label={`View ${product.name}`}>
              <Eye className="h-4 w-4" />
            </a>
          </Button>

          {product.status !== 'deleted' ? (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onStatusChange(product.id, 'deleted')}
            >
              <Archive className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              size="sm"
              variant="outline"
              onClick={() => onStatusChange(product.id, 'active')}
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
};

export default ProductTableRow;
