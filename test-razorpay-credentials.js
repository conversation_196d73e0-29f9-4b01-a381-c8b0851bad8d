/**
 * Test Razorpay Credentials
 * 
 * This script tests if your Razorpay credentials are working
 * Run with: node test-razorpay-credentials.js
 */

import Ra<PERSON>pay from 'razorpay';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testRazorpayCredentials() {
  console.log('🧪 Testing Razorpay Credentials...\n');

  // Check environment variables
  console.log('🔧 Environment Variables:');
  console.log(`   RAZORPAY_KEY_ID: ${process.env.RAZORPAY_KEY_ID ? '✅ Set' : '❌ Missing'}`);
  console.log(`   RAZORPAY_SECRET: ${process.env.RAZORPAY_SECRET ? '✅ Set' : '❌ Missing'}`);
  console.log('');

  if (!process.env.RAZORPAY_KEY_ID || !process.env.RAZORPAY_SECRET) {
    console.error('❌ Missing Razorpay credentials in .env file');
    console.log('Please add the following to your .env file:');
    console.log('RAZORPAY_KEY_ID=your_key_id');
    console.log('RAZORPAY_SECRET=your_secret');
    process.exit(1);
  }

  try {
    // Initialize Razorpay
    console.log('🚀 Initializing Razorpay...');
    const razorpay = new Razorpay({
      key_id: process.env.RAZORPAY_KEY_ID,
      key_secret: process.env.RAZORPAY_SECRET,
    });
    console.log('✅ Razorpay initialized successfully\n');

    // Test 1: Create a test order
    console.log('📦 Test 1: Creating a test order...');
    const orderOptions = {
      amount: 10000, // 100 INR in paise
      currency: 'INR',
      receipt: `test_${Date.now()}`,
      notes: {
        test: true,
        description: 'Test order for credential verification'
      }
    };

    const order = await razorpay.orders.create(orderOptions);
    console.log('✅ Test order created successfully!');
    console.log(`   Order ID: ${order.id}`);
    console.log(`   Amount: ₹${order.amount / 100}`);
    console.log(`   Status: ${order.status}`);
    console.log('');

    // Test 2: Fetch the created order
    console.log('🔍 Test 2: Fetching the created order...');
    const fetchedOrder = await razorpay.orders.fetch(order.id);
    console.log('✅ Order fetched successfully!');
    console.log(`   Fetched Order ID: ${fetchedOrder.id}`);
    console.log(`   Matches Created Order: ${fetchedOrder.id === order.id ? '✅' : '❌'}`);
    console.log('');

    // Test 3: List recent orders
    console.log('📋 Test 3: Listing recent orders...');
    const orders = await razorpay.orders.all({ count: 5 });
    console.log('✅ Orders listed successfully!');
    console.log(`   Total orders retrieved: ${orders.items.length}`);
    console.log('   Recent orders:');
    orders.items.forEach((order, index) => {
      console.log(`     ${index + 1}. ${order.id} - ₹${order.amount / 100} - ${order.status}`);
    });
    console.log('');

    // Summary
    console.log('🎉 All tests passed! Your Razorpay credentials are working correctly.');
    console.log('');
    console.log('📝 Summary:');
    console.log('   ✅ Razorpay initialization successful');
    console.log('   ✅ Order creation working');
    console.log('   ✅ Order fetching working');
    console.log('   ✅ Order listing working');
    console.log('');
    console.log('🚀 You can now proceed with local testing of the payment system.');

  } catch (error) {
    console.error('❌ Razorpay test failed!');
    console.error('');
    console.error('Error details:');
    console.error(`   Message: ${error.message}`);
    console.error(`   Code: ${error.code || 'N/A'}`);
    console.error(`   Description: ${error.description || 'N/A'}`);
    console.error(`   Source: ${error.source || 'N/A'}`);
    console.error(`   Step: ${error.step || 'N/A'}`);
    console.error(`   Reason: ${error.reason || 'N/A'}`);
    console.error('');

    // Common error solutions
    console.log('🔧 Common solutions:');
    if (error.message.includes('authentication')) {
      console.log('   • Check if your RAZORPAY_KEY_ID and RAZORPAY_SECRET are correct');
      console.log('   • Verify you are using the right environment (test/live) keys');
      console.log('   • Ensure there are no extra spaces in your .env file');
    } else if (error.message.includes('network') || error.message.includes('timeout')) {
      console.log('   • Check your internet connection');
      console.log('   • Verify Razorpay API is accessible from your network');
    } else if (error.message.includes('rate limit')) {
      console.log('   • You may have hit Razorpay API rate limits');
      console.log('   • Wait a few minutes and try again');
    } else {
      console.log('   • Check Razorpay dashboard for any account issues');
      console.log('   • Verify your API keys are active and not expired');
      console.log('   • Contact Razorpay support if the issue persists');
    }

    process.exit(1);
  }
}

// Run the test
testRazorpayCredentials().catch(console.error);
