import React, { useState } from 'react';
import { useAuth } from '@/context/SupabaseAuthContext';
import CustomizationRequestForm from '@/components/products/CustomizationRequestForm';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const TestCustomization: React.FC = () => {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const { user, isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        <Card>
          <CardHeader>
            <CardTitle>Test Customization Request</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-2">Authentication Status</h3>
              <p>Authenticated: {isAuthenticated ? '✅ Yes' : '❌ No'}</p>
              {user && (
                <div className="mt-2 text-sm">
                  <p>User ID: {user.id}</p>
                  <p>Email: {user.email}</p>
                </div>
              )}
            </div>

            <div className="p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold mb-2">Test Product</h3>
              <p>Product Name: Modern Sofa</p>
              <p>Product ID: test-product-123</p>
            </div>

            <Button 
              onClick={() => setIsFormOpen(true)}
              className="w-full"
            >
              Open Customization Request Form
            </Button>

            <div className="text-sm text-gray-600">
              <p><strong>Expected behavior:</strong></p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>If not signed in: Should show sign-in prompt</li>
                <li>If signed in: Should show form with pre-filled user data</li>
                <li>Form submission should work without RLS errors</li>
                <li>Admin should receive real-time notification</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <CustomizationRequestForm
          isOpen={isFormOpen}
          onClose={() => setIsFormOpen(false)}
          productId="test-product-123"
          productName="Modern Sofa"
        />
      </div>
    </div>
  );
};

export default TestCustomization;
