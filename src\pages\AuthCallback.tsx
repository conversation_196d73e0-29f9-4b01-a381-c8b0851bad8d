import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Loader2 } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

/**
 * Auth Callback Page
 *
 * This page handles the OAuth callback from providers like Google.
 * It processes the authentication response and redirects the user.
 */
const AuthCallback = () => {
  const navigate = useNavigate();
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Handle the auth callback from URL hash/search params
        const { data, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Auth callback error:', error);
          throw error;
        }

        if (data?.session) {
          // Successfully authenticated
          console.log('Authentication successful:', data.session.user.email);
          toast({
            title: "Login successful",
            description: `Welcome back, ${data.session.user.email}!`,
          });

          // Redirect to home page
          navigate('/', { replace: true });
        } else {
          // Try to get session from URL parameters
          const urlParams = new URLSearchParams(window.location.search);
          const accessToken = urlParams.get('access_token');
          const refreshToken = urlParams.get('refresh_token');

          if (accessToken) {
            // Set session manually if tokens are in URL
            const { data: sessionData, error: sessionError } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken || '',
            });

            if (sessionError) {
              throw sessionError;
            }

            if (sessionData?.session) {
              toast({
                title: "Login successful",
                description: `Welcome back, ${sessionData.session.user.email}!`,
              });
              navigate('/', { replace: true });
              return;
            }
          }

          // No session found
          console.log('No session found in callback');
          setError("Authentication failed. Please try again.");
          setTimeout(() => navigate('/login', { replace: true }), 3000);
        }
      } catch (err) {
        console.error('Auth callback error:', err);
        setError(err instanceof Error ? err.message : "Authentication failed");
        setTimeout(() => navigate('/login', { replace: true }), 3000);
      }
    };

    handleAuthCallback();
  }, [navigate]);

  if (error) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4">
        <div className="bg-red-50 border border-red-200 rounded-md p-4 max-w-md w-full">
          <h1 className="text-lg font-medium text-red-800 mb-2">Authentication Error</h1>
          <p className="text-red-700">{error}</p>
          <p className="text-sm text-red-600 mt-2">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4">
      <div className="flex flex-col items-center">
        <Loader2 className="h-12 w-12 animate-spin text-badhees-600 mb-4" />
        <h1 className="text-xl font-bold text-badhees-800 mb-2">Completing Sign In</h1>
        <p className="text-badhees-600">Please wait while we authenticate you...</p>
      </div>
    </div>
  );
};

export default AuthCallback;
