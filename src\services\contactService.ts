import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface ContactSubmission {
  id?: string;
  full_name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  user_id?: string;
  status?: 'new' | 'read' | 'replied' | 'archived';
  created_at?: string;
  updated_at?: string;
  // Additional fields for UI display
  is_customer?: boolean;
}

/**
 * Submit a new contact form
 * @param submission The contact form data
 * @returns The created submission or null if creation failed
 */
export const submitContactForm = async (submission: ContactSubmission): Promise<ContactSubmission | null> => {
  try {
    // Make sure we're using the public client for anonymous submissions
    // This ensures the RLS policy works correctly
    const { data, error } = await supabase
      .from('contact_submissions')
      .insert([submission])
      .select()
      .single();

    if (error) {
      console.error('Error submitting contact form:', error);
      toast({
        title: 'Error submitting form',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    toast({
      title: 'Message sent',
      description: 'Your message has been successfully submitted. We will contact you soon.',
    });

    return data;
  } catch (error: any) {
    console.error('Error in submitContactForm:', error);
    toast({
      title: 'Error submitting form',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Get all contact form submissions (admin only)
 * @param status Optional status filter
 * @returns Array of contact submissions
 */
export const getContactSubmissions = async (status?: string): Promise<ContactSubmission[]> => {
  try {
    // Query contact submissions
    let query = supabase
      .from('contact_submissions')
      .select('*');

    if (status && status !== 'all') {
      query = query.eq('status', status);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching contact submissions:', error);
      toast({
        title: 'Database Error',
        description: 'Failed to fetch contact submissions. Please try again.',
        variant: 'destructive',
      });
      return [];
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Process the data to add is_customer flag
    const processedData = data.map(item => ({
      ...item,
      is_customer: !!item.user_id
    }));

    return processedData;
  } catch (error: any) {
    console.error('Error in getContactSubmissions:', error);
    toast({
      title: 'Unexpected Error',
      description: error.message || 'An error occurred while fetching contact submissions',
      variant: 'destructive',
    });
    return [];
  }
};

/**
 * Update a contact submission status (admin only)
 * @param submissionId The submission ID
 * @param status The new status
 * @returns True if update was successful, false otherwise
 */
export const updateContactSubmissionStatus = async (
  submissionId: string,
  status: 'new' | 'read' | 'replied' | 'archived'
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('contact_submissions')
      .update({
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', submissionId);

    if (error) {
      console.error('Error updating contact submission:', error);
      toast({
        title: 'Error updating submission',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Submission updated',
      description: `Submission status has been updated to ${status}`,
    });

    return true;
  } catch (error: any) {
    console.error('Error in updateContactSubmissionStatus:', error);
    toast({
      title: 'Error updating submission',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return false;
  }
};
