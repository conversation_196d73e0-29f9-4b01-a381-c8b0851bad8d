/**
 * Formats a number as Indian Rupees (₹)
 * @param amount - The amount to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export const formatCurrency = (
  amount: number,
  options: {
    decimals?: number;
    showSymbol?: boolean;
    showCode?: boolean;
  } = {}
): string => {
  const {
    decimals = 2,
    showSymbol = true,
    showCode = false,
  } = options;

  // Format the number with Indian thousands separator and decimal places
  const formattedAmount = new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);

  // Build the final string
  let result = '';
  
  if (showSymbol) {
    result += '₹';
  }
  
  result += formattedAmount;
  
  if (showCode) {
    result += ' INR';
  }
  
  return result;
};
