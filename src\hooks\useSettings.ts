/**
 * Settings Hook
 * 
 * React Query hooks for managing application settings.
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getPaymentMethodSettings, 
  updatePaymentMethodSettings, 
  toggleCOD,
  isCODEnabled,
  getAllSettings,
  PaymentMethodSettings 
} from '@/services/settingsService';

// Query keys for settings
export const settingsKeys = {
  all: ['settings'] as const,
  paymentMethods: () => [...settingsKeys.all, 'payment-methods'] as const,
  codEnabled: () => [...settingsKeys.all, 'cod-enabled'] as const,
  allSettings: () => [...settingsKeys.all, 'all-settings'] as const,
};

/**
 * Hook to get payment method settings
 */
export function usePaymentMethodSettings() {
  return useQuery({
    queryKey: settingsKeys.paymentMethods(),
    queryFn: getPaymentMethodSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to check if COD is enabled
 */
export function useCODEnabled() {
  return useQuery({
    queryKey: settingsKeys.codEnabled(),
    queryFn: isCODEnabled,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to get all settings
 */
export function useAllSettings() {
  return useQuery({
    queryKey: settingsKeys.allSettings(),
    queryFn: getAllSettings,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

/**
 * Hook to update payment method settings
 */
export function useUpdatePaymentMethodSettings() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (settings: PaymentMethodSettings) => updatePaymentMethodSettings(settings),
    onSuccess: () => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: settingsKeys.paymentMethods() });
      queryClient.invalidateQueries({ queryKey: settingsKeys.codEnabled() });
      queryClient.invalidateQueries({ queryKey: settingsKeys.allSettings() });
    },
  });
}

/**
 * Hook to toggle COD availability
 */
export function useToggleCOD() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (enabled: boolean) => toggleCOD(enabled),
    onSuccess: () => {
      // Invalidate and refetch related queries
      queryClient.invalidateQueries({ queryKey: settingsKeys.paymentMethods() });
      queryClient.invalidateQueries({ queryKey: settingsKeys.codEnabled() });
      queryClient.invalidateQueries({ queryKey: settingsKeys.allSettings() });
    },
  });
}
