import { ProjectType } from "@/types/project";
import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

// Migration has been completed, no need to check localStorage anymore
export const checkForLocalProjects = (): boolean => {
  return false;
};

// Get all projects or filter by category
export const getCompletedProjects = async (categoryId?: string): Promise<ProjectType[]> => {
  try {
    let query;

    if (categoryId) {
      // Use the function to get projects by category
      const { data, error } = await supabase
        .rpc('get_completed_projects_by_category', { p_category_id: categoryId });

      if (error) throw error;
      query = data || [];
    } else {
      // Get all projects (including drafts and deleted for admin)
      const { data, error } = await supabase
        .rpc('get_all_completed_projects');

      if (error) throw error;
      query = data || [];
    }

    // Transform the data to match ProjectType
    return query.map((project: any) => ({
      id: project.id,
      name: project.name,
      description: project.description,
      category: project.category,
      categoryId: project.category_id,
      budget: project.budget,
      completionDate: project.completion_date,
      clientName: project.client_name,
      location: project.location,
      materials: project.materials,
      images: project.images || [],
      videoUrl: project.video_url,
      createdAt: project.created_at,
      status: project.status
    }));
  } catch (error) {
    console.error('Error fetching completed projects:', error);
    return [];
  }
};

// Get a specific project by ID
export const getProjectById = async (projectId: string): Promise<ProjectType | undefined> => {
  try {
    const { data, error } = await supabase
      .from('completed_projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (error) throw error;

    if (!data) return undefined;

    // Transform the data to match ProjectType
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      category: data.category,
      categoryId: data.category_id,
      budget: data.budget,
      completionDate: data.completion_date,
      clientName: data.client_name,
      location: data.location,
      materials: data.materials,
      images: data.images || [],
      videoUrl: data.video_url,
      createdAt: data.created_at,
      status: data.status
    };
  } catch (error) {
    console.error('Error fetching project by ID:', error);
    return undefined;
  }
};

// Add a new project
export const addProject = async (project: Omit<ProjectType, 'id' | 'createdAt'>): Promise<ProjectType | null> => {
  try {
    // Get current user ID
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to add a project.",
        variant: "destructive"
      });
      return null;
    }

    // Transform the data to match Supabase schema
    const newProject = {
      name: project.name,
      description: project.description,
      category: project.category,
      category_id: project.categoryId,
      budget: project.budget,
      completion_date: project.completionDate,
      client_name: project.clientName,
      location: project.location,
      materials: project.materials,
      images: project.images,
      video_url: project.videoUrl,
      status: project.status,
      created_by: user.id
    };

    const { data, error } = await supabase
      .from('completed_projects')
      .insert([newProject])
      .select()
      .single();

    if (error) throw error;

    toast({
      title: "Project Added",
      description: "The project has been successfully added.",
    });

    // Transform the response to match ProjectType
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      category: data.category,
      categoryId: data.category_id,
      budget: data.budget,
      completionDate: data.completion_date,
      clientName: data.client_name,
      location: data.location,
      materials: data.materials,
      images: data.images || [],
      videoUrl: data.video_url,
      createdAt: data.created_at,
      status: data.status
    };
  } catch (error: any) {
    console.error('Error adding project:', error);
    toast({
      title: "Error Adding Project",
      description: error.message || "An unexpected error occurred.",
      variant: "destructive"
    });
    return null;
  }
};

// Update an existing project
export const updateProject = async (projectId: string, updatedData: Partial<ProjectType>): Promise<ProjectType | undefined> => {
  try {
    // Transform the data to match Supabase schema
    const transformedData: any = {};

    if (updatedData.name !== undefined) transformedData.name = updatedData.name;
    if (updatedData.description !== undefined) transformedData.description = updatedData.description;
    if (updatedData.category !== undefined) transformedData.category = updatedData.category;
    if (updatedData.categoryId !== undefined) transformedData.category_id = updatedData.categoryId;
    if (updatedData.budget !== undefined) transformedData.budget = updatedData.budget;
    if (updatedData.completionDate !== undefined) transformedData.completion_date = updatedData.completionDate;
    if (updatedData.clientName !== undefined) transformedData.client_name = updatedData.clientName;
    if (updatedData.location !== undefined) transformedData.location = updatedData.location;
    if (updatedData.materials !== undefined) transformedData.materials = updatedData.materials;
    if (updatedData.images !== undefined) transformedData.images = updatedData.images;
    if (updatedData.videoUrl !== undefined) transformedData.video_url = updatedData.videoUrl;
    if (updatedData.status !== undefined) transformedData.status = updatedData.status;

    const { data, error } = await supabase
      .from('completed_projects')
      .update(transformedData)
      .eq('id', projectId)
      .select()
      .single();

    if (error) throw error;

    toast({
      title: "Project Updated",
      description: "The project has been successfully updated.",
    });

    // Transform the response to match ProjectType
    return {
      id: data.id,
      name: data.name,
      description: data.description,
      category: data.category,
      categoryId: data.category_id,
      budget: data.budget,
      completionDate: data.completion_date,
      clientName: data.client_name,
      location: data.location,
      materials: data.materials,
      images: data.images || [],
      videoUrl: data.video_url,
      createdAt: data.created_at,
      status: data.status
    };
  } catch (error: any) {
    console.error('Error updating project:', error);
    toast({
      title: "Error Updating Project",
      description: error.message || "An unexpected error occurred.",
      variant: "destructive"
    });
    return undefined;
  }
};

// Delete a project (soft delete by setting status to 'deleted')
export const deleteProject = async (projectId: string): Promise<boolean> => {
  try {
    // First check if the project exists
    const { data: existingProject, error: fetchError } = await supabase
      .from('completed_projects')
      .select('status')
      .eq('id', projectId)
      .single();

    if (fetchError) throw fetchError;

    if (!existingProject) {
      toast({
        title: "Error",
        description: "Project not found.",
        variant: "destructive"
      });
      return false;
    }

    // If the project is already deleted, permanently remove it
    if (existingProject.status === 'deleted') {
      const { error: deleteError } = await supabase
        .from('completed_projects')
        .delete()
        .eq('id', projectId);

      if (deleteError) throw deleteError;
    } else {
      // Otherwise soft delete
      const { error: updateError } = await supabase
        .from('completed_projects')
        .update({ status: 'deleted' })
        .eq('id', projectId);

      if (updateError) throw updateError;
    }

    toast({
      title: "Project Deleted",
      description: "The project has been successfully deleted.",
    });

    return true;
  } catch (error: any) {
    console.error('Error deleting project:', error);
    toast({
      title: "Error Deleting Project",
      description: error.message || "An unexpected error occurred.",
      variant: "destructive"
    });
    return false;
  }
};
