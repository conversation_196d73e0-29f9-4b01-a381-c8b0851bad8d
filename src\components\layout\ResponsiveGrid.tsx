
import React from "react";
import { cn } from "@/lib/utils";

interface ResponsiveGridProps {
  children: React.ReactNode;
  className?: string;
  cols?: {
    default: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: string;
}

/**
 * A responsive grid component that adjusts columns based on screen size
 */
const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  className,
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = "gap-4 sm:gap-6"
}) => {
  const getGridCols = () => {
    return [
      `grid-cols-${cols.default}`,
      cols.sm && `sm:grid-cols-${cols.sm}`,
      cols.md && `md:grid-cols-${cols.md}`,
      cols.lg && `lg:grid-cols-${cols.lg}`,
      cols.xl && `xl:grid-cols-${cols.xl}`,
    ].filter(Boolean).join(" ");
  };

  return (
    <div 
      className={cn(
        "grid w-full",
        getGridCols(),
        gap,
        className
      )}
    >
      {children}
    </div>
  );
};

export default ResponsiveGrid;
