import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/SupabaseAuthContext';
import { submitConsultationRequest } from '@/services/consultationRequestService';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Loader2, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';

interface ConsultationRequestFormProps {
  onSuccess?: () => void;
}

const ConsultationRequestForm: React.FC<ConsultationRequestFormProps> = ({ onSuccess }) => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    project_type: '',
    message: '',
  });

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        name: user.name || user.user_metadata?.full_name || '',
        email: user.email || '',
        phone: user.user_metadata?.phone || prev.phone,
      }));
    }
  }, [user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({ ...prev, project_type: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user is authenticated
    if (!isAuthenticated) {
      setShowAuthDialog(true);
      return;
    }

    // Basic validation
    if (!formData.name.trim() || !formData.email.trim()) {
      toast({
        title: 'Missing information',
        description: 'Please provide your name and email address.',
        variant: 'destructive',
      });
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast({
        title: 'Invalid email',
        description: 'Please provide a valid email address.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const requestData = {
        ...formData,
        user_id: user?.id, // Link to user if logged in
      };

      const result = await submitConsultationRequest(requestData);
      
      if (result) {
        // Reset form
        setFormData({
          name: user?.name || user?.user_metadata?.full_name || '',
          email: user?.email || '',
          phone: user?.user_metadata?.phone || '',
          project_type: '',
          message: '',
        });
        
        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      }
    } catch (error) {
      console.error('Error submitting consultation request:', error);
      toast({
        title: 'Submission failed',
        description: 'There was an error submitting your request. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div>
          <Label htmlFor="name" className="text-white/80">Name</Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Your name"
            className="bg-white/20 border-white/20 text-white placeholder:text-white/60"
            required
          />
        </div>
        <div>
          <Label htmlFor="email" className="text-white/80">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Your email"
            className="bg-white/20 border-white/20 text-white placeholder:text-white/60"
            required
          />
        </div>
      </div>

      <div>
        <Label htmlFor="phone" className="text-white/80">Phone</Label>
        <Input
          id="phone"
          name="phone"
          value={formData.phone}
          onChange={handleChange}
          placeholder="Your phone number"
          className="bg-white/20 border-white/20 text-white placeholder:text-white/60"
        />
      </div>
      
      <div>
        <Label htmlFor="project_type" className="text-white/80">Project Type</Label>
        <Select
          value={formData.project_type}
          onValueChange={handleSelectChange}
        >
          <SelectTrigger id="project_type" className="w-full bg-white/20 border-white/20 text-white">
            <SelectValue placeholder="Select project type" className="text-white/60" />
          </SelectTrigger>
          <SelectContent className="bg-white border border-gray-200 shadow-lg z-[9999] max-h-60 overflow-y-auto">
            <SelectItem value="living_room" className="hover:bg-gray-100 cursor-pointer">Living Room</SelectItem>
            <SelectItem value="bedroom" className="hover:bg-gray-100 cursor-pointer">Bedroom</SelectItem>
            <SelectItem value="kitchen" className="hover:bg-gray-100 cursor-pointer">Kitchen</SelectItem>
            <SelectItem value="bathroom" className="hover:bg-gray-100 cursor-pointer">Bathroom</SelectItem>
            <SelectItem value="office" className="hover:bg-gray-100 cursor-pointer">Home Office</SelectItem>
            <SelectItem value="full_home" className="hover:bg-gray-100 cursor-pointer">Full Home</SelectItem>
            <SelectItem value="other" className="hover:bg-gray-100 cursor-pointer">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label htmlFor="message" className="text-white/80">Message</Label>
        <Textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
          placeholder="Briefly describe your project"
          className="bg-white/20 border-white/20 text-white placeholder:text-white/60"
          rows={4}
        />
      </div>
      
      <Button 
        type="submit" 
        className="w-full bg-badhees-accent hover:bg-badhees-accent/90"
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Submitting...
          </>
        ) : (
          'Submit Request'
        )}
      </Button>

      {/* Authentication Dialog */}
      <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Authentication Required
            </DialogTitle>
            <DialogDescription>
              You need to be signed in to submit a consultation request. This helps us provide you with personalized service and keep track of your requests.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="flex gap-2 sm:gap-0">
            <Button
              variant="outline"
              onClick={() => setShowAuthDialog(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                setShowAuthDialog(false);
                navigate('/login?redirect=' + encodeURIComponent(window.location.pathname));
              }}
              className="flex-1 bg-badhees-accent hover:bg-badhees-accent/90"
            >
              Sign In
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </form>
  );
};

export default ConsultationRequestForm;
