import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { FrontendProduct } from '@/services/product/types';
import { formatPrice } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { ArrowRight, Search } from 'lucide-react';
import { getRelatedProducts } from '@/services/search';
import { cn } from '@/lib/utils';

export interface SearchResultsProps {
  /**
   * Search results to display
   */
  results: FrontendProduct[];

  /**
   * Current search query
   */
  query: string;

  /**
   * Whether the search is currently loading
   */
  isLoading: boolean;

  /**
   * Callback when a result is selected
   */
  onResultSelect?: (product: FrontendProduct) => void;

  /**
   * Callback when "View all results" is clicked
   */
  onViewAllResults?: () => void;

  /**
   * Maximum number of results to display
   */
  maxResults?: number;

  /**
   * Additional CSS classes
   */
  className?: string;

  /**
   * Whether to show the "View all results" button
   */
  showViewAll?: boolean;
}

/**
 * A component to display search results
 */
export const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  query,
  isLoading,
  onResultSelect,
  onViewAllResults,
  maxResults = 5,
  className = "",
  showViewAll = true,
}) => {
  const [relatedProducts, setRelatedProducts] = useState<FrontendProduct[]>([]);
  const [isLoadingRelated, setIsLoadingRelated] = useState(false);

  // Limit the number of results to display
  const displayedResults = results.slice(0, maxResults);
  const hasMoreResults = results.length > maxResults;

  // Load related products when no search results are found
  useEffect(() => {
    if (displayedResults.length === 0 && query.trim() && !isLoading) {
      const loadRelatedProducts = async () => {
        setIsLoadingRelated(true);
        try {
          const related = await getRelatedProducts(6);
          setRelatedProducts(related);
        } catch (error) {
          console.error('Error loading related products:', error);
        } finally {
          setIsLoadingRelated(false);
        }
      };

      loadRelatedProducts();
    }
  }, [displayedResults.length, query, isLoading]);

  // Handle result click
  const handleResultClick = (product: FrontendProduct) => {
    if (onResultSelect) {
      onResultSelect(product);
    }
  };

  // Handle "View all results" click
  const handleViewAllClick = () => {
    if (onViewAllResults) {
      onViewAllResults();
    }
  };

  // If loading, show loading state
  if (isLoading) {
    return (
      <div className={cn("p-4 bg-white shadow-md rounded-md", className)}>
        <div className="flex items-center justify-center py-8">
          <div className="animate-pulse flex flex-col space-y-4 w-full">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-badhees-100 rounded"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-badhees-100 rounded w-3/4"></div>
                  <div className="h-4 bg-badhees-100 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // If no results and query is not empty, show no results message with related products
  if (displayedResults.length === 0 && query.trim()) {
    return (
      <div className={cn("bg-white shadow-md rounded-md", className)}>
        <div className="p-4">
          <div className="flex flex-col items-center justify-center py-6 text-center">
            <Search className="h-12 w-12 text-badhees-200 mb-4" />
            <h3 className="text-lg font-medium text-badhees-800 mb-2">No results found</h3>
            <p className="text-badhees-500 mb-4">
              Sorry, we couldn't find any products matching "{query}"
            </p>
            <div className="text-sm text-badhees-500 mb-4">
              Try using different keywords or check for spelling mistakes
            </div>
          </div>

          {/* Show related products */}
          {isLoadingRelated ? (
            <div className="border-t border-badhees-100 pt-4">
              <h4 className="text-sm font-medium text-badhees-800 mb-3">You might also like:</h4>
              <div className="grid grid-cols-2 gap-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="animate-pulse">
                    <div className="w-full h-24 bg-badhees-100 rounded mb-2"></div>
                    <div className="h-3 bg-badhees-100 rounded mb-1"></div>
                    <div className="h-3 bg-badhees-100 rounded w-2/3"></div>
                  </div>
                ))}
              </div>
            </div>
          ) : relatedProducts.length > 0 ? (
            <div className="border-t border-badhees-100 pt-4">
              <h4 className="text-sm font-medium text-badhees-800 mb-3">You might also like:</h4>
              <div className="grid grid-cols-2 gap-3">
                {relatedProducts.slice(0, 4).map((product) => (
                  <Link
                    key={product.id}
                    to={`/products/${product.id}`}
                    className="group block"
                    onClick={() => handleResultClick(product)}
                  >
                    <div className="w-full h-24 bg-badhees-100 rounded overflow-hidden mb-2">
                      {product.image ? (
                        <img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                          loading="lazy"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-badhees-400">
                          <span className="text-xs">No image</span>
                        </div>
                      )}
                    </div>
                    <h5 className="text-xs font-medium text-badhees-800 line-clamp-1 group-hover:text-badhees-accent">
                      {product.name}
                    </h5>
                    <p className="text-xs text-badhees-600 font-medium">
                      {formatPrice(product.isSale && product.salePrice ? product.salePrice : product.price)}
                    </p>
                  </Link>
                ))}
              </div>
              {relatedProducts.length > 4 && (
                <div className="mt-4 text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewAllResults && onViewAllResults()}
                    className="text-badhees-accent hover:text-badhees-accent-hover"
                  >
                    View more products
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          ) : null}
        </div>
      </div>
    );
  }

  // If no query, don't show anything
  if (!query.trim()) {
    return null;
  }

  // Otherwise, show results
  return (
    <div className={cn("bg-white shadow-md rounded-md overflow-hidden", className)}>
      <div className="p-2">
        {displayedResults.map((product) => (
          <Link
            key={product.id}
            to={`/products/${product.id}`}
            className="flex items-center p-2 hover:bg-badhees-50 rounded-md transition-colors"
            onClick={() => handleResultClick(product)}
          >
            <div className="w-16 h-16 bg-badhees-100 rounded overflow-hidden flex-shrink-0">
              {product.image ? (
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                  loading="lazy"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    target.nextElementSibling?.classList.remove('hidden');
                  }}
                />
              ) : null}
              <div className={`w-full h-full flex items-center justify-center bg-badhees-100 text-badhees-400 ${product.image ? 'hidden' : ''}`}>
                <span className="text-xs">No image</span>
              </div>
            </div>
            <div className="ml-4 flex-1">
              <h4 className="text-sm font-medium text-badhees-800 line-clamp-1">{product.name}</h4>
              <p className="text-xs text-badhees-500 line-clamp-1">{product.category}</p>
              <div className="flex items-center mt-1">
                <span className="text-sm font-medium text-badhees-800">
                  {formatPrice(product.price)}
                </span>
                {product.isSale && product.salePrice && (
                  <span className="ml-2 text-xs line-through text-badhees-400">
                    {formatPrice(product.price)}
                  </span>
                )}
              </div>
            </div>
          </Link>
        ))}
      </div>

      {showViewAll && query.trim() && (hasMoreResults || results.length > 0) && (
        <div className="p-2 border-t border-badhees-100">
          <Button
            variant="ghost"
            className="w-full justify-center py-2 text-badhees-accent hover:text-badhees-accent-hover hover:bg-badhees-50"
            onClick={handleViewAllClick}
          >
            <span>View all results</span>
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
};
