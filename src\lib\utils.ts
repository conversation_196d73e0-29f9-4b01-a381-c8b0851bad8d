import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formats a number as Indian Rupees (₹)
 * @param amount - The amount to format
 * @param options - Formatting options
 * @returns Formatted currency string
 */
export function formatPrice(
  amount: number,
  options: {
    decimals?: number;
    showSymbol?: boolean;
    showCode?: boolean;
  } = {}
): string {
  const {
    decimals = 0,
    showSymbol = true,
    showCode = false,
  } = options;

  // Format the number with Indian thousands separator and decimal places
  const formattedAmount = new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(amount);

  // Build the final string
  let result = '';

  if (showSymbol) {
    result += '₹';
  }

  result += formattedAmount;

  if (showCode) {
    result += ' INR';
  }

  return result;
}
