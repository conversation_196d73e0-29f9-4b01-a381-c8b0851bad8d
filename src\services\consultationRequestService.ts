import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface ConsultationRequest {
  id: string;
  name: string;
  email: string;
  phone?: string;
  project_type?: string;
  message?: string;
  status: 'pending' | 'contacted' | 'scheduled' | 'completed' | 'cancelled' | 'on_hold';
  created_at: string;
  updated_at?: string;
  user_id?: string;
}

export interface ConsultationRequestInput {
  name: string;
  email: string;
  phone?: string;
  project_type?: string;
  message?: string;
  user_id?: string;
}

/**
 * Submit a new consultation request
 * @param requestData The consultation request data
 * @returns The created request or null if there was an error
 */
export const submitConsultationRequest = async (
  requestData: ConsultationRequestInput
): Promise<ConsultationRequest | null> => {
  try {
    const { data, error } = await supabase
      .from('consultation_requests')
      .insert(requestData)
      .select()
      .single();

    if (error) {
      console.error('Error submitting consultation request:', error);
      toast({
        title: 'Error submitting request',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    toast({
      title: 'Request submitted',
      description: 'Your consultation request has been submitted successfully.',
    });

    return data as ConsultationRequest;
  } catch (error) {
    console.error('Error in submitConsultationRequest:', error);
    return null;
  }
};

/**
 * Get all consultation requests (admin only)
 * @param status Optional status filter
 * @returns Array of consultation requests
 */
export const getConsultationRequests = async (status?: string): Promise<ConsultationRequest[]> => {
  try {
    let query = supabase
      .from('consultation_requests')
      .select('*');

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching consultation requests:', error);
      toast({
        title: 'Error fetching requests',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return [];
    }

    return data as ConsultationRequest[];
  } catch (error) {
    console.error('Error in getConsultationRequests:', error);
    return [];
  }
};

/**
 * Update a consultation request status
 * @param requestId The request ID
 * @param status The new status
 * @returns True if successful, false otherwise
 */
export const updateConsultationRequestStatus = async (
  requestId: string,
  status: ConsultationRequest['status']
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('consultation_requests')
      .update({ status })
      .eq('id', requestId);

    if (error) {
      console.error('Error updating consultation request status:', error);
      toast({
        title: 'Error updating status',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Status updated',
      description: `Request status has been updated to ${status}.`,
    });

    return true;
  } catch (error) {
    console.error('Error in updateConsultationRequestStatus:', error);
    return false;
  }
};

/**
 * Get a single consultation request by ID
 * @param requestId The request ID
 * @returns The consultation request or null if not found
 */
export const getConsultationRequestById = async (
  requestId: string
): Promise<ConsultationRequest | null> => {
  try {
    const { data, error } = await supabase
      .from('consultation_requests')
      .select('*')
      .eq('id', requestId)
      .single();

    if (error) {
      console.error('Error fetching consultation request:', error);
      return null;
    }

    return data as ConsultationRequest;
  } catch (error) {
    console.error('Error in getConsultationRequestById:', error);
    return null;
  }
};
