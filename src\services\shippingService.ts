/**
 * Shipping Service
 * 
 * This module provides functions to calculate shipping fees and handle shipping logic.
 */
import { supabase } from '@/lib/supabase';

// Import the CartItem from the cart context
import { CartItem as ContextCartItem } from '@/context/SupabaseCartContext';

export interface CartItem {
  id?: string;
  product_id?: string;
  quantity: number;
  price?: number;
  product?: {
    id?: string;
    name?: string;
    price?: number;
    salePrice?: number;
    shippingFeeBangalore?: number;
    shippingFeeOutsideBangalore?: number;
    freeShippingThreshold?: number;
    shippingNotes?: string;
  };
}

/**
 * Convert cart context items to shipping service format
 */
export const convertCartItemsForShipping = (contextCartItems: ContextCartItem[]): CartItem[] => {
  return contextCartItems.map(item => ({
    id: item.product.id,
    product_id: item.product.id,
    quantity: item.quantity,
    price: item.product.salePrice || item.product.price,
    product: {
      id: item.product.id,
      name: item.product.name,
      price: item.product.price,
      salePrice: item.product.salePrice,
    }
  }));
};

export interface ShippingCalculation {
  shippingFee: number;
  isFreeShipping: boolean;
  isManualCalculation: boolean;
  notes: string[];
  eligibleForFreeShipping: boolean;
  freeShippingThreshold?: number;
}

/**
 * Calculate shipping fee for cart items
 * @param cartItems Array of cart items
 * @param isBangaloreDelivery Whether delivery is within Bangalore
 * @returns Shipping calculation details
 */
export const calculateShippingFee = async (
  cartItems: CartItem[],
  isBangaloreDelivery: boolean | null = null
): Promise<ShippingCalculation> => {
  try {
    // If delivery location is not specified (outside Bangalore), return manual calculation
    if (isBangaloreDelivery === null || isBangaloreDelivery === false) {
      return {
        shippingFee: 0,
        isFreeShipping: false,
        isManualCalculation: true,
        notes: ['Our team will contact you for shipping calculation based on distance.'],
        eligibleForFreeShipping: false,
      };
    }

    let totalShippingFee = 0;
    let subtotal = 0;
    let maxFreeShippingThreshold = 0;
    const notes: string[] = [];
    let hasAnyFreeShippingEligible = false;

    // Get product shipping information for all items
    const productIds = cartItems.map(item => item.product_id || item.product?.id || item.id).filter(Boolean);

    console.log('🚚 [calculateShippingFee] Cart items structure:', cartItems);
    console.log('🚚 [calculateShippingFee] Extracted product IDs:', productIds);

    const { data: products, error } = await supabase
      .from('products')
      .select('id, shipping_fee_bangalore, shipping_fee_outside_bangalore, free_shipping_threshold, shipping_notes')
      .in('id', productIds);

    if (error) {
      console.error('❌ [calculateShippingFee] Error fetching product shipping info:', error);
      console.error('❌ [calculateShippingFee] Error details:', {
        message: error.message,
        code: error.code,
        hint: error.hint,
        details: error.details
      });

      // Fallback to default shipping
      return {
        shippingFee: 50,
        isFreeShipping: false,
        isManualCalculation: false,
        notes: ['Error fetching shipping info. Standard shipping fee applied.'],
        eligibleForFreeShipping: false,
      };
    }

    console.log('✅ [calculateShippingFee] Fetched shipping info for', products?.length || 0, 'products:', products);

    // Create a map for quick lookup
    const productShippingMap = new Map();
    products?.forEach(product => {
      productShippingMap.set(product.id, product);
    });

    // Calculate shipping for each item
    for (const item of cartItems) {
      const productId = item.product_id || item.product?.id || item.id;
      const productShipping = productShippingMap.get(productId);

      console.log(`🔍 [calculateShippingFee] Processing item:`, {
        productId,
        quantity: item.quantity,
        price: item.price || item.product?.price,
        foundShipping: !!productShipping,
        shippingData: productShipping
      });

      if (productShipping) {
        // Calculate item total (price * quantity)
        const itemPrice = item.price || item.product?.salePrice || item.product?.price || 0;
        const itemTotal = itemPrice * item.quantity;
        subtotal += itemTotal;

        // Add shipping fee (per product, not per quantity)
        const shippingFee = productShipping.shipping_fee_bangalore || 50;
        totalShippingFee += shippingFee;

        console.log(`✅ [calculateShippingFee] Applied shipping fee ₹${shippingFee} for product ${productId}`);

        // Track the highest free shipping threshold
        if (productShipping.free_shipping_threshold) {
          maxFreeShippingThreshold = Math.max(
            maxFreeShippingThreshold,
            productShipping.free_shipping_threshold
          );
          hasAnyFreeShippingEligible = true;
        }

        // Add product-specific shipping notes
        if (productShipping.shipping_notes) {
          notes.push(productShipping.shipping_notes);
        }
      } else {
        console.warn(`⚠️ [calculateShippingFee] No shipping data found for product ${productId}, using fallback`);
        // Fallback shipping fee
        totalShippingFee += 50;
        const itemPrice = item.price || item.product?.salePrice || item.product?.price || 0;
        subtotal += itemPrice * item.quantity;
      }
    }

    // Check if order qualifies for free shipping
    const qualifiesForFreeShipping = hasAnyFreeShippingEligible && 
                                   subtotal >= maxFreeShippingThreshold;

    if (qualifiesForFreeShipping) {
      return {
        shippingFee: 0,
        isFreeShipping: true,
        isManualCalculation: false,
        notes: [`Free shipping applied! Order total ₹${subtotal.toFixed(2)} exceeds threshold of ₹${maxFreeShippingThreshold.toFixed(2)}`],
        eligibleForFreeShipping: true,
        freeShippingThreshold: maxFreeShippingThreshold,
      };
    }

    return {
      shippingFee: totalShippingFee,
      isFreeShipping: false,
      isManualCalculation: false,
      notes: notes.length > 0 ? notes : ['Standard shipping fees applied.'],
      eligibleForFreeShipping: hasAnyFreeShippingEligible,
      freeShippingThreshold: maxFreeShippingThreshold > 0 ? maxFreeShippingThreshold : undefined,
    };

  } catch (error) {
    console.error('Error calculating shipping fee:', error);
    // Fallback to default shipping
    return {
      shippingFee: isBangaloreDelivery ? 50 : 0,
      isFreeShipping: false,
      isManualCalculation: !isBangaloreDelivery,
      notes: isBangaloreDelivery 
        ? ['Standard shipping fee applied.'] 
        : ['Our team will contact you for shipping calculation.'],
      eligibleForFreeShipping: false,
    };
  }
};

/**
 * Get shipping information for a specific product
 * @param productId Product ID
 * @returns Product shipping information
 */
export const getProductShippingInfo = async (productId: string) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('shipping_fee_bangalore, shipping_fee_outside_bangalore, free_shipping_threshold, shipping_notes')
      .eq('id', productId)
      .single();

    if (error) {
      console.error('Error fetching product shipping info:', error);
      return null;
    }

    return {
      shippingFeeBangalore: data.shipping_fee_bangalore || 50,
      shippingFeeOutsideBangalore: data.shipping_fee_outside_bangalore || 0,
      freeShippingThreshold: data.free_shipping_threshold || 2000,
      shippingNotes: data.shipping_notes || 'Standard shipping terms apply.',
    };
  } catch (error) {
    console.error('Error in getProductShippingInfo:', error);
    return null;
  }
};

/**
 * Format shipping fee for display
 * @param shippingCalculation Shipping calculation result
 * @returns Formatted shipping fee string
 */
export const formatShippingFee = (shippingCalculation: ShippingCalculation): string => {
  if (shippingCalculation.isFreeShipping) {
    return 'Free';
  }
  
  if (shippingCalculation.isManualCalculation) {
    return 'TBD';
  }
  
  return `₹${shippingCalculation.shippingFee.toFixed(2)}`;
};

/**
 * Get shipping message for display
 * @param shippingCalculation Shipping calculation result
 * @returns Shipping message
 */
export const getShippingMessage = (shippingCalculation: ShippingCalculation): string => {
  if (shippingCalculation.isFreeShipping) {
    return 'Free shipping applied!';
  }
  
  if (shippingCalculation.isManualCalculation) {
    return 'Shipping fee will be calculated based on distance. Our team will contact you.';
  }
  
  if (shippingCalculation.eligibleForFreeShipping && shippingCalculation.freeShippingThreshold) {
    const remaining = shippingCalculation.freeShippingThreshold - 
      (shippingCalculation.shippingFee > 0 ? 0 : shippingCalculation.freeShippingThreshold);
    if (remaining > 0) {
      return `Add ₹${remaining.toFixed(2)} more for free shipping!`;
    }
  }
  
  return 'Standard shipping fees applied.';
};
