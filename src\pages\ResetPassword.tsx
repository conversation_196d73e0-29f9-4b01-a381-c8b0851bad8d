import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import Navbar from '@/components/layout/Navbar';
import Footer from '@/components/layout/Footer';
import { AlertCircle, ArrowLeft, Eye, EyeOff, CheckCircle, Home, LogIn } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [countdown, setCountdown] = useState<number>(3);
  const [userEmail, setUserEmail] = useState<string>('');
  const navigate = useNavigate();

  // Check if we have a session or hash fragment when the component mounts
  useEffect(() => {
    const checkSessionAndHash = async () => {
      // Check if we have a hash fragment in the URL (from the reset password email)
      const hash = window.location.hash;

      if (hash) {
        console.log('Hash fragment detected:', hash);
        // The hash contains the access token and type
        // Supabase will automatically handle this
      }

      // Check if we have a session
      const { data } = await supabase.auth.getSession();
      console.log('Session check:', data.session ? 'Session found' : 'No session');

      // If no session and no hash, redirect to forgot password page
      if (!data.session && !hash) {
        console.log('No session and no hash, redirecting to forgot-password');
        navigate('/forgot-password');
      }
    };

    checkSessionAndHash();
  }, [navigate]);

  const validateForm = (): boolean => {
    if (!password) {
      setError('Password is required');
      return false;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return false;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('Attempting to update password');

      // Update the user's password
      const { data, error } = await supabase.auth.updateUser({
        password,
      });

      if (error) {
        console.error('Supabase error:', error);
        throw error;
      }

      console.log('Password updated successfully:', data);
      setUserEmail(data.user?.email || '');

      // Show success message
      setIsSuccess(true);

      // Show success toast
      toast({
        title: "Password Reset Successful!",
        description: "Your password has been updated successfully.",
        duration: 5000,
      });

      // Start countdown for auto-redirect
      let countdownTimer = 3;
      const countdownInterval = setInterval(() => {
        setCountdown(countdownTimer);
        countdownTimer--;

        if (countdownTimer < 0) {
          clearInterval(countdownInterval);
          navigate('/login', {
            state: {
              message: 'Password reset successful! Please sign in with your new password.',
              email: data.user?.email
            }
          });
        }
      }, 1000);
    } catch (error: any) {
      console.error('Password reset error:', error);
      setError(error.message || 'An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 bg-badhees-50">
        <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-md">
          <div>
            <Link to="/login" className="inline-flex items-center text-sm text-badhees-600 hover:text-badhees-800 mb-6">
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back to login
            </Link>

            <h2 className="mt-2 text-center text-3xl font-extrabold text-badhees-800">
              Create new password
            </h2>
            <p className="mt-2 text-center text-sm text-badhees-600">
              Your new password must be at least 6 characters long.
            </p>
          </div>

          {isSuccess ? (
            <div className="flex flex-col items-center">
              <div className="relative mb-4">
                <CheckCircle className="h-16 w-16 text-green-500 animate-pulse" />
                <div className="absolute -top-2 -right-2 text-2xl">🎉</div>
              </div>
              <p className="text-green-700 font-bold text-lg mb-2">
                Password Reset Successful!
              </p>
              <p className="text-sm text-badhees-600 mb-4 text-center">
                🔐 Your password has been updated successfully! You can now sign in with your new password.
              </p>
              {userEmail && (
                <p className="text-xs text-badhees-500 mb-4">
                  Account: <span className="font-medium">{userEmail}</span>
                </p>
              )}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 w-full">
                <p className="text-sm text-green-700 text-center">
                  Redirecting to login in <span className="font-bold text-green-800">{countdown}</span> seconds...
                </p>
                <div className="mt-2 w-full bg-green-200 rounded-full h-2">
                  <div
                    className={`bg-green-500 h-2 rounded-full transition-all duration-1000 ${
                      countdown === 3 ? 'w-0' :
                      countdown === 2 ? 'w-1/3' :
                      countdown === 1 ? 'w-2/3' :
                      'w-full'
                    }`}
                  ></div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3 mt-6 w-full">
                <button
                  type="button"
                  onClick={() => navigate('/login', { state: { email: userEmail } })}
                  className="w-full bg-badhees-800 text-white py-3 rounded-md font-medium hover:bg-badhees-700 transition-colors flex items-center justify-center gap-2"
                >
                  <LogIn className="h-4 w-4" />
                  Continue to Sign In
                </button>
                <button
                  type="button"
                  onClick={() => navigate('/')}
                  className="w-full bg-gray-100 text-badhees-800 py-3 rounded-md font-medium hover:bg-gray-200 transition-colors flex items-center justify-center gap-2"
                >
                  <Home className="h-4 w-4" />
                  Go to Homepage
                </button>
              </div>
            </div>
          ) : (
            <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
              {error && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-md flex items-start">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              )}

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-badhees-700 mb-1">
                  New Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => {
                      setPassword(e.target.value);
                      if (error) setError(null);
                    }}
                    placeholder="Enter new password"
                    required
                    className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-badhees-500"
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-badhees-700 mb-1">
                  Confirm New Password
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirmPassword}
                    onChange={(e) => {
                      setConfirmPassword(e.target.value);
                      if (error) setError(null);
                    }}
                    placeholder="Confirm new password"
                    required
                    className="w-full px-4 py-3 rounded-md border border-badhees-200 focus:outline-none focus:ring-1 focus:ring-badhees-accent"
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-badhees-500"
                    aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                  >
                    {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-badhees-accent hover:bg-badhees-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-badhees-accent disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Resetting...' : 'Reset Password'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default ResetPassword;
