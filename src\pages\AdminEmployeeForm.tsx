import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ArrowLeft, Loader2 } from 'lucide-react';
import { format, parseISO } from 'date-fns';
import { useAuth } from '@/context/SupabaseAuthContext';
import {
  useEmployees,
  useEmployee,
  useCreateEmployee,
  useUpdateEmployee
} from '@/hooks/useEmployeeManagement';
import { Employee } from '@/services/employee/types';

const AdminEmployeeForm = () => {
  const { employeeId } = useParams<{ employeeId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin, user } = useAuth();
  const isEditMode = !!employeeId;

  // Fetch data
  const { data: employees } = useEmployees();
  const { data: employeeData, isLoading: isLoadingEmployee } = useEmployee(employeeId || '');
  const createEmployeeMutation = useCreateEmployee();
  const updateEmployeeMutation = useUpdateEmployee();

  // Form state
  const [formData, setFormData] = useState<Partial<Employee>>({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    position: '',
    department: '',
    manager_id: 'none', // Use 'none' as default value for the Select component
    hire_date: format(new Date(), 'yyyy-MM-dd'),
    base_salary: 0,
    status: 'active',
  });

  // Check authentication and admin status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/employees' + (isEditMode ? `/${employeeId}` : '/new'));
    } else if (!isAdmin()) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate, employeeId, isEditMode]);

  // Populate form with employee data if in edit mode
  useEffect(() => {
    if (isEditMode && employeeData) {
      setFormData({
        first_name: employeeData.first_name,
        last_name: employeeData.last_name,
        email: employeeData.email || '',
        phone: employeeData.phone || '',
        position: employeeData.position,
        department: employeeData.department || '',
        manager_id: employeeData.manager_id || 'none',
        hire_date: employeeData.hire_date,
        base_salary: employeeData.base_salary,
        status: employeeData.status,
      });
    }
  }, [isEditMode, employeeData]);

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'base_salary' ? parseFloat(value) : value
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Create a copy of the form data to process
      const processedFormData = { ...formData };

      // Handle manager_id field - convert 'none' to null (not empty string)
      // This prevents the "invalid input syntax for type uuid" error
      if (processedFormData.manager_id === 'none' || processedFormData.manager_id === '') {
        // Remove the property entirely instead of setting to empty string
        delete processedFormData.manager_id;
      }

      if (isEditMode && employeeId) {
        await updateEmployeeMutation.mutateAsync({
          id: employeeId,
          employee: processedFormData as Partial<Omit<Employee, 'id' | 'created_at' | 'updated_at'>>
        });
      } else {
        // Add created_by field for new employees
        const employeeWithCreator = {
          ...processedFormData,
          created_by: user?.id
        } as Omit<Employee, 'id' | 'created_at' | 'updated_at'>;

        await createEmployeeMutation.mutateAsync(employeeWithCreator);
      }

      navigate('/admin/employees');
    } catch (error) {
      console.error('Error saving employee:', error);
    }
  };

  // Loading state
  if (isEditMode && isLoadingEmployee) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-1 pt-28 pb-16 flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
          <span className="ml-2">Loading employee data...</span>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-6">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin/employees')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Employees
              </Button>
              <h1 className="text-2xl font-bold">
                {isEditMode ? 'Edit Employee' : 'Add New Employee'}
              </h1>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>{isEditMode ? 'Edit Employee Details' : 'New Employee Details'}</CardTitle>
                <CardDescription>
                  {isEditMode
                    ? 'Update the employee information below.'
                    : 'Fill in the employee information to add them to the system.'}
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleSubmit}>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Personal Information */}
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="first_name">First Name</Label>
                        <Input
                          id="first_name"
                          name="first_name"
                          value={formData.first_name}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="last_name">Last Name</Label>
                        <Input
                          id="last_name"
                          name="last_name"
                          value={formData.last_name}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Phone</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                        />
                      </div>
                    </div>

                    {/* Employment Information */}
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="position">Position</Label>
                        <Input
                          id="position"
                          name="position"
                          value={formData.position}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="department">Department</Label>
                        <Input
                          id="department"
                          name="department"
                          value={formData.department}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div>
                        <Label htmlFor="manager_id">Manager</Label>
                        <Select
                          value={formData.manager_id || 'none'}
                          onValueChange={(value) => handleSelectChange('manager_id', value === 'none' ? '' : value)}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a manager" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            {employees?.filter(emp => emp.id !== employeeId).map(employee => (
                              <SelectItem key={employee.id} value={employee.id}>
                                {employee.first_name} {employee.last_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="hire_date">Hire Date</Label>
                        <Input
                          id="hire_date"
                          name="hire_date"
                          type="date"
                          value={formData.hire_date}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  {/* Additional Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="base_salary">Base Salary (per day)</Label>
                      <Input
                        id="base_salary"
                        name="base_salary"
                        type="number"
                        min="0"
                        step="0.01"
                        value={formData.base_salary}
                        onChange={handleInputChange}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="status">Status</Label>
                      <Select
                        value={formData.status || 'active'}
                        onValueChange={(value) => handleSelectChange('status', value as 'active' | 'inactive')}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/admin/employees')}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={createEmployeeMutation.isPending || updateEmployeeMutation.isPending}
                  >
                    {(createEmployeeMutation.isPending || updateEmployeeMutation.isPending) && (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    )}
                    {isEditMode ? 'Update Employee' : 'Add Employee'}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminEmployeeForm;
