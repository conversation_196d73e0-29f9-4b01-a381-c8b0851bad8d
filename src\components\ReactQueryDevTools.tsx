import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

/**
 * React Query DevTools component
 * 
 * This component renders the React Query DevTools in development mode only.
 * It's conditionally imported to avoid including it in the production bundle.
 */
export function TanStackDevTools() {
  // Only render in development mode
  if (import.meta.env.DEV) {
    return (
      <ReactQueryDevtools 
        initialIsOpen={false} 
        position="bottom-right"
      />
    );
  }
  
  // Return null in production
  return null;
}
