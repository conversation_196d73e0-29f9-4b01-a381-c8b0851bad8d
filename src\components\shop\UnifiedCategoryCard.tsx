import React from 'react';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import '@/styles/card.css';

interface CategoryData {
  id: string;
  name: string;
  description?: string;
  image: string;
  itemCount?: number;
  subcategories?: string[];
}

interface UnifiedCategoryCardProps {
  category: CategoryData;
  index: number;
}

const UnifiedCategoryCard = ({ category, index }: UnifiedCategoryCardProps) => {
  // Special handling for certain categories
  let categoryPath = `/products?category=${category.name}`;

  // Custom & Specialty category redirects to custom interiors
  if (category.name === "Custom & Specialty" || category.name === "Custom & Specialty Furniture") {
    categoryPath = "/custom-interiors";
  }

  // Default description if none provided
  const description = category.description ||
    `Explore our collection of high-quality ${category.name.toLowerCase()} pieces, designed for style and comfort.`;

  // Calculate animation delay class based on index
  const getAnimationDelayClass = (idx: number) => {
    const delay = Math.min(idx * 50, 450);
    return `animation-delay-${delay}`;
  };

  return (
    <div className={`unified-category-card animate-fade-in ${getAnimationDelayClass(index)}`}>
      {/* Image container with consistent aspect ratio */}
      <div className="unified-image-container">
        <img
          src={category.image}
          alt={category.name}
          className="unified-image"
          loading="lazy"
        />
        <div className="unified-overlay"></div>
        <div className="unified-title-container">
          <h2 className="unified-title">
            {category.name}
          </h2>
          {category.itemCount !== undefined && (
            <p className="text-white/90 text-xs sm:text-sm">
              {category.itemCount} Products
            </p>
          )}
        </div>
      </div>

      {/* Content area with consistent height */}
      <div className="unified-content">
        <p className="unified-description">
          {description}
        </p>

        <div className="unified-button-container">
          <Button
            asChild
            variant="outline"
            className="w-full border-badhees-800 text-badhees-800 hover:bg-badhees-800 hover:text-white min-h-[44px] text-sm sm:text-base"
          >
            <Link to={categoryPath} className="flex items-center justify-center">
              <span>Shop Now</span>
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default UnifiedCategoryCard;
