import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Calendar, Loader2, Save } from 'lucide-react';
import { format, parseISO, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay } from 'date-fns';
import { useAuth } from '@/context/SupabaseAuthContext';
import { 
  useEmployees, 
  useAttendanceByDate, 
  useAttendanceByEmployeeAndMonth,
  useMonthlyAttendanceSummary,
  useMarkAttendance
} from '@/hooks/useEmployeeManagement';
import { Attendance } from '@/services/employee/types';

const AdminAttendance = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { isAuthenticated, isAdmin, user } = useAuth();
  
  // Get employee ID from URL if provided
  const employeeIdFromUrl = searchParams.get('employee');
  
  // State for date selection
  const today = new Date();
  const [selectedDate, setSelectedDate] = useState(format(today, 'yyyy-MM-dd'));
  const [selectedMonth, setSelectedMonth] = useState(format(today, 'yyyy-MM'));
  const [selectedYear, setSelectedYear] = useState(today.getFullYear().toString());
  const [selectedMonthNumber, setSelectedMonthNumber] = useState((today.getMonth() + 1).toString());
  const [activeTab, setActiveTab] = useState('daily');
  const [selectedEmployeeId, setSelectedEmployeeId] = useState(employeeIdFromUrl || '');
  
  // State for attendance records
  const [attendanceRecords, setAttendanceRecords] = useState<Record<string, Omit<Attendance, 'id' | 'created_at' | 'updated_at'>>>({});

  // Fetch data
  const { data: employees, isLoading: isLoadingEmployees } = useEmployees();
  const { data: dailyAttendance, isLoading: isLoadingDailyAttendance } = useAttendanceByDate(selectedDate);
  const { data: employeeMonthlyAttendance, isLoading: isLoadingEmployeeAttendance } = 
    useAttendanceByEmployeeAndMonth(
      selectedEmployeeId, 
      parseInt(selectedYear), 
      parseInt(selectedMonthNumber)
    );
  const { data: monthlySummary, isLoading: isLoadingMonthlySummary } = 
    useMonthlyAttendanceSummary(
      parseInt(selectedYear), 
      parseInt(selectedMonthNumber)
    );
  
  const markAttendanceMutation = useMarkAttendance();

  // Check authentication and admin status
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/employees/attendance');
    } else if (!isAdmin()) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Initialize attendance records when daily attendance data changes
  useEffect(() => {
    if (dailyAttendance) {
      const records: Record<string, Omit<Attendance, 'id' | 'created_at' | 'updated_at'>> = {};
      
      // First, initialize with all employees as 'present'
      employees?.forEach(employee => {
        records[employee.id] = {
          employee_id: employee.id,
          date: selectedDate,
          status: 'present',
          notes: '',
          created_by: user?.id
        };
      });
      
      // Then, update with actual attendance records
      dailyAttendance.forEach(record => {
        records[record.employee_id] = {
          employee_id: record.employee_id,
          date: selectedDate,
          status: record.status,
          notes: record.notes || '',
          created_by: user?.id
        };
      });
      
      setAttendanceRecords(records);
    }
  }, [dailyAttendance, employees, selectedDate, user?.id]);

  // Handle month change
  useEffect(() => {
    const [year, month] = selectedMonth.split('-');
    setSelectedYear(year);
    setSelectedMonthNumber(month);
  }, [selectedMonth]);

  // Handle attendance status change
  const handleStatusChange = (employeeId: string, status: 'present' | 'absent' | 'half-day' | 'leave') => {
    setAttendanceRecords(prev => ({
      ...prev,
      [employeeId]: {
        ...prev[employeeId],
        status
      }
    }));
  };

  // Handle notes change
  const handleNotesChange = (employeeId: string, notes: string) => {
    setAttendanceRecords(prev => ({
      ...prev,
      [employeeId]: {
        ...prev[employeeId],
        notes
      }
    }));
  };

  // Save attendance records
  const handleSaveAttendance = async () => {
    try {
      const records = Object.values(attendanceRecords);
      await markAttendanceMutation.mutateAsync(records);
    } catch (error) {
      console.error('Error saving attendance:', error);
    }
  };

  // Generate calendar days for employee monthly view
  const getDaysInMonth = () => {
    const start = startOfMonth(new Date(parseInt(selectedYear), parseInt(selectedMonthNumber) - 1));
    const end = endOfMonth(start);
    return eachDayOfInterval({ start, end });
  };

  // Get attendance status for a specific day
  const getAttendanceForDay = (date: Date) => {
    if (!employeeMonthlyAttendance) return null;
    
    return employeeMonthlyAttendance.find(record => 
      isSameDay(parseISO(record.date), date)
    );
  };

  // Get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800';
      case 'absent':
        return 'bg-red-100 text-red-800';
      case 'half-day':
        return 'bg-yellow-100 text-yellow-800';
      case 'leave':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-6">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin/employees')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Employees
              </Button>
              <h1 className="text-2xl font-bold">Attendance Management</h1>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="mb-6">
                <TabsTrigger value="daily">Daily Attendance</TabsTrigger>
                <TabsTrigger value="employee">Employee Monthly View</TabsTrigger>
                <TabsTrigger value="summary">Monthly Summary</TabsTrigger>
              </TabsList>

              {/* Daily Attendance Tab */}
              <TabsContent value="daily">
                <Card>
                  <CardHeader>
                    <CardTitle>Mark Daily Attendance</CardTitle>
                    <CardDescription>
                      Record attendance for all employees for a specific date.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="attendance-date">Select Date</Label>
                      <div className="flex items-center gap-2 mt-1.5">
                        <Input
                          id="attendance-date"
                          type="date"
                          value={selectedDate}
                          onChange={(e) => setSelectedDate(e.target.value)}
                          className="w-48"
                        />
                        <Button 
                          variant="outline" 
                          onClick={() => setSelectedDate(format(new Date(), 'yyyy-MM-dd'))}
                          size="sm"
                        >
                          Today
                        </Button>
                      </div>
                    </div>

                    {isLoadingEmployees || isLoadingDailyAttendance ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading attendance data...</span>
                      </div>
                    ) : employees?.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No employees found.</p>
                        <Button 
                          variant="outline" 
                          className="mt-4"
                          onClick={() => navigate('/admin/employees/new')}
                        >
                          Add Employee
                        </Button>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Employee</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Notes</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {employees?.map((employee) => (
                              <TableRow key={employee.id}>
                                <TableCell className="font-medium">
                                  {employee.first_name} {employee.last_name}
                                </TableCell>
                                <TableCell>
                                  <Select
                                    value={attendanceRecords[employee.id]?.status || 'present'}
                                    onValueChange={(value) => handleStatusChange(
                                      employee.id, 
                                      value as 'present' | 'absent' | 'half-day' | 'leave'
                                    )}
                                  >
                                    <SelectTrigger className="w-32">
                                      <SelectValue placeholder="Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="present">Present</SelectItem>
                                      <SelectItem value="absent">Absent</SelectItem>
                                      <SelectItem value="half-day">Half Day</SelectItem>
                                      <SelectItem value="leave">Leave</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </TableCell>
                                <TableCell>
                                  <Input
                                    placeholder="Optional notes"
                                    value={attendanceRecords[employee.id]?.notes || ''}
                                    onChange={(e) => handleNotesChange(employee.id, e.target.value)}
                                  />
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                  <CardFooter className="flex justify-end">
                    <Button 
                      onClick={handleSaveAttendance}
                      disabled={markAttendanceMutation.isPending || isLoadingEmployees || isLoadingDailyAttendance}
                    >
                      {markAttendanceMutation.isPending && (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      )}
                      <Save className="h-4 w-4 mr-2" />
                      Save Attendance
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              {/* Employee Monthly View Tab */}
              <TabsContent value="employee">
                <Card>
                  <CardHeader>
                    <CardTitle>Employee Monthly Attendance</CardTitle>
                    <CardDescription>
                      View attendance records for a specific employee by month.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div>
                        <Label htmlFor="employee-select">Select Employee</Label>
                        <Select
                          value={selectedEmployeeId}
                          onValueChange={setSelectedEmployeeId}
                        >
                          <SelectTrigger id="employee-select">
                            <SelectValue placeholder="Select an employee" />
                          </SelectTrigger>
                          <SelectContent>
                            {employees?.map(employee => (
                              <SelectItem key={employee.id} value={employee.id}>
                                {employee.first_name} {employee.last_name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="month-select">Select Month</Label>
                        <Input
                          id="month-select"
                          type="month"
                          value={selectedMonth}
                          onChange={(e) => setSelectedMonth(e.target.value)}
                        />
                      </div>
                    </div>

                    {!selectedEmployeeId ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">Please select an employee to view attendance.</p>
                      </div>
                    ) : isLoadingEmployeeAttendance ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading attendance data...</span>
                      </div>
                    ) : (
                      <div className="grid grid-cols-7 gap-2">
                        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                          <div key={day} className="text-center font-medium p-2">
                            {day}
                          </div>
                        ))}
                        {getDaysInMonth().map(day => {
                          const attendance = getAttendanceForDay(day);
                          return (
                            <div 
                              key={format(day, 'yyyy-MM-dd')}
                              className={`border rounded-md p-2 text-center ${
                                attendance ? 'border-gray-300' : 'border-gray-200 bg-gray-50'
                              }`}
                            >
                              <div className="font-medium">{format(day, 'd')}</div>
                              {attendance && (
                                <Badge 
                                  variant="outline"
                                  className={getStatusBadgeColor(attendance.status)}
                                >
                                  {attendance.status.charAt(0).toUpperCase() + attendance.status.slice(1)}
                                </Badge>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Monthly Summary Tab */}
              <TabsContent value="summary">
                <Card>
                  <CardHeader>
                    <CardTitle>Monthly Attendance Summary</CardTitle>
                    <CardDescription>
                      Overview of attendance for all employees in a month.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="mb-6">
                      <Label htmlFor="summary-month">Select Month</Label>
                      <Input
                        id="summary-month"
                        type="month"
                        value={selectedMonth}
                        onChange={(e) => setSelectedMonth(e.target.value)}
                        className="w-48"
                      />
                    </div>

                    {isLoadingMonthlySummary ? (
                      <div className="flex justify-center items-center p-8">
                        <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
                        <span className="ml-2 text-badhees-600">Loading summary data...</span>
                      </div>
                    ) : monthlySummary?.length === 0 ? (
                      <div className="text-center py-8">
                        <p className="text-gray-500">No attendance data found for this month.</p>
                      </div>
                    ) : (
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Employee</TableHead>
                              <TableHead>Present Days</TableHead>
                              <TableHead>Absent Days</TableHead>
                              <TableHead>Half Days</TableHead>
                              <TableHead>Leave Days</TableHead>
                              <TableHead>Working %</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {monthlySummary?.map((summary) => (
                              <TableRow key={summary.employee_id}>
                                <TableCell className="font-medium">
                                  {summary.employee_name}
                                </TableCell>
                                <TableCell>{summary.present_days}</TableCell>
                                <TableCell>{summary.absent_days}</TableCell>
                                <TableCell>{summary.half_days}</TableCell>
                                <TableCell>{summary.leave_days}</TableCell>
                                <TableCell>
                                  <Badge 
                                    variant="outline"
                                    className={
                                      summary.working_percentage >= 90 ? 'bg-green-100 text-green-800' :
                                      summary.working_percentage >= 75 ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }
                                  >
                                    {summary.working_percentage}%
                                  </Badge>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminAttendance;
