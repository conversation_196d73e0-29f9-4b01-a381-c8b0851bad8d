/**
 * Centralized Category Constants
 * 
 * This file contains all category-related constants used throughout the application.
 * It ensures consistency between different parts of the app and the database.
 */

// =====================================================
// MAIN PRODUCT CATEGORIES
// =====================================================

export interface CategoryOption {
  value: string;
  label: string;
  description?: string;
}

export interface CategoryData {
  id: string;
  name: string;
  description: string;
  image: string;
}

// Main product categories (matches database categories table)
export const PRODUCT_CATEGORIES: CategoryOption[] = [
  { value: "Living Room", label: "Living Room", description: "Sofas, coffee tables, TV units, and living room furniture" },
  { value: "Bedroom", label: "Bedroom", description: "Beds, wardrobes, nightstands, and bedroom furniture" },
  { value: "Dining Room", label: "Dining Room", description: "Dining tables, chairs, and dining room furniture" },
  { value: "Home Office", label: "Home Office", description: "Desks, office chairs, and home office furniture" },
  { value: "Outdoor", label: "Outdoor", description: "Patio furniture, garden furniture, and outdoor pieces" },
  { value: "Storage", label: "Storage", description: "Wardrobes, cabinets, shelves, and storage solutions" },
  { value: "Kids & Nursery", label: "Kids & Nursery", description: "Children's furniture and nursery items" },
  { value: "Accent Furniture", label: "Accent Furniture", description: "Decorative furniture and accent pieces" },
  { value: "Decorative Accessories", label: "Decorative Accessories", description: "Home decor items and accessories" },
  { value: "Custom & Specialty", label: "Custom & Specialty", description: "Custom-made and specialty furniture pieces" },
];

// =====================================================
// PRODUCT PAGE FILTER CATEGORIES
// =====================================================

// Categories used in the Products page filters (includes "All" and longer names)
export const PRODUCT_FILTER_CATEGORIES = [
  "All",
  "Living Room Furniture",
  "Bedroom Furniture", 
  "Dining Room Furniture",
  "Home Office Furniture",
  "Storage Solutions",
  "Kids & Nursery Furniture",
  "Vibe & Decor",
  "Custom & Specialty Furniture"
];

// =====================================================
// CUSTOM PROJECT CATEGORIES
// =====================================================

// Categories for custom interior projects
export const CUSTOM_PROJECT_CATEGORIES: CategoryOption[] = [
  { value: 'kitchen', label: 'Kitchen Interiors', description: 'Custom kitchen designs and interiors' },
  { value: 'bedroom', label: 'Bedroom Designs', description: 'Custom bedroom interior designs' },
  { value: 'living', label: 'Living Room Interiors', description: 'Custom living room designs' },
  { value: 'office', label: 'Office Furniture & Others', description: 'Office and workspace designs' },
  { value: 'bathroom', label: 'Bathroom Designs', description: 'Custom bathroom interior designs' },
  { value: 'dining', label: 'Dining Room', description: 'Custom dining room designs' },
  { value: 'outdoor', label: 'Outdoor Spaces', description: 'Outdoor and patio designs' },
  { value: 'commercial', label: 'Commercial Spaces', description: 'Commercial interior designs' },
  { value: 'other', label: 'Other', description: 'Other custom interior projects' }
];

// =====================================================
// CONSULTATION PROJECT TYPES
// =====================================================

// Project types for consultation requests
export const CONSULTATION_PROJECT_TYPES: CategoryOption[] = [
  { value: "living_room", label: "Living Room" },
  { value: "bedroom", label: "Bedroom" },
  { value: "kitchen", label: "Kitchen" },
  { value: "bathroom", label: "Bathroom" },
  { value: "office", label: "Home Office" },
  { value: "full_home", label: "Full Home" },
  { value: "other", label: "Other" }
];

// =====================================================
// SHOP PAGE CATEGORIES
// =====================================================

// Categories displayed on the shop page
export const SHOP_CATEGORIES: CategoryData[] = [
  {
    id: "living-room",
    name: "Living Room Furniture",
    description: "Create the perfect living space with our stylish and comfortable living room furniture.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//productcardliving"
  },
  {
    id: "bedroom",
    name: "Bedroom Furniture", 
    description: "Transform your bedroom into a peaceful retreat with our beautiful furniture pieces.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//projectcardbed.jpg"
  },
  {
    id: "dining-room",
    name: "Dining Room Furniture",
    description: "Elegant dining furniture to bring family and friends together.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//productcarddining"
  },
  {
    id: "home-office",
    name: "Home Office Furniture",
    description: "Productive and comfortable furniture for your home workspace.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//productcardoffice"
  },
  {
    id: "storage",
    name: "Storage Solutions",
    description: "Organize your space with our functional and stylish storage furniture.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//productcardstorage"
  },
  {
    id: "kids-nursery",
    name: "Kids & Nursery Furniture",
    description: "Safe, fun, and functional furniture designed specifically for children.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//productcardkids"
  },
  {
    id: "vibe-decor",
    name: "Vibe & Decor",
    description: "Add personality to your space with our decorative furniture and accessories.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//productcardvibe"
  },
  {
    id: "custom-specialty",
    name: "Custom & Specialty Furniture",
    description: "Find unique and personalized furniture pieces that match your specific needs.",
    image: "https://tfvbwveohcbghqmxnpbd.supabase.co/storage/v1/object/public/others//productcardcustom"
  }
];

// =====================================================
// CATEGORY MAPPING FUNCTIONS
// =====================================================

/**
 * Maps product filter categories to database categories
 */
export const mapFilterCategoryToDatabase = (filterCategory: string): string | null => {
  const mapping: Record<string, string> = {
    "Living Room Furniture": "Living Room",
    "Bedroom Furniture": "Bedroom",
    "Dining Room Furniture": "Dining Room", 
    "Home Office Furniture": "Home Office",
    "Storage Solutions": "Storage",
    "Kids & Nursery Furniture": "Kids & Nursery",
    "Vibe & Decor": "Decorative Accessories", // Maps to closest database category
    "Custom & Specialty Furniture": "Custom & Specialty"
  };
  
  return mapping[filterCategory] || null;
};

/**
 * Maps database categories to product filter categories
 */
export const mapDatabaseCategoryToFilter = (dbCategory: string): string => {
  const mapping: Record<string, string> = {
    "Living Room": "Living Room Furniture",
    "Bedroom": "Bedroom Furniture", 
    "Dining Room": "Dining Room Furniture",
    "Home Office": "Home Office Furniture",
    "Storage": "Storage Solutions",
    "Kids & Nursery": "Kids & Nursery Furniture",
    "Outdoor": "Vibe & Decor", // Maps outdoor to vibe & decor for filtering
    "Accent Furniture": "Vibe & Decor",
    "Decorative Accessories": "Vibe & Decor",
    "Custom & Specialty": "Custom & Specialty Furniture"
  };
  
  return mapping[dbCategory] || dbCategory;
};

/**
 * Maps custom project categories to database categories
 */
export const mapCustomProjectCategoryToDatabase = (projectCategory: string): string => {
  const mapping: Record<string, string> = {
    "kitchen": "Custom & Specialty",
    "bedroom": "Bedroom",
    "living": "Living Room", 
    "office": "Home Office",
    "bathroom": "Custom & Specialty",
    "dining": "Dining Room",
    "outdoor": "Outdoor",
    "commercial": "Custom & Specialty",
    "other": "Custom & Specialty"
  };
  
  return mapping[projectCategory] || "Custom & Specialty";
};

/**
 * Gets all unique category names used in the application
 */
export const getAllCategoryNames = (): string[] => {
  return PRODUCT_CATEGORIES.map(cat => cat.value);
};

/**
 * Gets category by name
 */
export const getCategoryByName = (name: string): CategoryOption | undefined => {
  return PRODUCT_CATEGORIES.find(cat => cat.value === name);
};

/**
 * Checks if a category name is valid
 */
export const isValidCategory = (name: string): boolean => {
  return PRODUCT_CATEGORIES.some(cat => cat.value === name);
};
