
import { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import EditorProductTable from '@/components/editor/EditorProductTable';
import EditorProductForm from '@/components/editor/EditorProductForm';
import EditorFilters from '@/components/editor/EditorFilters';
import EditorHeader from '@/components/editor/EditorHeader';
import { Button } from '@/components/ui/button';
import {
  editorProductsService,
  Product
} from '@/services/editorProductsService';
import { LayoutDashboard, BoxIcon, PackageOpen, Plus } from 'lucide-react';

const EditorPanel = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [stockFilter, setStockFilter] = useState<string>('all');
  const [isAddProductOpen, setIsAddProductOpen] = useState(false);
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  const emptyProduct: Product = {
    id: '',
    name: '',
    description: '',
    price: 0,
    image: '',
    category: '',
    status: 'draft',
    stock: 0,
    sku: '',
    images: [],
    rating: 0,
    reviewCount: 0
  };

  useEffect(() => {
    window.scrollTo(0, 0);

    const fetchProducts = async () => {
      try {
        const initialProducts = await editorProductsService.getInitialProducts();
        setProducts(initialProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast({
          title: 'Error fetching products',
          description: 'Failed to load products from the database',
          variant: 'destructive',
        });
      }
    };

    fetchProducts();
  }, []);

  useEffect(() => {
    const filtered = editorProductsService.filterProducts(
      products,
      searchQuery,
      statusFilter,
      categoryFilter,
      stockFilter
    );
    setFilteredProducts(filtered);
  }, [products, searchQuery, statusFilter, categoryFilter, stockFilter]);

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin');
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive"
      });
    } else if (!isAdmin()) {
      navigate('/');
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive"
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // This function is now only updating the local state
  // Products are saved to Supabase directly through the API calls
  const saveProducts = (updatedProducts: Product[]) => {
    setProducts(updatedProducts);
    // No longer saving to localStorage
  };

  const handleStatusChange = (productId: string, newStatus: 'active' | 'draft' | 'deleted') => {
    const updatedProducts = products.map(product =>
      product.id === productId
        ? { ...product, status: newStatus }
        : product
    );

    saveProducts(updatedProducts);

    const actionMap = {
      'active': 'activated',
      'draft': 'set to draft',
      'deleted': 'archived'
    };

    toast({
      title: "Status Updated",
      description: `Product ${actionMap[newStatus]} successfully`,
    });
  };

  const handleBulkAction = (productIds: string[], action: 'delete' | 'restore' | 'activate' | 'draft') => {
    const statusMap = {
      'delete': 'deleted',
      'restore': 'active',
      'activate': 'active',
      'draft': 'draft'
    };

    const newStatus = statusMap[action] as 'active' | 'draft' | 'deleted';

    const updatedProducts = products.map(product =>
      productIds.includes(product.id)
        ? { ...product, status: newStatus }
        : product
    );

    saveProducts(updatedProducts);

    const actionText = {
      'delete': 'archived',
      'restore': 'restored',
      'activate': 'activated',
      'draft': 'moved to draft'
    }[action];

    toast({
      title: "Bulk Action Complete",
      description: `${productIds.length} products ${actionText} successfully`,
    });
  };

  const handleSaveProduct = (product: Product) => {
    let updatedProducts: Product[];

    if (product.id) {
      updatedProducts = products.map(p =>
        p.id === product.id ? product : p
      );
      toast({
        title: "Product Updated",
        description: `Product "${product.name}" updated successfully`,
      });
    } else {
      const newProduct = {
        ...product,
        id: Date.now().toString(),
      };
      updatedProducts = [...products, newProduct];
      toast({
        title: "Product Created",
        description: `Product "${product.name}" created successfully`,
      });
    }

    saveProducts(updatedProducts);
    setIsAddProductOpen(false);
  };

  const uniqueCategories = editorProductsService.getUniqueCategories(products);

  if (!isAuthenticated || !isAdmin()) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Admin Access Required</h1>
            <p className="text-muted-foreground mb-6 text-center">
              You need to be logged in as an admin to access this page.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <Link to="/login?redirect=/admin">Login</Link>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />

      <div className="pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
          <EditorHeader
            totalProducts={products.length}
            onAddProductClick={() => setIsAddProductOpen(true)}
          />

          <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
            <h2 className="text-lg font-medium text-badhees-800 mb-3">Admin Quick Links</h2>
            <div className="flex flex-wrap gap-3">
              <Button asChild variant="outline" size="sm">
                <Link to="/admin/dashboard">
                  <LayoutDashboard className="h-4 w-4 mr-2" />
                  Dashboard
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link to="/admin/products">
                  <BoxIcon className="h-4 w-4 mr-2" />
                  Products
                </Link>
              </Button>
              <Button asChild variant="outline" size="sm" className="bg-badhees-50 border-badhees-200">
                <Link to="/admin/completed-projects">
                  <PackageOpen className="h-4 w-4 mr-2" />
                  Custom Projects
                </Link>
              </Button>
            </div>
          </div>

          <EditorFilters
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            statusFilter={statusFilter}
            setStatusFilter={setStatusFilter}
            categoryFilter={categoryFilter}
            setCategoryFilter={setCategoryFilter}
            stockFilter={stockFilter}
            setStockFilter={setStockFilter}
            uniqueCategories={uniqueCategories}
          />

          <EditorProductTable
            products={filteredProducts}
            onStatusChange={handleStatusChange}
            onBulkAction={handleBulkAction}
            onSave={handleSaveProduct}
          />

          <Dialog open={isAddProductOpen} onOpenChange={setIsAddProductOpen}>
            <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add New Product</DialogTitle>
              </DialogHeader>

              <EditorProductForm
                product={emptyProduct}
                onSave={handleSaveProduct}
                onCancel={() => setIsAddProductOpen(false)}
              />
            </DialogContent>
          </Dialog>

          <div className="mt-8 bg-white p-6 rounded-lg shadow-sm">
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-bold text-badhees-800">Manage Custom Projects</h2>
                <p className="text-sm text-badhees-600 mt-1">Add and manage custom interior projects to showcase your work</p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3">
                <Button asChild variant="default" className="bg-badhees-800 hover:bg-badhees-700">
                  <Link to="/admin/completed-projects/new" className="flex items-center">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Custom Project
                  </Link>
                </Button>
                <Button asChild variant="outline">
                  <Link to="/admin/completed-projects">
                    <PackageOpen className="h-4 w-4 mr-2" />
                    View All Projects
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default EditorPanel;
