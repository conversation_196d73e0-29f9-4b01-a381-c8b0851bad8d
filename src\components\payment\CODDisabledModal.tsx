/**
 * COD Disabled Modal Component
 *
 * Modal that shows when users try to select COD but it's disabled.
 */
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CreditCard, Smartphone, AlertCircle, Clock } from 'lucide-react';

interface CODDisabledModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectOnlinePayment: () => void;
}

const CODDisabledModal: React.FC<CODDisabledModalProps> = ({
  isOpen,
  onClose,
  onSelectOnlinePayment,
}) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center text-lg">
            <Clock className="h-5 w-5 mr-2 text-blue-600" />
            Cash on Delivery - Temporarily Unavailable
          </DialogTitle>
          <DialogDescription className="text-base">
            We're currently only accepting prepaid orders to ensure faster delivery and better service.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Main Message */}
          <Alert className="bg-blue-50 border-blue-200">
            <CreditCard className="h-4 w-4 text-blue-600" />
            <AlertDescription className="text-blue-800">
              <strong>Switch to Online Payment for instant confirmation!</strong>
              Complete your order now with UPI, Credit Card, or Debit Card for faster processing.
            </AlertDescription>
          </Alert>

          {/* Available Payment Methods */}
          <div className="space-y-3">
            <h4 className="font-medium text-sm">Available Payment Methods:</h4>

            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center p-3 border rounded-lg bg-green-50 border-green-200">
                <Smartphone className="h-4 w-4 mr-2 text-green-600" />
                <span className="text-sm font-medium text-green-800">UPI</span>
              </div>

              <div className="flex items-center p-3 border rounded-lg bg-blue-50 border-blue-200">
                <CreditCard className="h-4 w-4 mr-2 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Cards</span>
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 className="font-medium text-sm mb-3 text-green-800">Why Choose Online Payment?</h4>
            <ul className="text-sm text-green-700 space-y-2">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <strong>Instant confirmation</strong> - Order confirmed immediately
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <strong>Faster delivery</strong> - Priority processing for prepaid orders
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <strong>100% secure</strong> - Bank-grade security with Razorpay
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                <strong>Easy refunds</strong> - Hassle-free returns if needed
              </li>
            </ul>
          </div>

          {/* Coming Soon Message */}
          <Alert className="bg-orange-50 border-orange-200">
            <AlertCircle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <strong>COD will be back soon!</strong> We're temporarily focusing on prepaid orders
              to serve you better. Thank you for your understanding! 🙏
            </AlertDescription>
          </Alert>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Go Back
          </Button>
          <Button
            onClick={onSelectOnlinePayment}
            className="w-full sm:w-auto bg-green-600 hover:bg-green-700 text-white"
          >
            <CreditCard className="h-4 w-4 mr-2" />
            Pay Online Now - Instant Confirmation!
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CODDisabledModal;
