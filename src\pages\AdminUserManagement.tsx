import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { useAuth } from '@/context/SupabaseAuthContext';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AlertCircle, CheckCircle, UserPlus, Shield, Trash2, RefreshCw } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';

interface UserProfile {
  id: string;
  email: string;
  display_name: string;
  role: string;
  created_at: string;
}

const AdminUserManagement = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newAdminEmail, setNewAdminEmail] = useState('');
  const [newAdminName, setNewAdminName] = useState('');
  const [newAdminPassword, setNewAdminPassword] = useState('');
  const [isCreatingAdmin, setIsCreatingAdmin] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  useEffect(() => {
    // Check if user is authenticated and admin
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/users');
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive"
      });
    } else if (!isAdmin()) {
      navigate('/');
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive"
      });
    } else {
      fetchUsers();
    }
  }, [isAuthenticated, isAdmin, navigate]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get all users with profiles - only fetch existing users
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .not('id', 'is', null) // Ensure we only get valid user profiles
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Supabase error fetching users:', error);
        throw error;
      }

      // Filter out any null or undefined entries
      const validUsers = (data || []).filter(user => user && user.id && user.email);

      console.log(`Fetched ${validUsers.length} valid users from database`);
      setUsers(validUsers);
    } catch (error: any) {
      console.error('Error fetching users:', error);
      setError(error.message || 'An error occurred while fetching users');
    } finally {
      setIsLoading(false);
    }
  };

  const createAdminUser = async () => {
    try {
      setIsCreatingAdmin(true);
      setError(null);
      setSuccess(null);

      // Validate inputs
      if (!newAdminEmail || !newAdminEmail.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newAdminEmail)) {
        setError('Please enter a valid email address');
        return;
      }

      if (!newAdminPassword || newAdminPassword.length < 6) {
        setError('Password must be at least 6 characters');
        return;
      }

      if (!newAdminName || newAdminName.trim().length < 2) {
        setError('Name must be at least 2 characters');
        return;
      }

      // Create user with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: newAdminEmail,
        password: newAdminPassword,
        email_confirm: true,
        user_metadata: { name: newAdminName }
      });

      if (authError) throw authError;

      if (!authData.user) {
        throw new Error('Failed to create user');
      }

      // Create user profile with admin role
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          id: authData.user.id,
          display_name: newAdminName,
          email: newAdminEmail,
          role: 'admin'
        });

      if (profileError) throw profileError;

      setSuccess(`Admin user ${newAdminName} (${newAdminEmail}) created successfully`);
      setNewAdminEmail('');
      setNewAdminName('');
      setNewAdminPassword('');
      setIsDialogOpen(false);

      // Refresh user list
      fetchUsers();
    } catch (error: any) {
      console.error('Error creating admin user:', error);
      setError(error.message || 'An error occurred while creating admin user');
    } finally {
      setIsCreatingAdmin(false);
    }
  };

  const toggleUserRole = async (userId: string, currentRole: string, email: string) => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      const newRole = currentRole === 'admin' ? 'user' : 'admin';

      // Update user profile role
      const { error } = await supabase
        .from('user_profiles')
        .update({ role: newRole })
        .eq('id', userId);

      if (error) throw error;

      setSuccess(`User ${email} role changed to ${newRole}`);

      // Refresh user list
      fetchUsers();
    } catch (error: any) {
      console.error('Error updating user role:', error);
      setError(error.message || 'An error occurred while updating user role');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-grow flex">
        <AdminSidebar />

        <div className="flex-1 p-6 bg-badhees-50">
          <div className="max-w-6xl mx-auto">
            <Breadcrumb className="mb-6">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>User Management</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            <div className="flex justify-between items-center mb-6">
              <h1 className="text-3xl font-bold text-badhees-800">User Management</h1>
              {/* Create Admin User button removed for privacy and security */}
            </div>

            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {success && (
              <Alert className="mb-6 bg-green-50 border-green-200 text-green-800">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertTitle>Success</AlertTitle>
                <AlertDescription>{success}</AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center">
                    <Shield className="mr-2 h-5 w-5" />
                    User Accounts
                  </span>
                  <Button variant="outline" size="sm" onClick={fetchUsers} disabled={isLoading}>
                    <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                    Refresh
                  </Button>
                </CardTitle>
                <CardDescription>
                  Manage user accounts and their roles
                </CardDescription>
              </CardHeader>

              <CardContent>
                {isLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <RefreshCw className="h-8 w-8 animate-spin text-badhees-600" />
                  </div>
                ) : users.length === 0 ? (
                  <div className="text-center py-8 text-badhees-600">
                    No users found
                  </div>
                ) : (
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Name</TableHead>
                          <TableHead>Email</TableHead>
                          <TableHead>Role</TableHead>
                          <TableHead>Created</TableHead>
                          <TableHead className="text-right">Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {users.map((user) => (
                          <TableRow key={user.id}>
                            <TableCell className="font-medium">{user.display_name}</TableCell>
                            <TableCell>{user.email}</TableCell>
                            <TableCell>
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                user.role === 'admin'
                                  ? 'bg-purple-100 text-purple-800'
                                  : 'bg-blue-100 text-blue-800'
                              }`}>
                                {user.role}
                              </span>
                            </TableCell>
                            <TableCell>{new Date(user.created_at).toLocaleDateString()}</TableCell>
                            <TableCell className="text-right">
                              {/* Role change buttons removed for privacy and security */}
                              <span className="text-gray-400 text-xs italic">Role changes restricted</span>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>

              <CardFooter className="flex justify-between">
                <div className="text-sm text-badhees-600">
                  Total users: {users.length}
                </div>
                <div className="text-sm text-badhees-600">
                  Admins: {users.filter(user => user.role === 'admin').length}
                </div>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminUserManagement;
