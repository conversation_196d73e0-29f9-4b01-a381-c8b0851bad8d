import React, { useState, useRef } from 'react';
import { Upload, X, Loader2, Image as ImageIcon } from 'lucide-react';
import { uploadProductImage } from '@/services/productImageService';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';

interface ProductImageUploadProps {
  productId?: string;
  currentImageUrl?: string;
  onImageChange: (url: string | null) => void;
  isPrimary?: boolean;
}

const ProductImageUpload: React.FC<ProductImageUploadProps> = ({
  productId,
  currentImageUrl,
  onImageChange,
  isPrimary = false
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    console.log('Starting upload for file:', file.name, 'Size:', file.size, 'Type:', file.type);
    setIsUploading(true);
    try {
      const url = await uploadProductImage(file, productId);
      console.log('Upload completed, received URL:', url);
      if (url) {
        onImageChange(url);
        toast({
          title: isPrimary ? 'Main image updated' : 'Image added',
          description: isPrimary
            ? 'The main product image has been successfully updated'
            : 'The image has been successfully added'
        });
      } else {
        toast({
          title: 'Upload failed',
          description: 'Failed to upload image. Please try again.',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error uploading product image:', error);
      toast({
        title: 'Upload error',
        description: 'An error occurred while uploading the image',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
      // Clear the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveImage = () => {
    onImageChange(null);
  };

  return (
    <div className="space-y-3">
      {currentImageUrl ? (
        <div className="relative rounded-md overflow-hidden border">
          <img
            src={currentImageUrl}
            alt="Product"
            className={`w-full ${isPrimary ? 'h-[200px]' : 'h-[150px]'} object-cover`}
            onError={(e) => {
              console.error('Failed to load image:', currentImageUrl);
              (e.target as HTMLImageElement).src = "https://placehold.co/400x300?text=Loading+Error";
            }}
            onLoad={() => {
              console.log('Image loaded successfully:', currentImageUrl);
            }}
          />
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 hover:opacity-100">
            <div className="flex space-x-2">
              <Button
                type="button"
                size="sm"
                variant="secondary"
                onClick={() => fileInputRef.current?.click()}
                disabled={isUploading}
              >
                {isUploading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4" />
                )}
                <span className="ml-1">Change</span>
              </Button>
              <Button
                type="button"
                size="sm"
                variant="destructive"
                onClick={handleRemoveImage}
                disabled={isUploading}
              >
                <X className="h-4 w-4" />
                <span className="ml-1">Remove</span>
              </Button>
            </div>
          </div>
        </div>
      ) : (
        <div 
          className={`border border-dashed rounded-md flex flex-col items-center justify-center p-4 cursor-pointer hover:bg-badhees-50 transition-colors ${isPrimary ? 'h-[200px]' : 'h-[150px]'}`}
          onClick={() => fileInputRef.current?.click()}
        >
          {isUploading ? (
            <div className="flex flex-col items-center justify-center">
              <Loader2 className="h-8 w-8 text-badhees-500 animate-spin mb-2" />
              <p className="text-sm text-badhees-500">Uploading...</p>
            </div>
          ) : (
            <>
              <ImageIcon className="h-10 w-10 text-badhees-300 mb-2" />
              <p className="text-sm font-medium text-badhees-700">
                {isPrimary ? 'Upload main product image' : 'Upload image'}
              </p>
              <p className="text-xs text-badhees-500 mt-1">
                Click to browse or drop an image here
              </p>
            </>
          )}
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        aria-label={isPrimary ? 'Upload main product image' : 'Upload additional product image'}
      />
    </div>
  );
};

export default ProductImageUpload;
