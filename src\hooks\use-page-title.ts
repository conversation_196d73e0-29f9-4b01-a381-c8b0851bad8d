import { useEffect } from 'react';

/**
 * Hook to manage document title dynamically
 * @param title The page title to set
 * @param suffix Optional suffix to append (defaults to "The Badhees")
 */
export function usePageTitle(title?: string, suffix: string = "The Badhees") {
  useEffect(() => {
    const originalTitle = document.title;
    
    if (title) {
      document.title = `${title} | ${suffix}`;
    } else {
      document.title = suffix;
    }

    // Cleanup: restore original title when component unmounts
    return () => {
      document.title = originalTitle;
    };
  }, [title, suffix]);
}
