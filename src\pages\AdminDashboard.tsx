
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from '@/components/ui/button';
import { ChevronRight, PackageOpen, Package, Scissors, MessageSquare, Calendar } from 'lucide-react';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { toast } from 'sonner';
import AdminDashboardStats from '@/components/admin/AdminDashboardStats';
import { useAuth } from '@/context/SupabaseAuthContext';
import { getProducts } from '@/services/supabaseProductsService';
import AdminSidebar from '@/components/admin/AdminSidebar';
import CODSettingsCard from '@/components/admin/CODSettingsCard';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useAdminNotifications } from '@/hooks/useNotifications';
import NotificationBadge from '@/components/admin/NotificationBadge';

// Define the AdminProduct type which matches what AdminProductTable expects
interface AdminProduct {
  id: string;
  name: string;
  description?: string;
  price: number;
  salePrice?: number;
  isSale?: boolean;
  isNew?: boolean;
  image: string;
  category: string;
  stock?: number;
  status?: 'active' | 'draft' | 'deleted';
}

const AdminDashboard = () => {
  const [products, setProducts] = useState<AdminProduct[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { isAuthenticated, user, isAdmin } = useAuth();

  // Get notification counts and handlers
  const {
    counts,
    markOrdersViewed,
    markCustomizationViewed,
    markContactViewed,
    markConsultationViewed
  } = useAdminNotifications();

  useEffect(() => {
    window.scrollTo(0, 0);

    // Fetch products from Supabase
    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const supabaseProducts = await getProducts();

        // Map Supabase products to AdminProduct format
        const formattedProducts = supabaseProducts.map(product => ({
          id: product.id,
          name: product.name,
          description: product.description,
          price: product.price,
          salePrice: product.sale_price,
          isSale: product.is_sale,
          isNew: product.is_new,
          image: product.images?.[0]?.image_url || 'https://placehold.co/100x100?text=No+Image',
          category: product.category?.name || 'Uncategorized',
          stock: product.stock || 0,
          status: product.status
        }));

        setProducts(formattedProducts);
      } catch (error) {
        console.error('Error fetching products:', error);
        toast.error('Failed to load products');
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated && isAdmin()) {
      fetchProducts();
    }
  }, [isAuthenticated, isAdmin]);

  // Redirect if not authenticated or not admin
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin');
      toast.error('Please login to access the admin dashboard');
    } else if (!isAdmin()) {
      navigate('/');
      toast.error('You do not have admin privileges');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Admin Access Required</h1>
            <p className="text-badhees-600 mb-6 text-center">
              You need to be logged in as an admin to access this page.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <Link to="/login?redirect=/admin">Login</Link>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex flex-1 pt-20">
        <AdminSidebar />

        <div className="flex-1 p-6 overflow-auto bg-gray-50">
          {/* Breadcrumb */}
          <Breadcrumb className="mb-6">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Admin Dashboard</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <h1 className="text-2xl md:text-3xl font-bold text-badhees-800 mb-8">
            Welcome to your Dashboard
          </h1>

          {/* Dashboard Overview */}
          <AdminDashboardStats products={products} />

          {/* Quick Links Section */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Quick link cards */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Products</CardTitle>
                  <CardDescription>Manage your store's products</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  <p>Add, edit, or remove products from your inventory.</p>
                </CardContent>
                <CardFooter>
                  <Button asChild className="w-full bg-badhees-800 hover:bg-badhees-700">
                    <Link to="/admin/products" className="flex justify-between items-center w-full">
                      <span>Manage Products</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Custom Projects</CardTitle>
                  <CardDescription>Showcase your completed work</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  <p>Add and manage your custom interior projects.</p>
                </CardContent>
                <CardFooter>
                  <Button asChild className="w-full bg-badhees-800 hover:bg-badhees-700">
                    <Link to="/admin/completed-projects" className="flex justify-between items-center w-full">
                      <span>Manage Projects</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader className="pb-2 relative">
                  <CardTitle className="flex items-center">
                    <Package className="h-5 w-5 mr-2" />
                    Orders
                    {counts.orders > 0 && (
                      <NotificationBadge
                        count={counts.orders}
                        className="ml-2"
                      />
                    )}
                  </CardTitle>
                  <CardDescription>Process customer orders</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  <p>View and update order statuses and details.</p>
                  {counts.orders > 0 && (
                    <p className="text-red-600 font-medium mt-2">
                      {counts.orders} new order{counts.orders > 1 ? 's' : ''} pending!
                    </p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    asChild
                    className="w-full bg-badhees-800 hover:bg-badhees-700"
                  >
                    <Link
                      to="/admin/orders"
                      className="flex justify-between items-center w-full"
                      onClick={() => counts.orders > 0 && markOrdersViewed()}
                    >
                      <span>Process Orders</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>
            </div>

            {/* Notifications Section */}
            <h2 className="text-xl font-semibold mb-4 mt-8">Notifications & Requests</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Customization Requests */}
              <Card>
                <CardHeader className="pb-2 relative">
                  <CardTitle className="flex items-center">
                    <Scissors className="h-5 w-5 mr-2" />
                    Customization
                    {counts.customizationRequests > 0 && (
                      <NotificationBadge
                        count={counts.customizationRequests}
                        className="ml-2"
                      />
                    )}
                  </CardTitle>
                  <CardDescription>Custom furniture requests</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  {counts.customizationRequests > 0 ? (
                    <p className="text-red-600 font-medium">
                      {counts.customizationRequests} new request{counts.customizationRequests > 1 ? 's' : ''}!
                    </p>
                  ) : (
                    <p>No new customization requests</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    asChild
                    variant="outline"
                    className="w-full"
                  >
                    <Link
                      to="/admin/customization-requests"
                      className="flex justify-between items-center w-full"
                      onClick={() => counts.customizationRequests > 0 && markCustomizationViewed()}
                    >
                      <span>View Requests</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              {/* Contact Messages */}
              <Card>
                <CardHeader className="pb-2 relative">
                  <CardTitle className="flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2" />
                    Messages
                    {counts.contactMessages > 0 && (
                      <NotificationBadge
                        count={counts.contactMessages}
                        className="ml-2"
                      />
                    )}
                  </CardTitle>
                  <CardDescription>Customer contact messages</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  {counts.contactMessages > 0 ? (
                    <p className="text-red-600 font-medium">
                      {counts.contactMessages} new message{counts.contactMessages > 1 ? 's' : ''}!
                    </p>
                  ) : (
                    <p>No new contact messages</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    asChild
                    variant="outline"
                    className="w-full"
                  >
                    <Link
                      to="/admin/contact-submissions"
                      className="flex justify-between items-center w-full"
                      onClick={() => counts.contactMessages > 0 && markContactViewed()}
                    >
                      <span>View Messages</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              {/* Consultation Requests */}
              <Card>
                <CardHeader className="pb-2 relative">
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Consultations
                    {counts.consultationRequests > 0 && (
                      <NotificationBadge
                        count={counts.consultationRequests}
                        className="ml-2"
                      />
                    )}
                  </CardTitle>
                  <CardDescription>Consultation appointments</CardDescription>
                </CardHeader>
                <CardContent className="text-sm">
                  {counts.consultationRequests > 0 ? (
                    <p className="text-red-600 font-medium">
                      {counts.consultationRequests} new request{counts.consultationRequests > 1 ? 's' : ''}!
                    </p>
                  ) : (
                    <p>No new consultation requests</p>
                  )}
                </CardContent>
                <CardFooter>
                  <Button
                    asChild
                    variant="outline"
                    className="w-full"
                  >
                    <Link
                      to="/admin/consultation-requests"
                      className="flex justify-between items-center w-full"
                      onClick={() => counts.consultationRequests > 0 && markConsultationViewed()}
                    >
                      <span>View Requests</span>
                      <ChevronRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </CardFooter>
              </Card>

              {/* Summary Card */}
              <Card className="bg-gradient-to-br from-badhees-50 to-badhees-100 border-badhees-200">
                <CardHeader className="pb-2">
                  <CardTitle className="text-badhees-800">Total Notifications</CardTitle>
                  <CardDescription>All pending items</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-badhees-800">
                    {counts.orders + counts.customizationRequests + counts.contactMessages + counts.consultationRequests}
                  </div>
                  <p className="text-sm text-badhees-600 mt-1">
                    Items requiring attention
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Payment Settings Section */}
            <h2 className="text-xl font-semibold mb-4 mt-8">Payment Settings</h2>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <CODSettingsCard />
            </div>

          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminDashboard;
