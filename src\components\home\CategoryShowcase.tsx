
import React from "react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";

interface CategoryProps {
  title: string;
  description: string;
  image: string;
  link: string;
  position: "left" | "right";
}

const CategoryShowcase = () => {
  const categories: CategoryProps[] = [
    {
      title: "Living Room",
      description: "Transform your living space with our elegant sofas, coffee tables, and accent chairs designed for both comfort and style.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Homepageimages/homeliving%20(1).avif",
      link: "/products?category=Living Room",
      position: "right"
    },
    {
      title: "Bedroom",
      description: "Create a serene retreat with our minimalist bed frames, nightstands, and dressers crafted for tranquility.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Homepageimages/homepagebedroom1.avif",
      link: "/products?category=Bedroom",
      position: "left"
    },
    {
      title: "Dining",
      description: "Elevate your dining experience with our carefully crafted tables, chairs, and storage solutions for memorable gatherings.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Homepageimages/homedininig%20(1).avif",
      link: "/products?category=Dining Room",
      position: "right"
    },
    {
      title: "Custom & Specialty",
      description: "Discover our bespoke furniture solutions tailored to your unique style and space requirements.",
      image: "https://edthfbjthzcusqytmlzd.supabase.co/storage/v1/object/public/others/Homepageimages/homecustom1%20(1).avif",
      link: "/custom-interiors",
      position: "left"
    }
  ];

  return (
    <section className="py-12 md:py-16 bg-badhees-50">
      <div className="max-w-[1400px] mx-auto px-4 sm:px-8">
        <div className="text-center max-w-3xl mx-auto mb-8 md:mb-12">
          <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-badhees-800 mb-4">
            Designed For Every Space
          </h2>
          <p className="text-base md:text-lg text-badhees-600 mx-auto">
            Explore our collections crafted for different spaces in your home, each piece designed to enhance your lifestyle and elevate your interior.
          </p>
        </div>

        <div className="space-y-12 md:space-y-16">
          {categories.map((category, index) => (
            <Category key={index} {...category} />
          ))}
        </div>
      </div>
    </section>
  );
};

const Category = ({ title, description, image, link, position }: CategoryProps) => {
  return (
    <div className={cn(
      "flex flex-col",
      position === "left" ? "lg:flex-row" : "lg:flex-row-reverse",
      "gap-8 lg:gap-16 items-center"
    )}>
      <div className="lg:w-1/2">
        <div className="relative overflow-hidden rounded-2xl shadow-md">
          <div className="aspect-w-4 aspect-h-3">
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover transition-transform duration-700 hover:scale-105"
              loading="lazy"
            />
          </div>
          <div className="absolute inset-0 bg-gradient-to-t from-badhees-900/30 to-transparent"></div>
          <div className="absolute bottom-6 left-6">
            <h3 className="text-xl font-semibold text-white">{title}</h3>
          </div>
        </div>
      </div>

      <div className="lg:w-1/2 flex flex-col items-start">
        <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-badhees-800 mb-4">{title} Collection</h3>
        <p className="text-badhees-600 mb-8 max-w-xl">{description}</p>
        <Link
          to={link}
          className="inline-flex items-center justify-center px-6 py-3 rounded-md bg-badhees-800 text-white font-medium transition-colors hover:bg-badhees-700"
        >
          Explore {title}
        </Link>
      </div>
    </div>
  );
};

export default CategoryShowcase;
