
import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Loader2, CreditCard, Smartphone, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '@/context/SupabaseAuthContext';
import { useCODEnabled, useToggleCOD } from '@/hooks/useSettings';
import { toast } from '@/hooks/use-toast';

const AdminSettings = () => {
  const navigate = useNavigate();
  const { isAuthenticated, isAdmin } = useAuth();

  // Get COD availability from settings
  const { data: isCODEnabled, isLoading: isLoadingCOD, error } = useCODEnabled();
  const toggleCODMutation = useToggleCOD();

  useEffect(() => {
    window.scrollTo(0, 0);

    // Check if user has access to this page
    if (!isAuthenticated) {
      navigate('/login?redirect=/admin/settings');
    } else if (!isAdmin()) {
      navigate('/');
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Handle COD toggle
  const handleCODToggle = async (enabled: boolean) => {
    try {
      await toggleCODMutation.mutateAsync(enabled);
    } catch (error) {
      console.error('Error toggling COD:', error);
      toast({
        title: 'Error',
        description: 'Failed to update COD settings. Please try again.',
        variant: 'destructive'
      });
    }
  };

  if (!isAuthenticated || !isAdmin()) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <div className="max-w-md w-full p-8 bg-white rounded-lg shadow-md">
            <h1 className="text-2xl font-bold text-center mb-6">Admin Access Required</h1>
            <p className="text-badhees-600 mb-6 text-center">
              You need to be logged in as an admin to access this page.
            </p>
            <div className="flex justify-center">
              <Button asChild className="w-full">
                <a href="/login?redirect=/admin/settings">Login</a>
              </Button>
            </div>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      <AdminSidebar />

      <div className="flex-1">
        <Navbar />

        <div className="pt-28 pb-16 px-4 sm:px-8 max-w-[1400px] mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-badhees-800 mb-6">
            Payment Settings
          </h1>

          {/* Error State */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Failed to load payment settings. Please refresh the page and try again.
              </AlertDescription>
            </Alert>
          )}

          {/* Payment Methods Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-badhees-600" />
                Payment Methods
              </CardTitle>
              <CardDescription>
                Configure which payment methods are available to customers
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Online Payment Methods - Always Enabled */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Online Payment Methods</h3>

                <div className="flex items-center justify-between p-4 border rounded-lg bg-green-50 border-green-200">
                  <div className="flex items-center space-y-0.5">
                    <CreditCard className="h-5 w-5 mr-3 text-green-600" />
                    <div>
                      <Label className="text-base font-medium text-green-800">Credit & Debit Cards</Label>
                      <p className="text-sm text-green-600">Visa, Mastercard, RuPay, and other cards</p>
                    </div>
                  </div>
                  <div className="text-green-600 font-medium">Always Enabled</div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg bg-green-50 border-green-200">
                  <div className="flex items-center space-y-0.5">
                    <Smartphone className="h-5 w-5 mr-3 text-green-600" />
                    <div>
                      <Label className="text-base font-medium text-green-800">UPI Payments</Label>
                      <p className="text-sm text-green-600">Google Pay, PhonePe, Paytm, and other UPI apps</p>
                    </div>
                  </div>
                  <div className="text-green-600 font-medium">Always Enabled</div>
                </div>
              </div>

              {/* COD Toggle Section */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium mb-4">Cash on Delivery</h3>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="cod-toggle" className="text-base font-medium">
                      Cash on Delivery (COD)
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Allow customers to pay when they receive their order
                    </p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isLoadingCOD && (
                      <Loader2 className="h-4 w-4 animate-spin text-badhees-600" />
                    )}
                    <Switch
                      id="cod-toggle"
                      checked={isCODEnabled || false}
                      onCheckedChange={handleCODToggle}
                      disabled={isLoadingCOD || toggleCODMutation.isPending}
                    />
                  </div>
                </div>

                {/* COD Status Indicator */}
                <div className="mt-4 p-3 rounded-lg bg-gray-50">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${isCODEnabled ? 'bg-green-500' : 'bg-red-500'}`} />
                    <span className="text-sm font-medium">
                      COD Status:
                      <span className={`ml-1 ${isCODEnabled ? 'text-green-600' : 'text-red-600'}`}>
                        {isLoadingCOD ? 'Loading...' : (isCODEnabled ? 'Enabled' : 'Disabled')}
                      </span>
                    </span>
                  </div>
                </div>

                {/* Information Alert */}
                <Alert className="mt-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    {isCODEnabled ? (
                      <>
                        <strong>COD is currently enabled.</strong> Customers can select "Cash on Delivery"
                        during checkout and pay when they receive their order.
                      </>
                    ) : (
                      <>
                        <strong>COD is currently disabled.</strong> Customers will see the COD option
                        but won't be able to select it. They'll be prompted to use online payment methods.
                      </>
                    )}
                  </AlertDescription>
                </Alert>

                {/* Loading State */}
                {toggleCODMutation.isPending && (
                  <Alert className="mt-4">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <AlertDescription>
                      Updating COD settings...
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <Footer />
      </div>
    </div>
  );
};

export default AdminSettings;
