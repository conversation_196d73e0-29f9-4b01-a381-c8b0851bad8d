import { supabase } from '@/lib/supabase';
import {
  Employee,
  EmployeeWithDetails,
  Attendance,
  AttendanceWithEmployee,
  Overtime,
  OvertimeWithEmployee,
  Payroll,
  PayrollWithEmployee,
  MonthlyAttendanceSummary,
  EmployeeAttendance,
  DailyAttendance,
  OvertimeSummary,
  PayrollCalculationInput,
  PayrollCalculationResult,
  EmployeeLeave,
  EmployeeLeaveWithEmployee,
  EmployeeLeaveBalance
} from './types';
import { format, parseISO, isValid, startOfMonth, endOfMonth, differenceInDays, addDays } from 'date-fns';

// Employee CRUD operations
export const getEmployees = async (): Promise<EmployeeWithDetails[]> => {
  const { data, error } = await supabase
    .from('employees')
    .select('*, manager:manager_id(first_name, last_name)')
    .order('first_name', { ascending: true });

  if (error) {
    console.error('Error fetching employees:', error);
    throw error;
  }

  return data.map(employee => ({
    ...employee,
    manager_name: employee.manager ? `${employee.manager.first_name} ${employee.manager.last_name}` : undefined
  }));
};

export const getEmployee = async (id: string): Promise<EmployeeWithDetails> => {
  const { data, error } = await supabase
    .from('employees')
    .select('*, manager:manager_id(first_name, last_name)')
    .eq('id', id)
    .single();

  if (error) {
    console.error(`Error fetching employee with ID ${id}:`, error);
    throw error;
  }

  return {
    ...data,
    manager_name: data.manager ? `${data.manager.first_name} ${data.manager.last_name}` : undefined
  };
};

export const createEmployee = async (employee: Omit<Employee, 'id' | 'created_at' | 'updated_at'>): Promise<Employee> => {
  const { data, error } = await supabase
    .from('employees')
    .insert([employee])
    .select()
    .single();

  if (error) {
    console.error('Error creating employee:', error);
    throw error;
  }

  return data;
};

export const updateEmployee = async (id: string, employee: Partial<Omit<Employee, 'id' | 'created_at' | 'updated_at'>>): Promise<Employee> => {
  const { data, error } = await supabase
    .from('employees')
    .update({ ...employee, updated_at: new Date().toISOString() })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating employee with ID ${id}:`, error);
    throw error;
  }

  return data;
};

export const deleteEmployee = async (id: string): Promise<void> => {
  const { error } = await supabase
    .from('employees')
    .delete()
    .eq('id', id);

  if (error) {
    console.error(`Error deleting employee with ID ${id}:`, error);
    throw error;
  }
};

// Attendance operations
export const getAttendanceByDate = async (date: string): Promise<AttendanceWithEmployee[]> => {
  const formattedDate = formatDateForDB(date);

  const { data, error } = await supabase
    .from('attendance')
    .select('*, employee:employee_id(first_name, last_name)')
    .eq('date', formattedDate);

  if (error) {
    console.error(`Error fetching attendance for date ${date}:`, error);
    throw error;
  }

  return data.map(record => ({
    ...record,
    employee_name: `${record.employee.first_name} ${record.employee.last_name}`
  }));
};

export const getAttendanceByEmployeeAndMonth = async (employeeId: string, year: number, month: number): Promise<Attendance[]> => {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);

  const { data, error } = await supabase
    .from('attendance')
    .select('*')
    .eq('employee_id', employeeId)
    .gte('date', startDate.toISOString().split('T')[0])
    .lte('date', endDate.toISOString().split('T')[0])
    .order('date', { ascending: true });

  if (error) {
    console.error(`Error fetching attendance for employee ${employeeId} for ${year}-${month}:`, error);
    throw error;
  }

  return data;
};

export const getMonthlyAttendanceSummary = async (year: number, month: number): Promise<MonthlyAttendanceSummary[]> => {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);

  // First, get all employees
  const { data: employees, error: employeesError } = await supabase
    .from('employees')
    .select('id, first_name, last_name')
    .eq('status', 'active');

  if (employeesError) {
    console.error('Error fetching employees for attendance summary:', employeesError);
    throw employeesError;
  }

  // Then, get attendance records for the month
  const { data: attendanceRecords, error: attendanceError } = await supabase
    .from('attendance')
    .select('*')
    .gte('date', startDate.toISOString().split('T')[0])
    .lte('date', endDate.toISOString().split('T')[0]);

  if (attendanceError) {
    console.error(`Error fetching attendance records for ${year}-${month}:`, attendanceError);
    throw attendanceError;
  }

  // Calculate working days in the month (excluding weekends)
  const totalDays = differenceInDays(endDate, startDate) + 1;

  // Group attendance records by employee
  const attendanceByEmployee: Record<string, Attendance[]> = {};
  attendanceRecords.forEach(record => {
    if (!attendanceByEmployee[record.employee_id]) {
      attendanceByEmployee[record.employee_id] = [];
    }
    attendanceByEmployee[record.employee_id].push(record);
  });

  // Calculate summary for each employee
  return employees.map(employee => {
    const records = attendanceByEmployee[employee.id] || [];
    const presentDays = records.filter(r => r.status === 'present').length;
    const absentDays = records.filter(r => r.status === 'absent').length;
    const halfDays = records.filter(r => r.status === 'half-day').length;
    const leaveDays = records.filter(r => r.status === 'leave').length;

    // Calculate working percentage (present days + half days * 0.5) / total working days
    const workingPercentage = Math.round(((presentDays + (halfDays * 0.5)) / totalDays) * 100);

    return {
      employee_id: employee.id,
      employee_name: `${employee.first_name} ${employee.last_name}`,
      present_days: presentDays,
      absent_days: absentDays,
      half_days: halfDays,
      leave_days: leaveDays,
      working_percentage: workingPercentage
    };
  });
};

export const markAttendance = async (records: Omit<Attendance, 'id' | 'created_at' | 'updated_at'>[]): Promise<Attendance[]> => {
  // Format dates for all records
  const formattedRecords = records.map(record => ({
    ...record,
    date: formatDateForDB(record.date)
  }));

  const { data, error } = await supabase
    .from('attendance')
    .upsert(formattedRecords, { onConflict: 'employee_id,date' })
    .select();

  if (error) {
    console.error('Error marking attendance:', error);
    throw error;
  }

  return data;
};

// Overtime operations
export const getOvertimeByEmployee = async (employeeId: string): Promise<Overtime[]> => {
  const { data, error } = await supabase
    .from('overtime')
    .select('*')
    .eq('employee_id', employeeId)
    .order('date', { ascending: false });

  if (error) {
    console.error(`Error fetching overtime for employee ${employeeId}:`, error);
    throw error;
  }

  return data;
};

export const getOvertimeByMonth = async (year: number, month: number): Promise<OvertimeWithEmployee[]> => {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);

  const { data, error } = await supabase
    .from('overtime')
    .select('*, employees!overtime_employee_id_fkey(first_name, last_name)')
    .gte('date', startDate.toISOString().split('T')[0])
    .lte('date', endDate.toISOString().split('T')[0])
    .order('date', { ascending: false });

  if (error) {
    console.error(`Error fetching overtime for ${year}-${month}:`, error);
    throw error;
  }

  return data.map(record => ({
    ...record,
    employee_name: `${record.employees.first_name} ${record.employees.last_name}`
  }));
};

export const getOvertimeSummary = async (year: number, month: number): Promise<OvertimeSummary[]> => {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);

  // First, get all employees
  const { data: employees, error: employeesError } = await supabase
    .from('employees')
    .select('id, first_name, last_name')
    .eq('status', 'active');

  if (employeesError) {
    console.error('Error fetching employees for overtime summary:', employeesError);
    throw employeesError;
  }

  // Then, get overtime records for the month
  const { data: overtimeRecords, error: overtimeError } = await supabase
    .from('overtime')
    .select('*')
    .gte('date', startDate.toISOString().split('T')[0])
    .lte('date', endDate.toISOString().split('T')[0]);

  if (overtimeError) {
    console.error(`Error fetching overtime records for ${year}-${month}:`, overtimeError);
    throw overtimeError;
  }

  // Group overtime records by employee
  const overtimeByEmployee: Record<string, Overtime[]> = {};
  overtimeRecords.forEach(record => {
    if (!overtimeByEmployee[record.employee_id]) {
      overtimeByEmployee[record.employee_id] = [];
    }
    overtimeByEmployee[record.employee_id].push(record);
  });

  // Calculate summary for each employee
  return employees.map(employee => {
    const records = overtimeByEmployee[employee.id] || [];
    const totalHours = records.reduce((sum, r) => sum + r.hours, 0);
    const approvedHours = records.filter(r => r.status === 'approved').reduce((sum, r) => sum + r.hours, 0);
    const pendingHours = records.filter(r => r.status === 'pending').reduce((sum, r) => sum + r.hours, 0);
    const rejectedHours = records.filter(r => r.status === 'rejected').reduce((sum, r) => sum + r.hours, 0);

    return {
      employee_id: employee.id,
      employee_name: `${employee.first_name} ${employee.last_name}`,
      total_hours: totalHours,
      approved_hours: approvedHours,
      pending_hours: pendingHours,
      rejected_hours: rejectedHours
    };
  });
};

export const recordOvertime = async (overtime: Omit<Overtime, 'id' | 'created_at' | 'updated_at'>): Promise<Overtime> => {
  const formattedOvertime = {
    ...overtime,
    date: formatDateForDB(overtime.date)
  };

  const { data, error } = await supabase
    .from('overtime')
    .insert([formattedOvertime])
    .select()
    .single();

  if (error) {
    console.error('Error recording overtime:', error);
    throw error;
  }

  return data;
};

export const updateOvertimeStatus = async (id: string, status: 'approved' | 'rejected', approvedBy: string): Promise<Overtime> => {
  const { data, error } = await supabase
    .from('overtime')
    .update({
      status,
      approved_by: approvedBy,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating overtime status for ID ${id}:`, error);
    throw error;
  }

  return data;
};

// Payroll operations
export const calculatePayroll = async (input: PayrollCalculationInput): Promise<PayrollCalculationResult> => {
  // Get employee details
  const { data: employee, error: employeeError } = await supabase
    .from('employees')
    .select('id, first_name, last_name, base_salary')
    .eq('id', input.employee_id)
    .single();

  if (employeeError) {
    console.error(`Error fetching employee ${input.employee_id} for payroll calculation:`, employeeError);
    throw employeeError;
  }

  // Get attendance records for the period
  const { data: attendanceRecords, error: attendanceError } = await supabase
    .from('attendance')
    .select('*')
    .eq('employee_id', input.employee_id)
    .gte('date', input.period_start_date)
    .lte('date', input.period_end_date);

  if (attendanceError) {
    console.error(`Error fetching attendance records for payroll calculation:`, attendanceError);
    throw attendanceError;
  }

  // Calculate days worked
  const presentDays = attendanceRecords.filter(r => r.status === 'present').length;
  const halfDays = attendanceRecords.filter(r => r.status === 'half-day').length;
  const daysWorked = presentDays + (halfDays * 0.5);

  // Calculate base pay
  const basePay = employee.base_salary * daysWorked;

  // Get overtime records if needed
  let overtimeHours = 0;
  let overtimePay = 0;

  if (input.include_approved_overtime) {
    const { data: overtimeRecords, error: overtimeError } = await supabase
      .from('overtime')
      .select('*')
      .eq('employee_id', input.employee_id)
      .eq('status', 'approved')
      .gte('date', input.period_start_date)
      .lte('date', input.period_end_date);

    if (overtimeError) {
      console.error(`Error fetching overtime records for payroll calculation:`, overtimeError);
      throw overtimeError;
    }

    // Calculate overtime pay
    overtimeHours = overtimeRecords.reduce((sum, record) => sum + record.hours, 0);
    const defaultHourlyRate = employee.base_salary / 8; // Assuming 8 hours per day
    overtimePay = overtimeRecords.reduce((sum, record) => {
      // Use cost_per_hour if available, otherwise use the default hourly rate
      const hourlyRate = record.cost_per_hour || defaultHourlyRate;
      return sum + (record.hours * hourlyRate * record.rate_multiplier);
    }, 0);
  }

  // Calculate total pay
  const bonuses = input.additional_bonuses || 0;
  const deductions = input.additional_deductions || 0;
  const totalPay = basePay + overtimePay + bonuses - deductions;

  return {
    employee_id: employee.id,
    employee_name: `${employee.first_name} ${employee.last_name}`,
    period_start_date: input.period_start_date,
    period_end_date: input.period_end_date,
    days_worked: daysWorked,
    overtime_hours: overtimeHours,
    base_pay: basePay,
    overtime_pay: overtimePay,
    bonuses,
    deductions,
    total_pay: totalPay
  };
};

export const createPayroll = async (payroll: Omit<Payroll, 'id' | 'created_at' | 'updated_at'>): Promise<Payroll> => {
  const { data, error } = await supabase
    .from('payroll')
    .insert([payroll])
    .select()
    .single();

  if (error) {
    console.error('Error creating payroll record:', error);
    throw error;
  }

  return data;
};

export const getPayrollByEmployee = async (employeeId: string): Promise<Payroll[]> => {
  const { data, error } = await supabase
    .from('payroll')
    .select('*')
    .eq('employee_id', employeeId)
    .order('period_end_date', { ascending: false });

  if (error) {
    console.error(`Error fetching payroll for employee ${employeeId}:`, error);
    throw error;
  }

  return data;
};

export const getPayrollByPeriod = async (startDate: string, endDate: string): Promise<PayrollWithEmployee[]> => {
  const { data, error } = await supabase
    .from('payroll')
    .select('*, employee:employee_id(first_name, last_name)')
    .eq('period_start_date', formatDateForDB(startDate))
    .eq('period_end_date', formatDateForDB(endDate));

  if (error) {
    console.error(`Error fetching payroll for period ${startDate} to ${endDate}:`, error);
    throw error;
  }

  return data.map(record => ({
    ...record,
    employee_name: `${record.employee.first_name} ${record.employee.last_name}`
  }));
};

export const updatePayrollStatus = async (id: string, status: 'pending' | 'paid' | 'cancelled', paymentDate?: string): Promise<Payroll> => {
  const updateData: any = {
    payment_status: status,
    updated_at: new Date().toISOString()
  };

  if (status === 'paid' && paymentDate) {
    updateData.payment_date = formatDateForDB(paymentDate);
  }

  const { data, error } = await supabase
    .from('payroll')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating payroll status for ID ${id}:`, error);
    throw error;
  }

  return data;
};

// Employee Leave operations
export const getEmployeeLeaveByEmployee = async (employeeId: string): Promise<EmployeeLeave[]> => {
  const { data, error } = await supabase
    .from('employee_leave')
    .select('*')
    .eq('employee_id', employeeId)
    .order('start_date', { ascending: false });

  if (error) {
    console.error(`Error fetching leave for employee ${employeeId}:`, error);
    throw error;
  }

  return data;
};

export const getEmployeeLeaveByStatus = async (status: 'pending' | 'approved' | 'rejected'): Promise<EmployeeLeaveWithEmployee[]> => {
  const { data, error } = await supabase
    .from('employee_leave')
    .select('*, employees!employee_leave_employee_id_fkey(first_name, last_name)')
    .eq('status', status)
    .order('start_date', { ascending: true });

  if (error) {
    console.error(`Error fetching leave with status ${status}:`, error);
    throw error;
  }

  return data.map(record => ({
    ...record,
    employee_name: `${record.employees.first_name} ${record.employees.last_name}`
  }));
};

export const getEmployeeLeaveByDateRange = async (startDate: string, endDate: string): Promise<EmployeeLeaveWithEmployee[]> => {
  const formattedStartDate = formatDateForDB(startDate);
  const formattedEndDate = formatDateForDB(endDate);

  const { data, error } = await supabase
    .from('employee_leave')
    .select('*, employees!employee_leave_employee_id_fkey(first_name, last_name)')
    .or(`start_date.gte.${formattedStartDate},end_date.lte.${formattedEndDate}`)
    .order('start_date', { ascending: true });

  if (error) {
    console.error(`Error fetching leave for date range ${startDate} to ${endDate}:`, error);
    throw error;
  }

  return data.map(record => ({
    ...record,
    employee_name: `${record.employees.first_name} ${record.employees.last_name}`
  }));
};

export const requestEmployeeLeave = async (leave: Omit<EmployeeLeave, 'id' | 'created_at' | 'updated_at'>): Promise<EmployeeLeave> => {
  const formattedLeave = {
    ...leave,
    start_date: formatDateForDB(leave.start_date),
    end_date: formatDateForDB(leave.end_date)
  };

  const { data, error } = await supabase
    .from('employee_leave')
    .insert([formattedLeave])
    .select()
    .single();

  if (error) {
    console.error('Error requesting leave:', error);
    throw error;
  }

  return data;
};

export const updateEmployeeLeaveStatus = async (id: string, status: 'approved' | 'rejected', approvedBy: string, notes?: string): Promise<EmployeeLeave> => {
  const updateData: any = {
    status,
    approved_by: approvedBy,
    updated_at: new Date().toISOString()
  };

  if (notes) {
    updateData.notes = notes;
  }

  const { data, error } = await supabase
    .from('employee_leave')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating leave status for ID ${id}:`, error);
    throw error;
  }

  return data;
};

export const getEmployeeLeaveBalance = async (employeeId: string, year: number): Promise<EmployeeLeaveBalance> => {
  const { data, error } = await supabase
    .from('employee_leave_balance')
    .select('*')
    .eq('employee_id', employeeId)
    .eq('year', year)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // No leave balance found, create a default one
      const defaultBalance = {
        employee_id: employeeId,
        year,
        annual_leave_balance: 0,
        sick_leave_balance: 0,
        personal_leave_balance: 0
      };

      const { data: newData, error: insertError } = await supabase
        .from('employee_leave_balance')
        .insert([defaultBalance])
        .select()
        .single();

      if (insertError) {
        console.error(`Error creating leave balance for employee ${employeeId}:`, insertError);
        throw insertError;
      }

      return newData;
    }

    console.error(`Error fetching leave balance for employee ${employeeId}:`, error);
    throw error;
  }

  return data;
};

export const updateEmployeeLeaveBalance = async (
  id: string,
  balance: Partial<Pick<EmployeeLeaveBalance, 'annual_leave_balance' | 'sick_leave_balance' | 'personal_leave_balance'>>
): Promise<EmployeeLeaveBalance> => {
  const { data, error } = await supabase
    .from('employee_leave_balance')
    .update({
      ...balance,
      updated_at: new Date().toISOString()
    })
    .eq('id', id)
    .select()
    .single();

  if (error) {
    console.error(`Error updating leave balance for ID ${id}:`, error);
    throw error;
  }

  return data;
};

// Helper functions
function formatDateForDB(dateString: string): string {
  // If it's already in YYYY-MM-DD format, return as is
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }

  // Try to parse the date
  const date = parseISO(dateString);
  if (!isValid(date)) {
    throw new Error(`Invalid date format: ${dateString}`);
  }

  // Format as YYYY-MM-DD
  return format(date, 'yyyy-MM-dd');
}
