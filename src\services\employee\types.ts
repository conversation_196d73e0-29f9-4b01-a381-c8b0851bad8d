export interface Employee {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  position: string;
  department?: string;
  manager_id?: string;
  hire_date: string; // ISO date string
  base_salary: number;
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface Attendance {
  id: string;
  employee_id: string;
  date: string; // ISO date string
  status: 'present' | 'absent' | 'half-day' | 'leave';
  notes?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface Overtime {
  id: string;
  employee_id: string;
  date: string; // ISO date string
  hours: number;
  rate_multiplier: number;
  cost_per_hour?: number;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  approved_by?: string;
}

export interface Payroll {
  id: string;
  employee_id: string;
  period_start_date: string; // ISO date string
  period_end_date: string; // ISO date string
  days_worked: number;
  overtime_hours: number;
  base_pay: number;
  overtime_pay: number;
  bonuses: number;
  deductions: number;
  total_pay: number;
  payment_date?: string; // ISO date string
  payment_status: 'pending' | 'paid' | 'cancelled';
  notes?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
}

export interface EmployeeWithDetails extends Employee {
  manager_name?: string;
}

export interface AttendanceWithEmployee extends Attendance {
  employee_name: string;
}

export interface OvertimeWithEmployee extends Overtime {
  employee_name: string;
}

export interface PayrollWithEmployee extends Payroll {
  employee_name: string;
}

export interface MonthlyAttendanceSummary {
  employee_id: string;
  employee_name: string;
  present_days: number;
  absent_days: number;
  half_days: number;
  leave_days: number;
  working_percentage: number;
}

export interface AttendanceRecord {
  date: string;
  status: 'present' | 'absent' | 'half-day' | 'leave';
  notes?: string;
}

export interface EmployeeAttendance {
  employee_id: string;
  employee_name: string;
  records: Record<string, AttendanceRecord>;
}

export interface DailyAttendance {
  date: string;
  records: {
    employee_id: string;
    employee_name: string;
    status: 'present' | 'absent' | 'half-day' | 'leave';
    notes?: string;
  }[];
}

export interface OvertimeSummary {
  employee_id: string;
  employee_name: string;
  total_hours: number;
  approved_hours: number;
  pending_hours: number;
  rejected_hours: number;
}

export interface PayrollCalculationInput {
  employee_id: string;
  period_start_date: string;
  period_end_date: string;
  include_approved_overtime: boolean;
  additional_bonuses?: number;
  additional_deductions?: number;
}

export interface PayrollCalculationResult {
  employee_id: string;
  employee_name: string;
  period_start_date: string;
  period_end_date: string;
  days_worked: number;
  overtime_hours: number;
  base_pay: number;
  overtime_pay: number;
  bonuses: number;
  deductions: number;
  total_pay: number;
}

export interface EmployeeLeave {
  id: string;
  employee_id: string;
  start_date: string; // ISO date string
  end_date: string; // ISO date string
  leave_type: 'annual' | 'sick' | 'personal' | 'unpaid' | 'other';
  status: 'pending' | 'approved' | 'rejected';
  reason?: string;
  notes?: string;
  created_at?: string;
  updated_at?: string;
  created_by?: string;
  approved_by?: string;
}

export interface EmployeeLeaveWithEmployee extends EmployeeLeave {
  employee_name: string;
}

export interface EmployeeLeaveBalance {
  id: string;
  employee_id: string;
  year: number;
  annual_leave_balance: number;
  sick_leave_balance: number;
  personal_leave_balance: number;
  created_at?: string;
  updated_at?: string;
}

export interface EmployeeLeaveBalanceWithEmployee extends EmployeeLeaveBalance {
  employee_name: string;
}
