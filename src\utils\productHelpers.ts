
import { Product } from "@/lib/data";

/**
 * Ensures a product object has all required properties with default values if missing
 */
export const normalizeProduct = (product: Partial<Product>): Product => {
  return {
    id: product.id || "",
    name: product.name || "",
    description: product.description || "",
    price: product.price || 0,
    image: product.image || "",
    category: product.category || "",
    stockStatus: product.stockStatus || "in_stock",
    status: product.status || 'active',
    stock: product.stock ?? 10,
    sku: product.sku || `SKU-${product.id || "unknown"}`,
    // Optional properties with default values
    salePrice: product.salePrice,
    isSale: product.isSale || false,
    isNew: product.isNew || false,
    images: product.images || [],
    // Remove properties that don't exist in Product type
    specifications: product.specifications || {
      "Dimensions": "Various sizes available",
      "Material": "Premium quality materials",
      "Care": "Refer to product manual for care instructions",
      "Origin": "Designed and crafted with attention to detail"
    }
  };
};

/**
 * Validates if a product has all necessary fields and returns a boolean
 */
export const isValidProduct = (product: any): boolean => {
  if (!product) return false;
  
  const requiredFields = ['id', 'name', 'price', 'image', 'category'];
  return requiredFields.every(field => !!product[field]);
};

/**
 * Generates a URL-friendly slug from a product name
 */
export const generateProductSlug = (name: string): string => {
  return name
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-'); // Replace multiple hyphens with single hyphen
};
