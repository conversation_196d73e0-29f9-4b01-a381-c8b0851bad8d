/**
 * Analytics Types
 * 
 * This module provides type definitions for analytics data.
 */

/**
 * Dashboard statistics interface
 */
export interface DashboardStats {
  totalRevenue: number;
  orderStats: OrderStats;
  customerStats: CustomerStats;
  categoryPerformance: CategoryPerformance[];
  salesTrend: SalesTrend[];
  weeklyPerformance: WeeklyPerformance[];
  topSellingProducts: TopSellingProduct[];
  totalProducts: number;
  totalProjects: number;
}

/**
 * Order statistics interface
 */
export interface OrderStats {
  total: number;
  completed: number;
  processing: number;
  pending: number;
}

/**
 * Customer statistics interface
 */
export interface CustomerStats {
  total: number;
  active: number;
  inactive: number;
  totalCustomers: number;
}

/**
 * Category performance interface
 */
export interface CategoryPerformance {
  name: string;
  value: number;
}

/**
 * Sales trend interface
 */
export interface SalesTrend {
  name: string;
  sales: number;
  forecast?: number;
}

/**
 * Weekly performance interface
 */
export interface WeeklyPerformance {
  name: string;
  orders: number;
  revenue: number;
}

/**
 * Top selling product interface
 */
export interface TopSellingProduct {
  id: string;
  name: string;
  category: string;
  total_sales: number;
  quantity_sold?: number;
  image_url?: string;
}

/**
 * Sort options for top selling products
 */
export type TopProductsSortBy = 'revenue' | 'quantity';
