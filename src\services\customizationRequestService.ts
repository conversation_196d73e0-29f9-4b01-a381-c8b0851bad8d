import { supabase } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';

export interface CustomizationRequest {
  id?: string;
  user_id?: string;
  product_id: string;
  name: string;
  email: string;
  phone?: string;
  description: string;
  status?: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  created_at?: string;
  updated_at?: string;
  product?: {
    name: string;
    image: string;
  };
}

/**
 * Create a new customization request
 * @param request The customization request data
 * @returns The created request or null if creation failed
 */
export const createCustomizationRequest = async (request: CustomizationRequest): Promise<CustomizationRequest | null> => {
  try {
    const { data, error } = await supabase
      .from('customization_requests')
      .insert([request])
      .select()
      .single();

    if (error) {
      console.error('Error creating customization request:', error);
      toast({
        title: 'Error submitting request',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return null;
    }

    toast({
      title: 'Request submitted',
      description: 'Your customization request has been successfully submitted. We will contact you soon.',
    });

    return data;
  } catch (error: any) {
    console.error('Error in createCustomizationRequest:', error);
    toast({
      title: 'Error submitting request',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return null;
  }
};

/**
 * Get all customization requests (admin only)
 * @param status Optional status filter
 * @returns Array of customization requests
 */
export const getCustomizationRequests = async (status?: string): Promise<CustomizationRequest[]> => {
  try {
    let query = supabase
      .from('customization_requests')
      .select(`
        *,
        product:products(name, images:product_images(image_url))
      `);

    if (status) {
      query = query.eq('status', status);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching customization requests:', error);
      return [];
    }

    // Process the data to match our interface
    return data.map((item: any) => ({
      ...item,
      product: {
        name: item.product?.name || '',
        image: item.product?.images?.[0]?.image_url || '',
      }
    }));
  } catch (error) {
    console.error('Error in getCustomizationRequests:', error);
    return [];
  }
};

/**
 * Get a user's customization requests
 * @param userId The user ID
 * @returns Array of the user's customization requests
 */
export const getUserCustomizationRequests = async (userId: string): Promise<CustomizationRequest[]> => {
  try {
    const { data, error } = await supabase
      .from('customization_requests')
      .select(`
        *,
        product:products(name, images:product_images(image_url))
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user customization requests:', error);
      return [];
    }

    // Process the data to match our interface
    return data.map((item: any) => ({
      ...item,
      product: {
        name: item.product?.name || '',
        image: item.product?.images?.[0]?.image_url || '',
      }
    }));
  } catch (error) {
    console.error('Error in getUserCustomizationRequests:', error);
    return [];
  }
};

/**
 * Update a customization request status (admin only)
 * @param requestId The request ID
 * @param status The new status
 * @returns True if update was successful, false otherwise
 */
export const updateCustomizationRequestStatus = async (
  requestId: string, 
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('customization_requests')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', requestId);

    if (error) {
      console.error('Error updating customization request:', error);
      toast({
        title: 'Error updating request',
        description: error.message || 'An unexpected error occurred',
        variant: 'destructive',
      });
      return false;
    }

    toast({
      title: 'Request updated',
      description: `Request status has been updated to ${status}`,
    });

    return true;
  } catch (error: any) {
    console.error('Error in updateCustomizationRequestStatus:', error);
    toast({
      title: 'Error updating request',
      description: error.message || 'An unexpected error occurred',
      variant: 'destructive',
    });
    return false;
  }
};
