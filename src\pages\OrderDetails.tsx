import { useEffect, useState } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Loader2, ArrowLeft, Truck, Package, CheckCircle, XCircle, Clock, AlertCircle, Star } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/context/SupabaseAuthContext';
import { Order, getOrderById } from '@/services/orderService';
import { PurchasableProduct, getUserPurchasableProducts } from '@/services/productReviewsService';
import ReviewModal from '@/components/orders/ReviewModal';
import OrderTracker from '@/components/orders/OrderTracker';

// Status colors for badges
const statusColors = {
  pending: 'bg-amber-100 text-amber-800',
  processing: 'bg-purple-100 text-purple-800',
  shipped: 'bg-blue-100 text-blue-800',
  delivered: 'bg-green-100 text-green-800',
  canceled: 'bg-red-100 text-red-800'
};

// Status icons
const statusIcons = {
  pending: <Clock className="h-5 w-5 mr-2" />,
  processing: <Package className="h-5 w-5 mr-2" />,
  shipped: <Truck className="h-5 w-5 mr-2" />,
  delivered: <CheckCircle className="h-5 w-5 mr-2" />,
  canceled: <XCircle className="h-5 w-5 mr-2" />
};

const OrderDetails = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();

  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [purchasableProducts, setPurchasableProducts] = useState<PurchasableProduct[]>([]);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<{ id: string; name: string } | null>(null);

  // Fetch order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId || !user?.id) return;

      setIsLoading(true);
      try {
        const orderData = await getOrderById(orderId);
        if (orderData) {
          // Check if this order belongs to the current user
          if (orderData.user_id !== user.id) {
            toast({
              title: 'Access denied',
              description: 'You do not have permission to view this order.',
              variant: 'destructive'
            });
            navigate('/orders');
            return;
          }

          setOrder(orderData);

          // Fetch purchasable products to check which ones can be reviewed
          const purchasableData = await getUserPurchasableProducts(user.id);
          setPurchasableProducts(purchasableData);
        } else {
          toast({
            title: 'Order not found',
            description: 'The requested order could not be found.',
            variant: 'destructive'
          });
          navigate('/orders');
        }
      } catch (error) {
        console.error('Error fetching order details:', error);
        toast({
          title: 'Error',
          description: 'Failed to load order details. Please try again.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Check if user is authenticated
    if (!isAuthenticated) {
      navigate('/login?redirect=/orders');
    } else {
      fetchOrderDetails();
    }
  }, [orderId, isAuthenticated, user, navigate]);

  // Refresh purchasable products after submitting a review
  const fetchPurchasableProducts = async () => {
    if (!user?.id) return;

    try {
      console.log(`Refreshing purchasable products for user ${user.id}`);
      const purchasableData = await getUserPurchasableProducts(user.id);
      console.log('Fetched purchasable products:', purchasableData);
      setPurchasableProducts(purchasableData);
    } catch (error) {
      console.error('Error fetching purchasable products:', error);
      toast({
        title: 'Error',
        description: 'Failed to load reviewable products. Please try again.',
        variant: 'destructive'
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow container mx-auto px-4 py-8 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <Loader2 className="h-8 w-8 animate-spin text-badhees-600 mb-4" />
            <p className="text-badhees-600">Loading order details...</p>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <div className="flex-grow container mx-auto px-4 py-8">
          <div className="text-center py-12 bg-badhees-50 rounded-lg">
            <AlertCircle className="h-12 w-12 mx-auto text-badhees-600 mb-4" />
            <h2 className="text-2xl font-bold text-badhees-800 mb-2">Order Not Found</h2>
            <p className="text-badhees-600 mb-6">The order you're looking for doesn't exist or you don't have permission to view it.</p>
            <Button asChild>
              <Link to="/orders">Back to Orders</Link>
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />
      <div className="flex-grow container mx-auto px-4 py-8">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="sm" className="mr-4" onClick={() => navigate('/orders')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
          <h1 className="text-2xl font-bold">Order Details</h1>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Order Summary */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Order #{order.id.substring(0, 8)}</CardTitle>
                  <CardDescription>
                    Placed on {new Date(order.created_at).toLocaleDateString()} at {new Date(order.created_at).toLocaleTimeString()}
                  </CardDescription>
                </div>
                <Badge className={statusColors[order.status]}>
                  {statusIcons[order.status]}
                  {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Order Status Tracker */}
                <div className="mb-8">
                  <h3 className="font-medium text-lg mb-4">Order Status</h3>
                  <OrderTracker status={order.status} />
                </div>

                {/* Order Items */}
                <div>
                  <h3 className="font-medium text-lg mb-3">Order Items</h3>
                  <div className="space-y-4">
                    {order.order_items?.map((item) => (
                      <div key={item.id} className="flex items-start border-b pb-4">
                        <div className="w-16 h-16 bg-badhees-100 rounded overflow-hidden mr-4 flex-shrink-0">
                          <img
                            src={item.product?.image_url || '/placeholder-product.jpg'}
                            alt={item.product?.name || 'Product'}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div className="flex-grow">
                          <div className="flex justify-between">
                            <h4 className="font-medium">{item.product?.name || 'Product'}</h4>
                            <p className="font-medium">₹{(item.price * item.quantity).toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</p>
                          </div>
                          <div className="flex justify-between text-sm text-badhees-500 mt-1">
                            <p>Quantity: {item.quantity}</p>
                            <p>₹{item.price.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })} each</p>
                          </div>

                          {/* Review button */}
                          {(order.status === 'delivered' || order.status === 'shipped') && (
                            <div className="mt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="flex items-center gap-1"
                                onClick={() => {
                                  setSelectedProduct({
                                    id: item.product_id,
                                    name: item.product?.name || 'Product'
                                  });
                                  setIsReviewModalOpen(true);
                                }}
                                disabled={purchasableProducts.some(p =>
                                  p.productId === item.product_id && p.hasReviewed
                                )}
                              >
                                <Star className="h-4 w-4" />
                                {purchasableProducts.some(p =>
                                  p.productId === item.product_id && p.hasReviewed
                                ) ? 'Reviewed' : 'Review Product'}
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Order Summary */}
                <div className="bg-badhees-50 p-4 rounded-lg">
                  <div className="flex justify-between mb-2">
                    <span>Subtotal:</span>
                    <span>₹{order.total_amount.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span>Shipping:</span>
                    <span>Free</span>
                  </div>
                  <Separator className="my-2" />
                  <div className="flex justify-between font-bold">
                    <span>Total:</span>
                    <span>₹{order.total_amount.toLocaleString('en-IN', { maximumFractionDigits: 2, minimumFractionDigits: 2 })}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Order Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Order Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Order ID</h3>
                    <p>{order.id}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Order Date</h3>
                    <p>{new Date(order.created_at).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-badhees-500">Payment Method</h3>
                    <p>{order.payment_method || 'Not specified'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Shipping Address</CardTitle>
              </CardHeader>
              <CardContent>
                {order.shipping_address ? (
                  <div className="space-y-1">
                    <p>{order.shipping_address.name}</p>
                    <p>{order.shipping_address.street}</p>
                    <p>
                      {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postal_code}
                    </p>
                    <p>{order.shipping_address.country}</p>
                  </div>
                ) : (
                  <p className="text-badhees-500">No shipping address provided</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Billing Address</CardTitle>
              </CardHeader>
              <CardContent>
                {order.billing_address ? (
                  <div className="space-y-1">
                    <p>{order.billing_address.name}</p>
                    <p>{order.billing_address.street}</p>
                    <p>
                      {order.billing_address.city}, {order.billing_address.state} {order.billing_address.postal_code}
                    </p>
                    <p>{order.billing_address.country}</p>
                  </div>
                ) : (
                  <p className="text-badhees-500">No billing address provided</p>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Need Help?</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-badhees-500 mb-4">
                  If you have any questions or concerns about your order, please contact our customer support.
                </p>
                <Button asChild className="w-full">
                  <Link to="/contact">Contact Support</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <Footer />

      {/* Review Modal */}
      {selectedProduct && (
        <ReviewModal
          isOpen={isReviewModalOpen}
          onClose={() => setIsReviewModalOpen(false)}
          productId={selectedProduct.id}
          productName={selectedProduct.name}
          onReviewSubmitted={() => {
            // Refresh the purchasable products list
            fetchPurchasableProducts();
          }}
        />
      )}
    </div>
  );
};

export default OrderDetails;
