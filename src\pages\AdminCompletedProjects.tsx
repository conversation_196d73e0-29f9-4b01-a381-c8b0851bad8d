
import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/context/SupabaseAuthContext";
import { supabase } from "@/lib/supabase";
import AdminSidebar from "@/components/admin/AdminSidebar";
import PageContainer from "@/components/layout/PageContainer";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Plus, Trash, Pencil, Eye, FileText, Home, LayoutDashboard, BoxIcon } from "lucide-react";
import { getCustomProjects, createCustomProject, updateCustomProject, deleteCustomProject, CustomProject } from "@/services/customProjectService";

import CompletedProjectForm from "@/components/admin/CompletedProjectForm";
import { ProjectType } from "@/types/project";

const AdminCompletedProjects = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState<CustomProject | null>(null);

  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { isAuthenticated, isAdmin } = useAuth();



  // Fetch projects data from Supabase
  const { data: projects = [], isLoading, refetch } = useQuery({
    queryKey: ["adminCompletedProjects"],
    queryFn: () => getCustomProjects(),
    enabled: isAuthenticated && isAdmin(),
    // Ensure we get fresh data when the component mounts or when the query is invalidated
    staleTime: 0,
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

  // Handle save project
  const handleSaveProject = async (projectData: CustomProject) => {
    try {
      const result = await createCustomProject(projectData);

      if (result) {
        toast({
          title: "Success",
          description: "Project saved successfully",
        });

        queryClient.invalidateQueries({ queryKey: ["adminCompletedProjects"] });
        setIsAddDialogOpen(false);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to save project",
        variant: "destructive",
      });
    }
  };

  // Handle update project
  const handleUpdateProject = async (projectData: Partial<CustomProject>) => {
    if (!selectedProject) return;

    try {
      const result = await updateCustomProject(selectedProject.id, projectData);

      if (result) {
        toast({
          title: "Success",
          description: "Project updated successfully",
        });

        queryClient.invalidateQueries({ queryKey: ["adminCompletedProjects"] });
        setIsEditDialogOpen(false);
        setSelectedProject(null);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update project",
        variant: "destructive",
      });
    }
  };

  // Handle delete project
  const handleDeleteProject = async () => {
    if (!selectedProject) return;

    try {
      // Pass true for permanent deletion if the project is already archived
      const isPermanentDelete = selectedProject.status === 'archived';
      const result = await deleteCustomProject(selectedProject.id, isPermanentDelete);

      if (result) {
        toast({
          title: "Success",
          description: isPermanentDelete
            ? "Project permanently deleted successfully"
            : "Project archived successfully",
        });

        queryClient.invalidateQueries({ queryKey: ["adminCompletedProjects"] });
        setIsDeleteDialogOpen(false);
        setSelectedProject(null);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete project",
        variant: "destructive",
      });
    }
  };

  // Check if user is authenticated and has admin privileges
  useEffect(() => {
    if (!isAuthenticated) {
      navigate("/login?redirect=/admin/completed-projects");
      toast({
        title: "Access Denied",
        description: "Please login to access the admin panel",
        variant: "destructive",
      });
    } else if (!isAdmin()) {
      navigate("/");
      toast({
        title: "Permission Denied",
        description: "You do not have permission to access the admin panel",
        variant: "destructive",
      });
    }
  }, [isAuthenticated, isAdmin, navigate]);

  // Set up real-time subscription to project changes
  useEffect(() => {
    if (!isAuthenticated || !isAdmin()) return;

    // Subscribe to changes in the custom_projects table
    const subscription = supabase
      .channel('custom-projects-changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'custom_projects' },
        () => {
          console.log('Projects data changed, refetching...');
          refetch();
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [isAuthenticated, isAdmin, refetch]);

  return (
    <div className="flex min-h-screen bg-gray-100">
      <AdminSidebar />

      <main className="flex-1 overflow-auto">
        <div className="p-4 md:p-8">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6">
            <div>
              <h1 className="text-2xl font-bold text-badhees-800">Completed Projects</h1>
              <p className="text-sm text-gray-500 mt-1">Manage and organize your custom interior projects</p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                onClick={() => navigate('/admin/dashboard')}
                size="sm"
                className="hidden md:flex"
              >
                <LayoutDashboard className="h-4 w-4 mr-2" />
                Dashboard
              </Button>
              <Button
                variant="outline"
                onClick={() => navigate('/admin/products')}
                size="sm"
                className="hidden md:flex"
              >
                <BoxIcon className="h-4 w-4 mr-2" />
                Products
              </Button>
              <Button onClick={() => setIsAddDialogOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Custom Project
              </Button>
            </div>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="bg-white rounded-lg shadow p-4 flex items-center">
              <div className="bg-badhees-100 p-2 rounded-full mr-4">
                <FileText className="h-6 w-6 text-badhees-accent" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Total Projects</p>
                <p className="text-2xl font-semibold">{projects.length}</p>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow p-4 flex items-center">
              <div className="bg-green-100 p-2 rounded-full mr-4">
                <Eye className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Active Projects</p>
                <p className="text-2xl font-semibold">{projects.filter(p => p.status === 'published').length}</p>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow p-4 flex items-center">
              <div className="bg-orange-100 p-2 rounded-full mr-4">
                <Pencil className="h-6 w-6 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Draft Projects</p>
                <p className="text-2xl font-semibold">{projects.filter(p => p.status === 'draft').length}</p>
              </div>
            </div>
            <div className="bg-white rounded-lg shadow p-4 flex items-center">
              <div className="bg-red-100 p-2 rounded-full mr-4">
                <Trash className="h-6 w-6 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-gray-500">Deleted Projects</p>
                <p className="text-2xl font-semibold">{projects.filter(p => p.status === 'archived').length}</p>
              </div>
            </div>
          </div>

          {/* Projects Table */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project Name</TableHead>
                    <TableHead className="hidden md:table-cell">Category</TableHead>
                    <TableHead className="hidden md:table-cell">Budget</TableHead>
                    <TableHead className="hidden lg:table-cell">Completion Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {projects.length > 0 ? (
                    projects.map((project) => (
                      <TableRow key={project.id}>
                        <TableCell className="font-medium">{project.name}</TableCell>
                        <TableCell className="hidden md:table-cell">{project.category}</TableCell>
                        <TableCell className="hidden md:table-cell">₹{project.budget ? project.budget.toLocaleString('en-IN') : 'N/A'}</TableCell>
                        <TableCell className="hidden lg:table-cell">{new Date(project.completion_date || '').toLocaleDateString()}</TableCell>
                        <TableCell>
                          <Badge variant={
                            project.status === "published" ? "default" :
                            project.status === "draft" ? "secondary" :
                            "destructive"
                          }>
                            {project.status === "published" ? "Published" :
                            project.status === "draft" ? "Draft" : "Archived"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Actions</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              {project.status !== 'archived' && (
                                <>
                                  <DropdownMenuItem asChild>
                                    <Link to={`/completed-projects/detail/${project.id}`} className="flex items-center">
                                      <Eye className="h-4 w-4 mr-2" />
                                      <span>View</span>
                                    </Link>
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setSelectedProject(project);
                                      setIsEditDialogOpen(true);
                                    }}
                                  >
                                    <Pencil className="h-4 w-4 mr-2" />
                                    <span>Edit</span>
                                  </DropdownMenuItem>
                                </>
                              )}
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedProject(project);
                                  setIsDeleteDialogOpen(true);
                                }}
                                className="text-red-600"
                              >
                                <Trash className="h-4 w-4 mr-2" />
                                <span>{project.status === 'archived' ? 'Permanently Delete' : 'Archive'}</span>
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-6 text-badhees-600">
                        No completed projects found. Click "Add Custom Project" to create one.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          <div className="mt-6 bg-badhees-50 rounded-lg p-4 border border-badhees-100">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div>
                <h3 className="font-medium text-badhees-800">Want to view your projects?</h3>
                <p className="text-sm text-badhees-600">Visit the custom interior projects page to see how they appear to customers</p>
              </div>
              <Button asChild variant="outline">
                <Link to="/custom-interiors">
                  <Home className="h-4 w-4 mr-2" />
                  View Projects Page
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </main>

      {/* Add Project Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add Custom Project</DialogTitle>
          </DialogHeader>
          <CompletedProjectForm
            onSave={handleSaveProject}
            onCancel={() => setIsAddDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Project Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Project</DialogTitle>
          </DialogHeader>
          {selectedProject && (
            <CompletedProjectForm
              initialData={selectedProject}
              onSave={handleUpdateProject}
              onCancel={() => {
                setIsEditDialogOpen(false);
                setSelectedProject(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will {selectedProject?.status === 'deleted' ? 'permanently ' : ''}delete the project "{selectedProject?.name}".
              {selectedProject?.status !== 'deleted' ? " The project will be removed from the website but can be restored later." : " This action cannot be undone."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteProject} className="bg-red-600 hover:bg-red-700">
              {selectedProject?.status === 'deleted' ? 'Permanently Delete' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default AdminCompletedProjects;
