
import { useEffect, useCallback } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import PageContainer from "@/components/layout/PageContainer";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Tag } from 'lucide-react';
import { getCustomProjectById, getCustomProjects, CustomProject } from '@/services/customProjectService';
import AutoChangingCarousel from '@/components/projects/AutoChangingCarousel';
import RelatedProjects from '@/components/projects/RelatedProjects';
import { PullToRefresh } from "@/components/ui/pull-to-refresh";
import { useTabVisibility } from "@/hooks/use-optimized-render";

const CompletedProjectDetail = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const queryClient = useQueryClient();

  // Handle tab visibility changes - refetch data when tab becomes visible
  const handleTabVisible = useCallback(() => {
    console.log('Tab became visible - refreshing project detail data');
    queryClient.invalidateQueries({ queryKey: ['project'] });
    queryClient.invalidateQueries({ queryKey: ['allPublishedProjects'] });
  }, [queryClient]);

  useTabVisibility(handleTabVisible);

  // Fetch project data from Supabase
  const { data: project, isLoading, error, refetch: refetchProject } = useQuery<CustomProject | null>({
    queryKey: ['project', projectId],
    queryFn: () => getCustomProjectById(projectId || ''),
    enabled: !!projectId,
    // Use global settings for better tab switching behavior
    // staleTime and refetchOnWindowFocus will be inherited from global config
  });

  // Fetch all published projects for recommendations
  const { data: allProjects = [], refetch: refetchAllProjects } = useQuery<CustomProject[]>({
    queryKey: ['allPublishedProjects'],
    queryFn: () => getCustomProjects('published'),
    // Use global settings for better tab switching behavior
    // staleTime and refetchOnWindowFocus will be inherited from global config
  });

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    try {
      // Invalidate and refetch project data
      await queryClient.invalidateQueries({ queryKey: ['project'] });
      await queryClient.invalidateQueries({ queryKey: ['allPublishedProjects'] });

      // Refetch current data
      await refetchProject();
      await refetchAllProjects();
    } catch (error) {
      console.error('Refresh failed:', error);
    }
  }, [queryClient, refetchProject, refetchAllProjects]);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [projectId]);

  if (isLoading) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
          <div className="pt-28 pb-16 flex items-center justify-center">
            <div className="text-center">
              <p className="text-badhees-600">Loading project details...</p>
            </div>
          </div>
          <Footer />
        </PullToRefresh>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
          <div className="pt-28 pb-16">
            <PageContainer className="text-center">
              <h1 className="text-2xl font-bold text-badhees-800 mb-4">Project Not Found</h1>
              <p className="text-badhees-600 mb-6">The project you're looking for could not be found or has been removed.</p>
              <Button asChild>
                <Link to="/custom-interiors">Browse All Services</Link>
              </Button>
            </PageContainer>
          </div>
          <Footer />
        </PullToRefresh>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <Navbar />

      <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
        <div className="pt-28 pb-16">
          <PageContainer>
          <div className="mb-8 text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-badhees-800 mb-3">
              {project.name}
            </h1>
            <p className="text-lg text-badhees-600 max-w-2xl mx-auto">
              {project.description?.split('.')[0]}.
            </p>
            <div className="w-20 h-1 bg-badhees-accent mx-auto mt-6"></div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12">
            {/* Project Images - Left Side */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl overflow-hidden shadow-md border border-badhees-100">
                {/* Simplified image rendering logic */}
                {Array.isArray(project.image_urls) && project.image_urls.length > 0 ? (
                  <AutoChangingCarousel images={project.image_urls} projectName={project.name} />
                ) : typeof project.image_urls === 'string' && project.image_urls && (project.image_urls as string).trim() !== '' ? (
                  <img
                    src={project.image_urls}
                    alt={project.name}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      console.log('Image load error, using placeholder');
                      e.currentTarget.src = '/placeholder-image.jpg';
                    }}
                  />
                ) : (
                  <div className="aspect-w-16 aspect-h-10 flex items-center justify-center rounded-lg bg-badhees-100">
                    <img
                      src="/placeholder-image.jpg"
                      alt={project.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
              </div>

              {/* Video Section (if available) */}
              {project.video_url && (
                <div className="mt-8">
                  <h3 className="text-xl font-semibold text-badhees-800 mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-badhees-accent" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Project Video
                  </h3>
                  <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden shadow-md border border-badhees-100">
                    <iframe
                      src={project.video_url}
                      title={`${project.name} Video`}
                      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                      allowFullScreen
                      className="w-full h-full"
                    ></iframe>
                  </div>
                </div>
              )}

              {/* Project Description - Mobile Only */}
              <div className="mt-6 lg:hidden">
                <h3 className="text-xl font-semibold text-badhees-800 mb-2">Project Description</h3>
                <p className="text-badhees-600">{project.description}</p>

                <Button
                  className="w-full mt-6 bg-badhees-accent hover:bg-badhees-accent/90"
                  asChild
                >
                  <Link to="/custom-interiors#contact-section">
                    Request Similar Project
                  </Link>
                </Button>
              </div>
            </div>

            {/* Project Details - Right Side */}
            <div className="lg:col-span-1">
              <div className="bg-white p-4 md:p-6 rounded-xl shadow-sm border border-badhees-100 lg:sticky lg:top-28">
                <h1 className="text-2xl md:text-3xl font-bold text-badhees-800 mb-3">{project.name}</h1>

                <div className="flex items-center text-sm text-badhees-600 mb-4">
                  <Tag className="h-4 w-4 mr-1" />
                  <span>{project.category}</span>
                </div>

                <Separator className="my-4" />

                <div className="space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-badhees-600">Budget:</span>
                    <span className="font-semibold text-badhees-800">₹{project.budget ? project.budget.toLocaleString('en-IN') : 'N/A'}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-badhees-600">Completion Date:</span>
                    <span className="font-semibold text-badhees-800">
                      {project.completion_date ? new Date(project.completion_date).toLocaleDateString() : 'N/A'}
                    </span>
                  </div>

                  {project.client_name && (
                    <div className="flex justify-between">
                      <span className="text-badhees-600">Client:</span>
                      <span className="font-semibold text-badhees-800">{project.client_name}</span>
                    </div>
                  )}

                  {project.location && (
                    <div className="flex justify-between">
                      <span className="text-badhees-600">Location:</span>
                      <span className="font-semibold text-badhees-800">{project.location}</span>
                    </div>
                  )}

                  {project.materials_used && (
                    <div className="flex flex-col">
                      <span className="text-badhees-600 mb-1">Materials:</span>
                      <span className="font-semibold text-badhees-800">{project.materials_used}</span>
                    </div>
                  )}
                </div>

                <Separator className="my-4" />

                <div className="mb-6 hidden lg:block">
                  <h3 className="text-lg font-semibold text-badhees-800 mb-2">Project Description</h3>
                  <p className="text-badhees-600">{project.description}</p>
                </div>

                <Button
                  asChild
                  className="w-full bg-badhees-accent hover:bg-badhees-accent/90 hidden lg:flex"
                >
                  <Link to="/custom-interiors#contact-section">
                    Request Similar Project
                  </Link>
                </Button>
              </div>
            </div>
          </div>

          {/* Ready to start your project section */}
          <div className="mt-16">
            <div className="bg-badhees-50 p-8 rounded-xl border border-badhees-100 shadow-sm">
              <div className="flex flex-col md:flex-row items-center justify-between gap-8">
                <div className="text-center md:text-left">
                  <h2 className="text-2xl font-bold text-badhees-800 mb-3">Ready to start your project?</h2>
                  <p className="text-badhees-600 max-w-xl">
                    Let's bring your vision to life. Our team of experts will work closely with you to create a custom solution that meets your needs and exceeds your expectations.
                  </p>
                </div>
                <div className="flex-shrink-0">
                  <Button
                    asChild
                    size="lg"
                    className="bg-badhees-accent hover:bg-badhees-accent/90 shadow-md hover:shadow-lg transition-all"
                  >
                    <Link to="/custom-interiors#contact-section" className="flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
                      </svg>
                      Get in Touch
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Related Projects Section - Same Category */}
          {project && allProjects.length > 0 && (
            <RelatedProjects
              currentProjectId={project.id || ''}
              projects={allProjects}
              category={project.category}
            />
          )}

          {/* Explore Other Categories Section */}
          {project && allProjects.length > 0 && (
            <div className="mt-16 pt-8 border-t border-badhees-100">
              <h2 className="text-2xl font-bold text-badhees-800 mb-6">
                Explore Other Categories
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
                {allProjects
                  .filter(p => p.id !== project.id && p.category !== project.category)
                  .slice(0, 3)
                  .map((project) => (
                    <Link
                      key={project.id}
                      to={`/completed-projects/detail/${project.id}`}
                      className="block group"
                    >
                      <div className="bg-white rounded-xl overflow-hidden shadow-md border border-badhees-100 hover:shadow-lg transition-all duration-300 h-full flex flex-col group-hover:border-badhees-accent">
                        <div className="relative w-full h-[180px] md:h-[200px] overflow-hidden bg-badhees-100">
                          {Array.isArray(project.image_urls) && project.image_urls.length > 0 ? (
                            <img
                              src={project.image_urls[0]}
                              alt={project.name}
                              className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                              onError={(e) => {
                                e.currentTarget.src = '/placeholder-image.jpg';
                              }}
                              loading="lazy"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-badhees-100">
                              <span className="text-badhees-600">No Image</span>
                            </div>
                          )}
                          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        <div className="p-3 md:p-4 flex-1 flex flex-col">
                          <div className="mb-2">
                            <span className="text-xs font-medium bg-badhees-100 text-badhees-800 px-2 py-1 rounded-full">
                              {project.category}
                            </span>
                          </div>
                          <h3 className="text-base md:text-lg font-semibold text-badhees-800 mb-1 md:mb-2 group-hover:text-badhees-accent transition-colors">
                            {project.name}
                          </h3>
                          <p className="text-xs md:text-sm text-badhees-600 line-clamp-2 mb-2 md:mb-3 flex-1">
                            {project.description}
                          </p>
                          <div className="flex justify-end">
                            <span className="inline-flex items-center text-xs md:text-sm text-badhees-accent font-medium group-hover:translate-x-1 transition-transform">
                              View Project
                            </span>
                          </div>
                        </div>
                      </div>
                    </Link>
                  ))}
              </div>

              <div className="mt-8 text-center">
                <Button asChild className="bg-badhees-accent hover:bg-badhees-accent/90">
                  <Link to="/custom-interiors">
                    View All Projects
                  </Link>
                </Button>
              </div>
            </div>
          )}
          </PageContainer>
        </div>

        <Footer />
      </PullToRefresh>
    </div>
  );
};

export default CompletedProjectDetail;
