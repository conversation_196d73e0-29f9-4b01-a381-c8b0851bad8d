import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/lib/supabase';
import { Loader2 } from 'lucide-react';

const AuthRedirect: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    const handleAuthRedirect = async () => {
      // Get the hash fragment from the URL
      const hash = window.location.hash;
      
      // Check if this is an auth redirect
      if (hash && (hash.includes('access_token') || hash.includes('error'))) {
        try {
          // Process the hash fragment
          const { data, error } = await supabase.auth.getSession();
          
          if (error) {
            console.error('Auth redirect error:', error);
            navigate('/login?error=auth_error');
            return;
          }
          
          if (data.session) {
            // Successfully authenticated
            navigate('/profile');
          } else {
            // No session found
            navigate('/login');
          }
        } catch (err) {
          console.error('Error processing auth redirect:', err);
          navigate('/login?error=unknown');
        }
      } else {
        // Not an auth redirect, go to home
        navigate('/');
      }
    };

    handleAuthRedirect();
  }, [navigate]);

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <Loader2 className="h-8 w-8 animate-spin text-primary" />
      <p className="mt-4 text-muted-foreground">Processing authentication...</p>
    </div>
  );
};

export default AuthRedirect;
