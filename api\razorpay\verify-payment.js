import crypto from 'crypto';

export default async function handler(req, res) {
  // Set CORS headers first
  const origin = req.headers.origin;
  console.log('🌐 Verify Payment - Request origin:', origin);

  // Allow requests from any origin that includes thebadhees.com or localhost
  if (origin && (
    origin.includes('thebadhees.com') ||
    origin.includes('localhost') ||
    origin.includes('127.0.0.1') ||
    origin.includes('vercel.app')
  )) {
    res.setHeader('Access-Control-Allow-Origin', origin);
  } else {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token, Origin');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    console.log('✅ Handling OPTIONS preflight request');
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  // Validate environment variables first
  console.log('🔧 Checking environment variables...');
  console.log('RAZORPAY_SECRET exists:', !!process.env.RAZORPAY_SECRET);
  console.log('VITE_SUPABASE_URL exists:', !!process.env.VITE_SUPABASE_URL);
  console.log('VITE_SUPABASE_ANON_KEY exists:', !!process.env.VITE_SUPABASE_ANON_KEY);

  if (!process.env.RAZORPAY_SECRET) {
    console.error('❌ Missing RAZORPAY_SECRET environment variable');
    return res.status(500).json({
      success: false,
      error: 'Server configuration error: Missing payment credentials'
    });
  }

  if (!process.env.VITE_SUPABASE_URL || !process.env.VITE_SUPABASE_ANON_KEY) {
    console.error('❌ Missing Supabase environment variables');
    return res.status(500).json({
      success: false,
      error: 'Server configuration error: Missing database credentials'
    });
  }

  try {
    console.log('✅ Received payment verification request');
    console.log('📋 Request body:', JSON.stringify(req.body, null, 2));

    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      order_id
    } = req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.error('❌ Missing required parameters:', {
        razorpay_order_id: !!razorpay_order_id,
        razorpay_payment_id: !!razorpay_payment_id,
        razorpay_signature: !!razorpay_signature
      });
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Missing required parameters'
      });
    }

    console.log('✅ All required parameters present');

    // Get the secret key from environment variables
    const secret = process.env.RAZORPAY_SECRET;

    // Create signature for verification
    const shasum = crypto.createHmac('sha256', secret);
    shasum.update(`${razorpay_order_id}|${razorpay_payment_id}`);
    const digest = shasum.digest('hex');

    console.log('🔐 Generated signature:', digest);
    console.log('📨 Received signature:', razorpay_signature);
    console.log('🔍 Signature match:', digest === razorpay_signature);

    // Verify signature
    if (digest !== razorpay_signature) {
      console.error('❌ Invalid signature - payment verification failed');
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Invalid signature'
      });
    }

    console.log('✅ Payment signature verified successfully');

    // Update database records
    try {
      console.log('💾 Initializing Supabase client for database updates');
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL,
        process.env.VITE_SUPABASE_ANON_KEY
      );

      console.log('💳 Updating payment record in database');
      // Update payment record
      const { error: paymentError } = await supabase
        .from('razorpay_payments')
        .update({
          razorpay_payment_id: razorpay_payment_id,
          status: 'captured',
          method: 'online',
          error_description: null,
          updated_at: new Date().toISOString()
        })
        .eq('razorpay_order_id', razorpay_order_id);

      if (paymentError) {
        console.error('❌ Error updating payment record:', paymentError);
        // Don't fail the verification for payment record update errors
      } else {
        console.log('✅ Payment record updated successfully');
      }

      // Update order if order_id is provided and it's not a temporary order
      if (order_id && !order_id.startsWith('temp_')) {
        console.log('📦 Updating order with payment details:', order_id);
        const { error: orderError } = await supabase
          .from('orders')
          .update({
            razorpay_payment_id: razorpay_payment_id,
            payment_status: 'paid',
            status: 'paid',
            updated_at: new Date().toISOString()
          })
          .eq('id', order_id);

        if (orderError) {
          console.error('❌ Error updating order:', orderError);
          // Don't fail the verification for order update errors
        } else {
          console.log('✅ Order updated successfully with payment ID');
        }
      } else if (order_id && order_id.startsWith('temp_')) {
        console.log('⏳ Temporary order detected, skipping order update:', order_id);
      } else {
        console.log('⚠️ No order_id provided, skipping order update');
      }
    } catch (dbError) {
      console.error('❌ Database update error:', dbError);
      // Don't fail the verification if DB update fails
    }

    console.log('🎉 Payment verification completed successfully');
    const response = {
      success: true,
      message: 'Payment verified successfully',
      order_id: order_id,
      payment_id: razorpay_payment_id
    };
    console.log('📤 Sending success response');

    return res.status(200).json(response);
  } catch (error) {
    console.error('❌ Error verifying payment:', error);
    console.error('❌ Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    const errorResponse = {
      success: false,
      error: error.message || 'Failed to verify payment'
    };
    console.log('📤 Sending error response');

    return res.status(500).json(errorResponse);
  }
}
