import crypto from 'crypto';

export default async function handler(req, res) {
  // Set CORS headers for thebadhees.com
  const origin = req.headers.origin;
  if (origin === 'https://thebadhees.com' || origin === 'http://localhost:5173') {
    res.setHeader('Access-Control-Allow-Origin', origin);
  }
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Date, X-Api-Version, X-CSRF-Token');
  res.setHeader('Access-Control-Max-Age', '86400');

  // Handle OPTIONS request (preflight)
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, error: 'Method not allowed' });
  }

  try {
    console.log('Received payment verification request:', req.body);

    const {
      razorpay_order_id,
      razorpay_payment_id,
      razorpay_signature,
      order_id
    } = req.body;

    if (!razorpay_order_id || !razorpay_payment_id || !razorpay_signature) {
      console.log('Missing required parameters');
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Missing required parameters'
      });
    }

    // Validate environment variables
    if (!process.env.RAZORPAY_SECRET) {
      console.error('Missing RAZORPAY_SECRET environment variable');
      return res.status(500).json({
        success: false,
        error: 'Server configuration error: Missing payment credentials'
      });
    }

    if (!process.env.VITE_SUPABASE_URL || !process.env.VITE_SUPABASE_ANON_KEY) {
      console.error('Missing Supabase environment variables');
      return res.status(500).json({
        success: false,
        error: 'Server configuration error: Missing database credentials'
      });
    }

    // Get the secret key from environment variables
    const secret = process.env.RAZORPAY_SECRET;

    // Create signature
    const shasum = crypto.createHmac('sha256', secret);
    shasum.update(`${razorpay_order_id}|${razorpay_payment_id}`);
    const digest = shasum.digest('hex');

    console.log('Generated signature:', digest);
    console.log('Received signature:', razorpay_signature);

    // Verify signature
    if (digest !== razorpay_signature) {
      console.log('Invalid signature');
      return res.status(400).json({
        success: false,
        error: 'Payment verification failed: Invalid signature'
      });
    }

    console.log('Payment verified successfully');

    // Update payment record in database
    try {
      // Use dynamic import for Supabase
      const { createClient } = await import('@supabase/supabase-js');
      const supabase = createClient(
        process.env.VITE_SUPABASE_URL,
        process.env.VITE_SUPABASE_ANON_KEY
      );

      // Try to update payment record using direct table update (fallback)
      try {
        const { error: paymentError } = await supabase
          .from('razorpay_payments')
          .update({
            razorpay_payment_id: razorpay_payment_id,
            status: 'captured',
            method: 'online',
            error_description: null,
            updated_at: new Date().toISOString()
          })
          .eq('razorpay_order_id', razorpay_order_id);

        if (paymentError) {
          console.error('Error updating payment record:', paymentError);
        } else {
          console.log('Payment record updated successfully');
        }
      } catch (paymentUpdateError) {
        console.error('Failed to update payment record:', paymentUpdateError);
      }

      // Update order if order_id is provided
      if (order_id && !order_id.startsWith('temp_')) {
        const { error: orderError } = await supabase
          .from('orders')
          .update({
            razorpay_payment_id: razorpay_payment_id,
            payment_status: 'paid',
            status: 'paid',
            updated_at: new Date().toISOString()
          })
          .eq('id', order_id);

        if (orderError) {
          console.error('Error updating order:', orderError);
        } else {
          console.log('Order updated successfully with payment ID');
        }
      }
    } catch (dbError) {
      console.error('Database update error:', dbError);
      // Don't fail the verification if DB update fails
    }

    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      order_id: order_id,
      payment_id: razorpay_payment_id
    });
  } catch (error) {
    console.error('Error verifying payment:', error);
    return res.status(500).json({
      success: false,
      error: error.message || 'Failed to verify payment'
    });
  }
}
