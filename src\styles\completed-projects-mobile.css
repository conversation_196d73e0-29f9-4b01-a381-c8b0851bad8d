/* Mobile optimizations for Completed Projects page */

/* Ensure consistent image sizes across all project cards */
@media (max-width: 640px) {
  /* Reduce spacing between elements */
  .project-card-image {
    height: 180px !important;
    overflow: hidden;
  }

  /* Ensure all images have the same fixed height */
  .project-card-image img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }

  /* Optimize card spacing */
  .project-card {
    margin-bottom: 16px !important;
  }

  /* Reduce padding inside cards */
  .project-card-content {
    padding: 12px !important;
  }

  /* Smaller text for mobile */
  .project-card-title {
    font-size: 16px !important;
    margin-bottom: 4px !important;
  }

  .project-card-description {
    font-size: 14px !important;
    line-height: 1.4 !important;
    margin-bottom: 8px !important;
  }

  /* Optimize metadata display */
  .project-card-metadata {
    font-size: 12px !important;
    gap: 8px !important;
  }

  .project-card-metadata-icon {
    width: 14px !important;
    height: 14px !important;
  }

  /* Optimize view details link */
  .project-card-link {
    font-size: 12px !important;
  }

  .project-card-link-icon {
    width: 14px !important;
    height: 14px !important;
  }

  /* Reduce grid gap */
  .projects-grid {
    gap: 12px !important;
  }

  /* Optimize page header */
  .projects-page-title {
    font-size: 24px !important;
    margin-bottom: 8px !important;
  }

  .projects-page-description {
    font-size: 14px !important;
    line-height: 1.4 !important;
  }

  /* Optimize empty state */
  .projects-empty-state {
    padding: 24px 16px !important;
  }

  .projects-empty-state-icon {
    width: 48px !important;
    height: 48px !important;
    margin-bottom: 12px !important;
  }

  .projects-empty-state-title {
    font-size: 18px !important;
    margin-bottom: 8px !important;
  }

  .projects-empty-state-description {
    font-size: 14px !important;
    margin-bottom: 16px !important;
  }

  /* Optimize CTA section */
  .projects-cta-section {
    padding: 16px !important;
    margin-top: 24px !important;
  }

  .projects-cta-title {
    font-size: 18px !important;
    margin-bottom: 8px !important;
  }

  .projects-cta-description {
    font-size: 14px !important;
    margin-bottom: 16px !important;
  }
}

/* Ensure consistent aspect ratio for project images */
.aspect-w-16.aspect-h-10 {
  position: relative;
  padding-bottom: 62.5%; /* 10/16 = 0.625 */
}

.aspect-w-16.aspect-h-10 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

/* Fix for image carousel in mobile */
@media (max-width: 640px) {
  .project-image-carousel {
    height: 180px !important;
  }
  
  .project-image-carousel img {
    object-position: center !important;
  }
}
