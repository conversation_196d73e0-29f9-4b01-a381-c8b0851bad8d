import React, { useState } from 'react';
import { Button } from '@/components/ui/button-system';
import { Input } from '@/components/ui/input';
import { getProductReviews, getProductRatingSummary, hasUserPurchasedProduct } from '@/services/productReviewsService';
import { useAuth } from '@/context/SupabaseAuthContext';

/**
 * Debug component to test product reviews functionality
 * This component helps diagnose issues with the reviews system
 */
export const ReviewsDebugger: React.FC = () => {
  const [productId, setProductId] = useState('');
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();

  const testReviewsSystem = async () => {
    if (!productId.trim()) {
      alert('Please enter a product ID');
      return;
    }

    setLoading(true);
    setResults(null);

    try {
      console.log('Testing reviews system for product:', productId);
      
      const testResults: any = {
        productId,
        timestamp: new Date().toISOString(),
        tests: {}
      };

      // Test 1: Get product reviews
      console.log('Test 1: Getting product reviews...');
      try {
        const reviews = await getProductReviews(productId);
        testResults.tests.getReviews = {
          success: true,
          data: reviews,
          count: reviews.length
        };
        console.log('✅ Get reviews successful:', reviews);
      } catch (error) {
        testResults.tests.getReviews = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
        console.error('❌ Get reviews failed:', error);
      }

      // Test 2: Get product rating summary
      console.log('Test 2: Getting product rating summary...');
      try {
        const summary = await getProductRatingSummary(productId);
        testResults.tests.getRatingSummary = {
          success: true,
          data: summary
        };
        console.log('✅ Get rating summary successful:', summary);
      } catch (error) {
        testResults.tests.getRatingSummary = {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
        console.error('❌ Get rating summary failed:', error);
      }

      // Test 3: Check if user has purchased product (only if user is logged in)
      if (user) {
        console.log('Test 3: Checking if user has purchased product...');
        try {
          const hasPurchased = await hasUserPurchasedProduct(productId, user.id);
          testResults.tests.hasPurchased = {
            success: true,
            data: hasPurchased
          };
          console.log('✅ Purchase check successful:', hasPurchased);
        } catch (error) {
          testResults.tests.hasPurchased = {
            success: false,
            error: error instanceof Error ? error.message : String(error)
          };
          console.error('❌ Purchase check failed:', error);
        }
      } else {
        testResults.tests.hasPurchased = {
          success: false,
          error: 'User not logged in'
        };
      }

      setResults(testResults);
    } catch (error) {
      console.error('Overall test failed:', error);
      setResults({
        productId,
        timestamp: new Date().toISOString(),
        overallError: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-md max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">Product Reviews System Debugger</h2>
      
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2">
          Product ID to test:
        </label>
        <div className="flex gap-2">
          <Input
            value={productId}
            onChange={(e) => setProductId(e.target.value)}
            placeholder="Enter product ID (UUID)"
            className="flex-1"
          />
          <Button 
            onClick={testReviewsSystem}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            {loading ? 'Testing...' : 'Test Reviews System'}
          </Button>
        </div>
      </div>

      {user ? (
        <p className="text-sm text-green-600 mb-4">
          ✅ User logged in: {user.email}
        </p>
      ) : (
        <p className="text-sm text-orange-600 mb-4">
          ⚠️ User not logged in (purchase checks will fail)
        </p>
      )}

      {results && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3">Test Results:</h3>
          <div className="bg-gray-100 p-4 rounded-lg">
            <pre className="text-sm overflow-auto max-h-96">
              {JSON.stringify(results, null, 2)}
            </pre>
          </div>
          
          <div className="mt-4 space-y-2">
            <h4 className="font-medium">Summary:</h4>
            {results.tests?.getReviews && (
              <div className={`p-2 rounded ${results.tests.getReviews.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                Get Reviews: {results.tests.getReviews.success ? '✅ Success' : '❌ Failed'}
                {results.tests.getReviews.success && ` (${results.tests.getReviews.count} reviews)`}
              </div>
            )}
            {results.tests?.getRatingSummary && (
              <div className={`p-2 rounded ${results.tests.getRatingSummary.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                Get Rating Summary: {results.tests.getRatingSummary.success ? '✅ Success' : '❌ Failed'}
              </div>
            )}
            {results.tests?.hasPurchased && (
              <div className={`p-2 rounded ${results.tests.hasPurchased.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                Purchase Check: {results.tests.hasPurchased.success ? '✅ Success' : '❌ Failed'}
              </div>
            )}
          </div>
        </div>
      )}

      <div className="mt-6 text-sm text-gray-600">
        <p><strong>Instructions:</strong></p>
        <ol className="list-decimal list-inside space-y-1">
          <li>Enter a valid product ID (UUID format)</li>
          <li>Click "Test Reviews System" to run all tests</li>
          <li>Check the console (F12) for detailed logs</li>
          <li>Review the results to identify any issues</li>
        </ol>
      </div>
    </div>
  );
};

export default ReviewsDebugger;
