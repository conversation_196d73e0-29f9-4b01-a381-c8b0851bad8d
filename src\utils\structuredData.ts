// Structured Data Schemas for SEO

export interface BusinessInfo {
  name: string;
  description: string;
  url: string;
  logo: string;
  image: string;
  telephone: string;
  email: string;
  address: {
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  socialMedia: {
    facebook?: string;
    instagram?: string;
    youtube?: string;
    whatsapp?: string;
  };
}

export const businessInfo: BusinessInfo = {
  name: 'The Badhees',
  description: 'Crafting timeless pieces that blend aesthetic appeal with functional design, creating spaces that reflect your unique personality.',
  url: 'https://www.thebadhees.com',
  logo: 'https://www.thebadhees.com/logo.avif',
  image: 'https://www.thebadhees.com/og-image.png',
  telephone: '+91-8197705438',
  email: '<EMAIL>',
  address: {
    streetAddress: '',
    addressLocality: '',
    addressRegion: '',
    postalCode: '',
    addressCountry: 'IN'
  },
  socialMedia: {
    instagram: 'https://www.instagram.com/thebadhees',
    youtube: 'https://www.youtube.com/@TheBadhees',
    whatsapp: 'https://wa.me/918197705438'
  }
};

export const generateOrganizationSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: businessInfo.name,
  description: businessInfo.description,
  url: businessInfo.url,
  logo: businessInfo.logo,
  image: businessInfo.image,
  telephone: businessInfo.telephone,
  email: businessInfo.email,
  address: {
    '@type': 'PostalAddress',
    ...businessInfo.address
  },
  sameAs: Object.values(businessInfo.socialMedia).filter(Boolean)
});

export const generateWebsiteSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'WebSite',
  name: businessInfo.name,
  description: businessInfo.description,
  url: businessInfo.url,
  publisher: {
    '@type': 'Organization',
    name: businessInfo.name,
    logo: businessInfo.logo
  },
  potentialAction: {
    '@type': 'SearchAction',
    target: `${businessInfo.url}/products?search={search_term_string}`,
    'query-input': 'required name=search_term_string'
  }
});

export const generateProductSchema = (product: any) => ({
  '@context': 'https://schema.org',
  '@type': 'Product',
  name: product.name,
  description: product.description,
  image: product.images?.map((img: any) => img.url) || [product.image],
  brand: {
    '@type': 'Brand',
    name: businessInfo.name
  },
  manufacturer: {
    '@type': 'Organization',
    name: businessInfo.name
  },
  offers: {
    '@type': 'Offer',
    price: product.salePrice || product.price,
    priceCurrency: 'INR',
    availability: product.inStock ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
    seller: {
      '@type': 'Organization',
      name: businessInfo.name
    }
  },
  ...(product.reviews && product.reviews.length > 0 && {
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: product.averageRating || 5,
      reviewCount: product.reviews.length
    }
  })
});

export const generateBreadcrumbSchema = (breadcrumbs: Array<{name: string, url: string}>) => ({
  '@context': 'https://schema.org',
  '@type': 'BreadcrumbList',
  itemListElement: breadcrumbs.map((item, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: item.name,
    item: item.url
  }))
});

export const generateLocalBusinessSchema = () => ({
  '@context': 'https://schema.org',
  '@type': 'LocalBusiness',
  '@id': businessInfo.url,
  name: businessInfo.name,
  description: businessInfo.description,
  url: businessInfo.url,
  logo: businessInfo.logo,
  image: businessInfo.image,
  telephone: businessInfo.telephone,
  email: businessInfo.email,
  address: {
    '@type': 'PostalAddress',
    ...businessInfo.address
  },
  sameAs: Object.values(businessInfo.socialMedia).filter(Boolean),
  openingHours: [
    'Mo-Sa 09:00-18:00'
  ],
  priceRange: '₹₹₹'
});

export const generateFAQSchema = (faqs: Array<{question: string, answer: string}>) => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: faqs.map(faq => ({
    '@type': 'Question',
    name: faq.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: faq.answer
    }
  }))
});
