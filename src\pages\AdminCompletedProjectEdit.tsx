
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import AdminSidebar from "@/components/admin/AdminSidebar";
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { ProjectType } from '@/types/project';
import CompletedProjectForm from '@/components/admin/CompletedProjectForm';
import { getCustomProjectById, updateCustomProject, CustomProject } from '@/services/customProjectService';

const AdminCompletedProjectEdit = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const [project, setProject] = useState<CustomProject | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    // Load project from Supabase
    const loadProject = async () => {
      if (!projectId) {
        navigate('/admin/completed-projects');
        return;
      }

      try {
        const foundProject = await getCustomProjectById(projectId);

        if (foundProject) {
          setProject(foundProject);
        } else {
          toast({
            title: "Error",
            description: "Project not found",
            variant: "destructive",
          });
          navigate('/admin/completed-projects');
        }
      } catch (error: any) {
        console.error("Error loading project:", error);
        toast({
          title: "Error",
          description: error.message || "Failed to load project data",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadProject();
  }, [projectId, navigate]);

  const handleSaveProject = async (updatedProject: CustomProject) => {
    if (!projectId) return;

    setIsSaving(true);
    try {
      // Update project in Supabase
      const success = await updateCustomProject(projectId, updatedProject);

      if (success) {
        toast({
          title: "Success",
          description: "Project updated successfully",
        });

        // Navigate back to projects list
        navigate('/admin/completed-projects');
      } else {
        throw new Error('Failed to update project');
      }
    } catch (error: any) {
      console.error("Error saving project:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to save project changes",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    navigate('/admin/completed-projects');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <p>Loading project data...</p>
        </div>
        <Footer />
      </div>
    );
  }

  if (!project) {
    return (
      <div className="min-h-screen">
        <Navbar />
        <div className="pt-28 pb-16 flex items-center justify-center">
          <p>Project not found</p>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Navbar />

      <div className="flex-1 pt-28 pb-16">
        <div className="max-w-[1400px] mx-auto px-4 sm:px-6 lg:px-8 flex flex-col md:flex-row gap-8">
          <div className="md:w-64 flex-shrink-0">
            <AdminSidebar />
          </div>

          <div className="flex-1">
            <div className="flex items-center mb-6">
              <Button
                variant="ghost"
                onClick={() => navigate('/admin/completed-projects')}
                className="mr-4"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Projects
              </Button>
              <h1 className="text-2xl font-bold">Edit Custom Project</h1>
            </div>

            <div className="bg-white shadow-sm rounded-lg p-6">
              <CompletedProjectForm
                initialData={project}
                onSave={handleSaveProject}
                onCancel={handleCancel}
              />
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default AdminCompletedProjectEdit;
