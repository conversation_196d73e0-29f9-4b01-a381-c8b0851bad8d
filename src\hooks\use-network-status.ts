import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';

/**
 * Hook to monitor network status and handle online/offline events
 * @param showToasts Whether to show toast notifications on status changes
 * @returns Object with online status and last updated timestamp
 */
export function useNetworkStatus(showToasts = true) {
  const [isOnline, setIsOnline] = useState<boolean>(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  );
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setLastUpdated(new Date());
      
      if (showToasts) {
        toast({
          title: 'You are back online',
          description: 'Your connection has been restored.',
        });
      }
    };

    const handleOffline = () => {
      setIsOnline(false);
      setLastUpdated(new Date());
      
      if (showToasts) {
        toast({
          title: 'You are offline',
          description: 'Please check your internet connection.',
          variant: 'destructive',
        });
      }
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [showToasts]);

  return { isOnline, lastUpdated };
}

/**
 * Hook to check if a fetch request should be attempted based on network status
 * @param url The URL to check
 * @param options Fetch options
 * @param retryCount Number of retries on failure
 * @param retryDelay Delay between retries in ms
 * @returns Object with fetch function and loading/error states
 */
export function useSafeFetch<T = any>(
  url: string,
  options?: RequestInit,
  retryCount = 3,
  retryDelay = 1000
) {
  const { isOnline } = useNetworkStatus(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);
  const [data, setData] = useState<T | null>(null);

  const fetchWithRetry = async (
    currentUrl: string,
    currentOptions?: RequestInit,
    retriesLeft = retryCount
  ): Promise<T> => {
    try {
      const response = await fetch(currentUrl, currentOptions);
      
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }
      
      return await response.json();
    } catch (err) {
      if (retriesLeft <= 0) {
        throw err;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      
      // Retry the request
      return fetchWithRetry(currentUrl, currentOptions, retriesLeft - 1);
    }
  };

  const executeFetch = async () => {
    if (!isOnline) {
      setError(new Error('You are offline. Please check your internet connection.'));
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await fetchWithRetry(url, options);
      setData(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An unknown error occurred');
      setError(error);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return { executeFetch, isLoading, error, data, isOnline };
}
