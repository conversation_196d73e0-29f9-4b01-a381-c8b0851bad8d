/**
 * Product Mappers
 *
 * This module provides functions to transform product data between different formats.
 */
import { FrontendProduct, SupabaseProduct } from './types';

/**
 * Convert Supabase product to frontend product format
 * @param product The Supabase product to convert
 * @returns Frontend product representation
 */
export const mapSupabaseProductToFrontend = (product: SupabaseProduct): FrontendProduct => {
  // Find primary image
  const primaryImage = product.images?.find(img => img.is_primary)?.image_url || '';

  // Get all image URLs
  const allImages = product.images?.map(img => img.image_url) || [];

  // Specifications are already in the correct format as a Record<string, string>
  const specifications = product.specifications || {};

  // Get rating information - use direct columns if they exist, otherwise default to 0
  let rating = 0;
  let reviewCount = 0;

  // Check if the product has direct rating and review_count columns
  if (product.rating !== undefined && product.rating !== null) {
    rating = parseFloat(String(product.rating)) || 0;
  }

  if (product.review_count !== undefined && product.review_count !== null) {
    reviewCount = parseInt(String(product.review_count)) || 0;
  }

  // For now, we'll use 0 for both rating and reviewCount until the database is properly set up
  // This ensures the app works without errors

  return {
    id: product.id,
    name: product.name,
    description: product.description || '',
    price: product.price,
    salePrice: product.sale_price,
    isSale: product.is_sale,
    isNew: product.is_new,
    isFeatured: product.is_featured || false,
    image: primaryImage,
    images: allImages,
    category: product.category?.name || '',
    status: product.status,
    stock: product.stock,
    sku: product.sku || '',
    specifications: Object.keys(specifications).length > 0 ? specifications : undefined,
    customizationAvailable: product.customization_available || false,
    // Shipping fields
    shippingFeeBangalore: product.shipping_fee_bangalore,
    shippingFeeOutsideBangalore: product.shipping_fee_outside_bangalore,
    freeShippingThreshold: product.free_shipping_threshold,
    shippingNotes: product.shipping_notes,
    rating: rating,
    reviewCount: reviewCount,
    created_at: product.created_at,
    updated_at: product.updated_at
  };
};
