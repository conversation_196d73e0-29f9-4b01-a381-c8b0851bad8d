import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Loader2 } from 'lucide-react';
import { useAuth } from '@/context/SupabaseAuthContext';
import {
  Address,
  getUserAddresses,
  createAddress,
  updateAddress,
  deleteAddress,
  addressToJsonb
} from '@/services/addressService';
import AddressSelector from './AddressSelector';

interface CheckoutAddressStepProps {
  onAddressSelect: (shippingAddress: any, billingAddress: any) => void;
  onContinue: () => void;
  onBack: () => void;
}

const CheckoutAddressStep = ({
  onAddressSelect,
  onContinue,
  onBack,
}: CheckoutAddressStepProps) => {
  const { user } = useAuth();
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedShippingId, setSelectedShippingId] = useState<string | null>(null);
  const [selectedBillingId, setSelectedBillingId] = useState<string | null>(null);
  const [useSameAddress, setUseSameAddress] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch user addresses
  useEffect(() => {
    const fetchAddresses = async () => {
      if (!user?.id) return;

      setIsLoading(true);
      try {
        const userAddresses = await getUserAddresses(user.id);
        setAddresses(userAddresses);

        // Set default addresses if available
        const defaultShipping = userAddresses.find(addr => addr.is_default_shipping);
        const defaultBilling = userAddresses.find(addr => addr.is_default_billing);

        if (defaultShipping) {
          setSelectedShippingId(defaultShipping.id!);
        } else if (userAddresses.length > 0) {
          setSelectedShippingId(userAddresses[0].id!);
        }

        if (defaultBilling) {
          setSelectedBillingId(defaultBilling.id!);
          setUseSameAddress(false);
        } else if (userAddresses.length > 0) {
          setSelectedBillingId(defaultShipping?.id || userAddresses[0].id!);
        }

        // Auto-select the address and continue
        if (userAddresses.length > 0) {
          const shippingAddr = defaultShipping || userAddresses[0];
          let billingAddr;

          if (defaultBilling) {
            billingAddr = defaultBilling;
            setUseSameAddress(false);
          } else {
            billingAddr = shippingAddr;
            setUseSameAddress(true);
          }

          const shippingJsonb = addressToJsonb(shippingAddr);
          const billingJsonb = addressToJsonb(billingAddr);
          onAddressSelect(shippingJsonb, billingJsonb);
        }
      } catch (error) {
        console.error('Error fetching addresses:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchAddresses();
  }, [user]);

  // Handle address creation
  const handleAddressCreate = async (address: Address): Promise<Address | null> => {
    if (!user?.id) return null;

    const newAddress = await createAddress({
      ...address,
      user_id: user.id,
    });

    if (newAddress) {
      setAddresses(prev => [...prev, newAddress]);

      // Auto-select the newly created address
      if (!selectedShippingId) {
        setSelectedShippingId(newAddress.id!);

        if (useSameAddress) {
          setSelectedBillingId(newAddress.id!);

          // Auto-continue with the new address
          const addressJsonb = addressToJsonb(newAddress);
          onAddressSelect(addressJsonb, addressJsonb);
        }
      } else if (!useSameAddress && !selectedBillingId) {
        setSelectedBillingId(newAddress.id!);

        // If shipping address is already selected, auto-continue
        const shippingAddress = addresses.find(addr => addr.id === selectedShippingId);
        if (shippingAddress) {
          const shippingJsonb = addressToJsonb(shippingAddress);
          const billingJsonb = addressToJsonb(newAddress);
          onAddressSelect(shippingJsonb, billingJsonb);
        }
      }
    }

    return newAddress;
  };

  // Handle address update
  const handleAddressUpdate = async (address: Address): Promise<Address | null> => {
    const updatedAddress = await updateAddress(address);

    if (updatedAddress) {
      setAddresses(prev =>
        prev.map(addr => addr.id === updatedAddress.id ? updatedAddress : addr)
      );
    }

    return updatedAddress;
  };

  // Handle address deletion
  const handleAddressDelete = async (addressId: string): Promise<void> => {
    const success = await deleteAddress(addressId);

    if (success) {
      setAddresses(prev => prev.filter(addr => addr.id !== addressId));

      // If deleted address was selected, reset selection
      if (selectedShippingId === addressId) {
        const remainingAddresses = addresses.filter(a => a.id !== addressId);
        setSelectedShippingId(remainingAddresses.length > 0 ? remainingAddresses[0].id! : null);
      }

      if (selectedBillingId === addressId) {
        if (useSameAddress) {
          setSelectedBillingId(selectedShippingId);
        } else {
          const remainingAddresses = addresses.filter(a => a.id !== addressId);
          setSelectedBillingId(remainingAddresses.length > 0 ? remainingAddresses[0].id! : null);
        }
      }
    }
  };

  // Handle shipping address selection
  const handleShippingSelect = (addressId: string) => {
    setSelectedShippingId(addressId);

    // Update billing address if using same address
    if (useSameAddress) {
      setSelectedBillingId(addressId);

      // Auto-select the address and continue
      const shippingAddress = addresses.find(addr => addr.id === addressId);
      if (shippingAddress) {
        const shippingJsonb = addressToJsonb(shippingAddress);
        onAddressSelect(shippingJsonb, shippingJsonb);
      }
    } else if (selectedBillingId) {
      // If billing address is already selected, auto-continue
      const shippingAddress = addresses.find(addr => addr.id === addressId);
      const billingAddress = addresses.find(addr => addr.id === selectedBillingId);

      if (shippingAddress && billingAddress) {
        const shippingJsonb = addressToJsonb(shippingAddress);
        const billingJsonb = addressToJsonb(billingAddress);
        onAddressSelect(shippingJsonb, billingJsonb);
      }
    }
  };

  // Handle billing address selection
  const handleBillingSelect = (addressId: string) => {
    setSelectedBillingId(addressId);

    // Auto-select the address and continue if shipping is already selected
    if (selectedShippingId) {
      const shippingAddress = addresses.find(addr => addr.id === selectedShippingId);
      const billingAddress = addresses.find(addr => addr.id === addressId);

      if (shippingAddress && billingAddress) {
        const shippingJsonb = addressToJsonb(shippingAddress);
        const billingJsonb = addressToJsonb(billingAddress);
        onAddressSelect(shippingJsonb, billingJsonb);
      }
    }
  };

  // Handle "use same address" toggle
  const handleUseSameAddressChange = (checked: boolean) => {
    setUseSameAddress(checked);

    if (checked && selectedShippingId) {
      setSelectedBillingId(selectedShippingId);

      // Auto-select the address and continue
      const shippingAddress = addresses.find(addr => addr.id === selectedShippingId);
      if (shippingAddress) {
        const shippingJsonb = addressToJsonb(shippingAddress);
        onAddressSelect(shippingJsonb, shippingJsonb);
      }
    } else if (!checked && selectedShippingId && selectedBillingId) {
      // If unchecked but both addresses are selected, auto-continue
      const shippingAddress = addresses.find(addr => addr.id === selectedShippingId);
      const billingAddress = addresses.find(addr => addr.id === selectedBillingId);

      if (shippingAddress && billingAddress) {
        const shippingJsonb = addressToJsonb(shippingAddress);
        const billingJsonb = addressToJsonb(billingAddress);
        onAddressSelect(shippingJsonb, billingJsonb);
      }
    }
  };

  // Handle continue button click
  const handleContinue = () => {
    if (!selectedShippingId || (!useSameAddress && !selectedBillingId)) {
      return;
    }

    setIsSubmitting(true);

    try {
      const shippingAddress = addresses.find(addr => addr.id === selectedShippingId);
      const billingAddress = useSameAddress
        ? shippingAddress
        : addresses.find(addr => addr.id === selectedBillingId);

      if (shippingAddress && billingAddress) {
        // Convert addresses to JSONB format for order
        const shippingJsonb = addressToJsonb(shippingAddress);
        const billingJsonb = addressToJsonb(billingAddress);

        // Pass addresses to parent component
        onAddressSelect(shippingJsonb, billingJsonb);

        // Continue to next step
        onContinue();
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-6 w-6 animate-spin text-badhees-600" />
        <span className="ml-3 text-badhees-600">Loading addresses...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <AddressSelector
        title="Shipping Address"
        addresses={addresses}
        selectedAddressId={selectedShippingId}
        onAddressSelect={handleShippingSelect}
        onAddressCreate={handleAddressCreate}
        onAddressUpdate={handleAddressUpdate}
        onAddressDelete={handleAddressDelete}
      />

      <button
        type="button"
        onClick={() => handleUseSameAddressChange(!useSameAddress)}
        className={`flex items-center justify-between w-full py-3 px-4 rounded-md cursor-pointer ${useSameAddress ? 'bg-badhees-50 border border-badhees-600' : 'bg-gray-50 border border-gray-200'}`}
      >
        <span className="text-sm font-medium">Use same address for billing</span>
        <div className={`w-10 h-5 rounded-full relative ${useSameAddress ? 'bg-badhees-600' : 'bg-gray-300'}`}>
          <div
            className={`absolute top-0.5 left-0.5 w-4 h-4 rounded-full bg-white transition-transform ${useSameAddress ? 'transform translate-x-5' : ''}`}
          ></div>
        </div>
      </button>

      {!useSameAddress && (
        <div className="mt-2">
          <AddressSelector
            title="Billing Address"
            addresses={addresses}
            selectedAddressId={selectedBillingId}
            onAddressSelect={handleBillingSelect}
            onAddressCreate={handleAddressCreate}
            onAddressUpdate={handleAddressUpdate}
            onAddressDelete={handleAddressDelete}
          />
        </div>
      )}
    </div>
  );
};

export default CheckoutAddressStep;
