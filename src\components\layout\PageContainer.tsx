
import React from "react";
import { cn } from "@/lib/utils";

interface PageContainerProps {
  children: React.ReactNode;
  className?: string;
  fullWidth?: boolean;
  noPadding?: boolean;
}

/**
 * A responsive container for page content with consistent padding and max-width
 */
const PageContainer: React.FC<PageContainerProps> = ({
  children,
  className,
  fullWidth = false,
  noPadding = false
}) => {
  return (
    <div 
      className={cn(
        "w-full mx-auto",
        !noPadding && "px-4 sm:px-6 md:px-8", 
        !fullWidth && "max-w-[1400px]",
        className
      )}
    >
      {children}
    </div>
  );
};

export default PageContainer;
