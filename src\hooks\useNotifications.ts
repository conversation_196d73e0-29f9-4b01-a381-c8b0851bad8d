/**
 * Notification Hooks
 * 
 * React Query hooks for managing admin notifications with real-time updates.
 */
import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';
import { useEffect } from 'react';
import {
  getAllNotificationCounts,
  subscribeToNotifications,
  markOrdersAsViewed,
  markCustomizationRequestsAsViewed,
  markContactMessagesAsViewed,
  markConsultationRequestsAsViewed,
  NotificationCounts
} from '@/services/notificationService';

// Query keys for notifications
export const notificationKeys = {
  all: ['notifications'] as const,
  counts: () => [...notificationKeys.all, 'counts'] as const,
};

/**
 * Hook to get all notification counts
 */
export function useNotificationCounts() {
  return useQuery({
    queryKey: notificationKeys.counts(),
    queryFn: getAllNotificationCounts,
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook to subscribe to real-time notification updates
 */
export function useRealtimeNotifications() {
  const queryClient = useQueryClient();

  useEffect(() => {
    const unsubscribe = subscribeToNotifications((counts: NotificationCounts) => {
      // Update the query cache with new counts
      queryClient.setQueryData(notificationKeys.counts(), counts);
    });

    return unsubscribe;
  }, [queryClient]);
}

/**
 * Hook to mark orders as viewed
 */
export function useMarkOrdersAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markOrdersAsViewed,
    onSuccess: () => {
      // Invalidate notification counts to refresh
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Hook to mark customization requests as viewed
 */
export function useMarkCustomizationRequestsAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markCustomizationRequestsAsViewed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Hook to mark contact messages as viewed
 */
export function useMarkContactMessagesAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markContactMessagesAsViewed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Hook to mark consultation requests as viewed
 */
export function useMarkConsultationRequestsAsViewed() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markConsultationRequestsAsViewed,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationKeys.counts() });
    },
  });
}

/**
 * Combined hook that provides all notification functionality
 */
export function useAdminNotifications() {
  const { data: counts, isLoading, error } = useNotificationCounts();
  
  // Subscribe to real-time updates
  useRealtimeNotifications();

  const markOrdersViewed = useMarkOrdersAsViewed();
  const markCustomizationViewed = useMarkCustomizationRequestsAsViewed();
  const markContactViewed = useMarkContactMessagesAsViewed();
  const markConsultationViewed = useMarkConsultationRequestsAsViewed();

  return {
    counts: counts || {
      orders: 0,
      customizationRequests: 0,
      contactMessages: 0,
      consultationRequests: 0
    },
    isLoading,
    error,
    markOrdersViewed: markOrdersViewed.mutate,
    markCustomizationViewed: markCustomizationViewed.mutate,
    markContactViewed: markContactViewed.mutate,
    markConsultationViewed: markConsultationViewed.mutate,
    isMarkingViewed: 
      markOrdersViewed.isPending ||
      markCustomizationViewed.isPending ||
      markContactViewed.isPending ||
      markConsultationViewed.isPending
  };
}
