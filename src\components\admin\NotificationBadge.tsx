/**
 * Notification Badge Component
 * 
 * Displays a red notification dot with count for admin notifications.
 */
import React from 'react';
import { cn } from '@/lib/utils';

interface NotificationBadgeProps {
  count: number;
  className?: string;
  showZero?: boolean;
  maxCount?: number;
}

const NotificationBadge: React.FC<NotificationBadgeProps> = ({
  count,
  className,
  showZero = false,
  maxCount = 99
}) => {
  // Don't show badge if count is 0 and show<PERSON><PERSON> is false
  if (count === 0 && !showZero) {
    return null;
  }

  // Format count display
  const displayCount = count > maxCount ? `${maxCount}+` : count.toString();

  return (
    <div
      className={cn(
        "absolute -top-1 -right-1 min-w-[18px] h-[18px] bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center px-1",
        "animate-pulse shadow-lg border-2 border-white",
        className
      )}
    >
      {displayCount}
    </div>
  );
};

export default NotificationBadge;
