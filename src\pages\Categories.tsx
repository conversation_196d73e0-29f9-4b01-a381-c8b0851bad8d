
import React, { useEffect, useState } from 'react';
import ResponsiveLayout from '@/components/layout/ResponsiveLayout';
import PageContainer from '@/components/layout/PageContainer';
import ResponsiveGrid from '@/components/layout/ResponsiveGrid';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { getInitialProducts } from '@/services/editorProductsService';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useIsMobile } from '@/hooks/use-responsive';
import UnifiedCategoryCard from '@/components/shop/UnifiedCategoryCard';

interface Category {
  id: string;
  name: string;
  description: string;
  image: string;
  itemCount: number;
}

const Categories = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useIsMobile();

  useEffect(() => {
    window.scrollTo(0, 0);

    const fetchProducts = async () => {
      setIsLoading(true);
      try {
        const products = await getInitialProducts();

        const categoryMap = new Map<string, {count: number, image: string}>();
        products.forEach(product => {
          if (!product.category) return;

          const category = product.category;
          if (!categoryMap.has(category)) {
            categoryMap.set(category, {count: 0, image: product.image});
          }
          categoryMap.get(category)!.count++;

          if (!categoryMap.get(category)!.image || categoryMap.get(category)!.image.includes('placeholder')) {
            categoryMap.set(category, {
              ...categoryMap.get(category)!,
              image: product.image
            });
          }
        });

        const categoriesData: Category[] = Array.from(categoryMap.entries()).map(([name, data]) => ({
          id: name.toLowerCase().replace(/\s+/g, '-'),
          name,
          description: `Explore our collection of high-quality ${name.toLowerCase()} pieces, designed for style and comfort.`,
          image: data.image,
          itemCount: data.count
        }));

        setCategories(categoriesData);
      } catch (error) {
        console.error('Error fetching products for categories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, []);

  return (
    <ResponsiveLayout>
      <div className="bg-badhees-50 py-8 md:py-16">
        <PageContainer>
          <Breadcrumb className="mb-4 md:mb-6">
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/">Home</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Categories</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-badhees-800 mb-3 md:mb-6 animate-fade-in">
              Browse Our Categories
            </h1>
            <p className="text-base md:text-lg text-badhees-600 animate-fade-in animate-delay-100">
              Explore our furniture by category to find exactly what you're looking for.
            </p>
          </div>
        </PageContainer>
      </div>



      <div className="py-8 md:py-16">
        <PageContainer>
          {isLoading ? (
            <div className="flex justify-center items-center py-20">
              <Loader2 className="h-8 w-8 animate-spin text-badhees-accent" />
              <span className="ml-2 text-badhees-600">Loading categories...</span>
            </div>
          ) : categories.length > 0 ? (
            <ResponsiveGrid cols={{ default: 1, md: 2 }} gap="gap-6 md:gap-8">
              {categories.map((category, index) => (
                <UnifiedCategoryCard
                  key={category.id}
                  category={category}
                  index={index}
                />
              ))}
            </ResponsiveGrid>
          ) : (
            <div className="text-center py-8 md:py-12">
              <h2 className="text-xl font-medium text-badhees-700 mb-4">No categories found</h2>
              <p className="text-badhees-600 mb-6">There are no product categories available at the moment.</p>
              <Button onClick={() => window.location.reload()}>
                Refresh Page
              </Button>
            </div>
          )}
        </PageContainer>
      </div>
    </ResponsiveLayout>
  );
};

export default Categories;
